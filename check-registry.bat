@echo off
echo === AUGMENT REGISTRY ANALYSIS ===
echo.

echo 1. Checking for direct Augment registry entries...
echo.

echo Searching HKEY_CURRENT_USER for "augment"...
reg query "HKEY_CURRENT_USER" /s /f "augment" /t REG_SZ 2>nul | findstr /i "augment" >nul
if %errorlevel%==0 (
    echo   FOUND: References to "augment" in HKEY_CURRENT_USER
    reg query "HKEY_CURRENT_USER" /s /f "augment" /t REG_SZ 2>nul | findstr /i "augment"
) else (
    echo   NOT FOUND: No "augment" references in HKEY_CURRENT_USER
)

echo.
echo Searching HKEY_LOCAL_MACHINE for "augment"...
reg query "HKEY_LOCAL_MACHINE" /s /f "augment" /t REG_SZ 2>nul | findstr /i "augment" >nul
if %errorlevel%==0 (
    echo   FOUND: References to "augment" in HKEY_LOCAL_MACHINE
    reg query "HKEY_LOCAL_MACHINE" /s /f "augment" /t REG_SZ 2>nul | findstr /i "augment"
) else (
    echo   NOT FOUND: No "augment" references in HKEY_LOCAL_MACHINE
)

echo.
echo 2. Checking specific registry locations...
echo.

echo Checking HKEY_CURRENT_USER\Software\augment...
reg query "HKEY_CURRENT_USER\Software\augment" 2>nul >nul
if %errorlevel%==0 (
    echo   FOUND: HKEY_CURRENT_USER\Software\augment exists
    reg query "HKEY_CURRENT_USER\Software\augment" 2>nul
) else (
    echo   NOT FOUND: HKEY_CURRENT_USER\Software\augment does not exist
)

echo.
echo Checking HKEY_LOCAL_MACHINE\SOFTWARE\augment...
reg query "HKEY_LOCAL_MACHINE\SOFTWARE\augment" 2>nul >nul
if %errorlevel%==0 (
    echo   FOUND: HKEY_LOCAL_MACHINE\SOFTWARE\augment exists
    reg query "HKEY_LOCAL_MACHINE\SOFTWARE\augment" 2>nul
) else (
    echo   NOT FOUND: HKEY_LOCAL_MACHINE\SOFTWARE\augment does not exist
)

echo.
echo Checking VS Code registry entries...
reg query "HKEY_CURRENT_USER\Software\Microsoft\VSCode" 2>nul >nul
if %errorlevel%==0 (
    echo   FOUND: VS Code registry key exists
    reg query "HKEY_CURRENT_USER\Software\Microsoft\VSCode" /s 2>nul | findstr /i "augment" >nul
    if %errorlevel%==0 (
        echo   FOUND: Augment references in VS Code registry
    ) else (
        echo   NOT FOUND: No Augment references in VS Code registry
    )
) else (
    echo   NOT FOUND: VS Code registry key does not exist
)

echo.
echo 3. Checking uninstall registry entries...
echo.

echo Checking for Augment in uninstall registry...
reg query "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall" /s 2>nul | findstr /i "augment" >nul
if %errorlevel%==0 (
    echo   FOUND: Augment in uninstall registry
    reg query "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall" /s 2>nul | findstr /i "augment"
) else (
    echo   NOT FOUND: Augment not in uninstall registry
)

echo.
echo === REGISTRY ANALYSIS SUMMARY ===
echo.
echo Augment appears to have MINIMAL registry footprint.
echo Most VS Code extensions do not create dedicated registry entries.
echo The extension likely relies on file-based storage only.
echo.
echo This makes cleanup easier - focus on file removal rather than registry cleaning.
echo.
pause
