# Augment VS Code Extension Complete Cleanup Tool

This tool completely removes all traces of the Augment VS Code extension and resets detection mechanisms to allow fresh installations.

## 🎯 What This Tool Does

### Files and Directories Removed:
1. **Extension Files**
   - `%USERPROFILE%\.vscode\extensions\augment.vscode-augment-*`
   - `%APPDATA%\Code\CachedExtensionVSIXs\augment.vscode-augment-*`
   - `%APPDATA%\Code\CachedExtensionVSIXs\.trash\augment.vscode-augment-*`

2. **License and Trial Files**
   - `%APPDATA%\Local\Code\User\augment-license.json`
   - `%APPDATA%\Local\Code\User\augment-trial.json`

3. **Global Storage**
   - `%APPDATA%\Code\User\globalStorage\augment.vscode-augment\`

4. **Trial Reset Backups**
   - `%TEMP%\TrialResetBackup_*\` (containing Augment files)

5. **Workspace Storage**
   - Any workspace storage containing Augment data

6. **Recent File References**
   - `%APPDATA%\Microsoft\Windows\Recent\augment.lnk`

### Detection Mechanisms Reset:
1. **VS Code Machine Identification**
   - Generates new `telemetry.machineId`
   - Generates new `telemetry.sqmId`
   - Generates new `telemetry.devDeviceId`

## 🚀 How to Use

### Method 1: Using the Batch File (Recommended)
1. Double-click `run-augment-cleanup.bat`
2. Choose your cleanup option:
   - **Quick cleanup**: Fast removal without backup
   - **Safe cleanup**: Removal with backup of deleted files
   - **Verbose cleanup**: Detailed output with backup
   - **Force cleanup**: No prompts, immediate removal

### Method 2: Using PowerShell Directly
```powershell
# Basic cleanup
.\augment-cleanup.ps1

# With backup
.\augment-cleanup.ps1 -Backup

# Force cleanup (no prompts)
.\augment-cleanup.ps1 -Force

# Verbose output with backup
.\augment-cleanup.ps1 -Backup -Verbose
```

## 🔧 Command Line Options

### PowerShell Script Parameters:
- `-Force`: Skip confirmation prompts
- `-Backup`: Create backup of deleted files before removal
- `-Verbose`: Show detailed output during cleanup

## 🛡️ Safety Features

1. **Backup Option**: Creates timestamped backups in `%TEMP%\AugmentCleanupBackup_*`
2. **Process Management**: Safely stops VS Code processes before cleanup
3. **Error Handling**: Continues cleanup even if some operations fail
4. **Confirmation Prompts**: Asks for confirmation unless `-Force` is used

## 📋 What Happens During Cleanup

1. **Process Termination**: Stops all VS Code processes
2. **Extension Removal**: Deletes all Augment extension files and directories
3. **License Cleanup**: Removes trial and license tracking files
4. **Storage Cleanup**: Clears global and workspace storage
5. **ID Reset**: Generates new machine identification values
6. **Cache Cleanup**: Removes cached extension files and backups
7. **Reference Cleanup**: Removes recent file links and shortcuts

## 🔍 How Augment Detection Works (Analysis Results)

Based on the analysis of Augment extension files, here's how it tracks users:

### 1. License and Trial Tracking
- **Files**: `augment-license.json`, `augment-trial.json`
- **Content**: Trial days, expiry dates, usage counts, activation status
- **Location**: `%APPDATA%\Local\Code\User\`

### 2. Machine Identification
- **VS Code Machine ID**: `telemetry.machineId` in storage.json
- **System UUID**: Hardware-based system identifier
- **Device ID**: `telemetry.devDeviceId` for device tracking

### 3. Global Storage
- **Extension State**: Stored in VS Code's global storage
- **Settings**: Terminal settings and configuration data
- **Usage Data**: Extension usage patterns and statistics

### 4. Trial Reset Detection
- **Backup Creation**: Creates backups when trial is reset
- **File Timestamps**: Tracks installation and first run dates
- **Persistent Flags**: Marks accounts as having used trials

## ⚠️ Important Notes

1. **Close VS Code**: The script will automatically close VS Code, but save your work first
2. **Administrator Rights**: Some operations may require administrator privileges
3. **Backup Recommendation**: Use the `-Backup` option for safety
4. **System Restart**: Consider restarting your computer after cleanup for complete effect

## 🔄 After Cleanup

1. **Restart VS Code**: Launch VS Code to verify cleanup
2. **Fresh Installation**: You can now install Augment as a new user
3. **New Trial**: Trial restrictions should be reset
4. **Clean State**: All previous usage data has been cleared

## 🐛 Troubleshooting

### If cleanup fails:
1. Run as Administrator
2. Manually close all VS Code processes
3. Check if files are in use by other applications
4. Use the verbose option to see detailed error messages

### If Augment still detects previous usage:
1. Clear browser cache (if using web features)
2. Restart your computer
3. Check for additional Augment files in other locations
4. Verify VS Code machine ID was reset in storage.json

## 📝 Technical Details

The cleanup script targets all known Augment storage locations based on reverse engineering of the extension. It resets machine identification by generating new UUIDs for VS Code's telemetry system, which Augment uses for user tracking.

## ⚖️ Legal Notice

This tool is for educational and legitimate use only. Use responsibly and in accordance with software licensing terms.
