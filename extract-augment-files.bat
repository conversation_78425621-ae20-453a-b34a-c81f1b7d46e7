@echo off
echo ========================================
echo AUGMENT EXTENSION FILE EXTRACTOR
echo ========================================
echo.
echo This script will locate and copy Augment extension files for analysis.
echo.

set ANALYSIS_DIR=augment-analysis
set FOUND=0

echo Creating analysis directory...
if exist "%ANALYSIS_DIR%" rmdir /s /q "%ANALYSIS_DIR%"
mkdir "%ANALYSIS_DIR%"

echo.
echo === SEARCHING FOR AUGMENT EXTENSION ===
echo.

echo 1. Checking VS Code extensions directory...
if exist "%USERPROFILE%\.vscode\extensions\" (
    for /d %%i in ("%USERPROFILE%\.vscode\extensions\*augment*") do (
        echo   FOUND EXTENSION: %%i
        set FOUND=1
        set EXTENSION_PATH=%%i
        
        echo   Copying key files...
        
        REM Copy package.json
        if exist "%%i\package.json" (
            copy "%%i\package.json" "%ANALYSIS_DIR%\package.json" >nul
            echo     ✓ package.json copied
        ) else (
            echo     ✗ package.json not found
        )
        
        REM Copy main extension file
        if exist "%%i\out\extension.js" (
            copy "%%i\out\extension.js" "%ANALYSIS_DIR%\extension.js" >nul
            echo     ✓ extension.js copied from out\
        ) else if exist "%%i\extension.js" (
            copy "%%i\extension.js" "%ANALYSIS_DIR%\extension.js" >nul
            echo     ✓ extension.js copied from root
        ) else if exist "%%i\dist\extension.js" (
            copy "%%i\dist\extension.js" "%ANALYSIS_DIR%\extension.js" >nul
            echo     ✓ extension.js copied from dist\
        ) else (
            echo     ✗ extension.js not found
        )
        
        REM Copy source map if available
        if exist "%%i\out\extension.js.map" (
            copy "%%i\out\extension.js.map" "%ANALYSIS_DIR%\extension.js.map" >nul
            echo     ✓ extension.js.map copied
        )
        
        REM Copy README if available
        if exist "%%i\README.md" (
            copy "%%i\README.md" "%ANALYSIS_DIR%\README.md" >nul
            echo     ✓ README.md copied
        )
        
        REM Copy CHANGELOG if available
        if exist "%%i\CHANGELOG.md" (
            copy "%%i\CHANGELOG.md" "%ANALYSIS_DIR%\CHANGELOG.md" >nul
            echo     ✓ CHANGELOG.md copied
        )
        
        echo.
    )
) else (
    echo   VS Code extensions directory not found
)

echo 2. Checking for cached extension files...
if exist "%APPDATA%\Code\CachedExtensionVSIXs\" (
    for %%i in ("%APPDATA%\Code\CachedExtensionVSIXs\*augment*") do (
        echo   FOUND CACHE: %%i
        set FOUND=1
    )
) else (
    echo   Cache directory not found
)

echo.
echo === ANALYSIS RESULTS ===
echo.

if %FOUND%==1 (
    echo ✓ SUCCESS: Augment extension files found and copied!
    echo.
    echo Files copied to: %ANALYSIS_DIR%\
    echo.
    
    if exist "%ANALYSIS_DIR%\extension.js" (
        for %%A in ("%ANALYSIS_DIR%\extension.js") do (
            echo Extension file size: %%~zA bytes
        )
        
        echo.
        echo === QUICK ANALYSIS ===
        echo.
        
        echo Searching for trial patterns...
        findstr /i "trial expire remaining days usage limit" "%ANALYSIS_DIR%\extension.js" >nul
        if %errorlevel%==0 (
            echo   ✓ Trial system patterns found
        ) else (
            echo   ✗ No trial patterns found
        )
        
        echo Searching for license patterns...
        findstr /i "license activation subscription premium" "%ANALYSIS_DIR%\extension.js" >nul
        if %errorlevel%==0 (
            echo   ✓ License system patterns found
        ) else (
            echo   ✗ No license patterns found
        )
        
        echo Searching for machine ID patterns...
        findstr /i "machineId telemetry fingerprint device" "%ANALYSIS_DIR%\extension.js" >nul
        if %errorlevel%==0 (
            echo   ✓ Machine identification patterns found
        ) else (
            echo   ✗ No machine ID patterns found
        )
        
        echo Searching for file operations...
        findstr /i "fs.readFile fs.writeFile fs.exists" "%ANALYSIS_DIR%\extension.js" >nul
        if %errorlevel%==0 (
            echo   ✓ File operation patterns found
        ) else (
            echo   ✗ No file operation patterns found
        )
        
        echo.
        echo === NEXT STEPS ===
        echo.
        echo 1. Open web-decompiler.html in your browser
        echo 2. Drag and drop the extension.js file for detailed analysis
        echo 3. Or run: python python-decompiler.py
        echo 4. Or run: node manual-decompiler.js
        echo 5. Or run: powershell -ExecutionPolicy Bypass -File augment-decompiler.ps1
        echo.
        echo The extension.js file is ready for decompilation!
        
    ) else (
        echo ✗ WARNING: extension.js file not found
        echo   The extension may use a different file structure
        echo   Check the %ANALYSIS_DIR% directory for available files
    )
    
) else (
    echo ✗ ERROR: No Augment extension files found!
    echo.
    echo Possible reasons:
    echo 1. Augment extension is not installed
    echo 2. Extension is installed in a different location
    echo 3. Extension uses a different naming convention
    echo.
    echo Try searching manually:
    echo   dir /s /b "%USERPROFILE%\*augment*"
)

echo.
echo === EXTRACTION COMPLETE ===
pause
