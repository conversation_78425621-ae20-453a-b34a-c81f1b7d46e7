# Augment Cleanup Verification Script
# This script checks if all Augment traces have been successfully removed

Write-Host "=== Augment Cleanup Verification ===" -ForegroundColor Cyan
Write-Host "Checking for remaining Augment traces..." -ForegroundColor Yellow
Write-Host ""

$foundIssues = 0

# Function to check if path exists and report
function Test-AugmentPath {
    param(
        [string]$Path,
        [string]$Description,
        [switch]$IsPattern
    )
    
    if ($IsPattern) {
        $items = Get-ChildItem -Path (Split-Path $Path -Parent) -Filter (Split-Path $Path -Leaf) -ErrorAction SilentlyContinue
        if ($items.Count -gt 0) {
            Write-Host "❌ FOUND: $Description" -ForegroundColor Red
            foreach ($item in $items) {
                Write-Host "   $($item.FullName)" -ForegroundColor Gray
            }
            return $items.Count
        }
    } else {
        if (Test-Path $Path) {
            Write-Host "❌ FOUND: $Description" -ForegroundColor Red
            Write-Host "   $Path" -ForegroundColor Gray
            return 1
        }
    }
    
    Write-Host "✅ CLEAN: $Description" -ForegroundColor Green
    return 0
}

Write-Host "1. Checking extension directories..." -ForegroundColor Cyan
$foundIssues += Test-AugmentPath -Path "$env:USERPROFILE\.vscode\extensions\augment.vscode-augment-*" -Description "User extension directory" -IsPattern
$foundIssues += Test-AugmentPath -Path "$env:APPDATA\Code\CachedExtensionVSIXs\augment.vscode-augment-*" -Description "Cached extension VSIX" -IsPattern
$foundIssues += Test-AugmentPath -Path "$env:APPDATA\Code\CachedExtensionVSIXs\.trash\augment.vscode-augment-*" -Description "Trashed extension VSIX" -IsPattern

Write-Host "`n2. Checking license and trial files..." -ForegroundColor Cyan
$foundIssues += Test-AugmentPath -Path "$env:APPDATA\Local\Code\User\augment-license.json" -Description "License file"
$foundIssues += Test-AugmentPath -Path "$env:APPDATA\Local\Code\User\augment-trial.json" -Description "Trial file"

Write-Host "`n3. Checking global storage..." -ForegroundColor Cyan
$foundIssues += Test-AugmentPath -Path "$env:APPDATA\Code\User\globalStorage\augment.vscode-augment" -Description "Global storage directory"

Write-Host "`n4. Checking for trial reset backups..." -ForegroundColor Cyan
$tempBackups = Get-ChildItem -Path "$env:TEMP" -Filter "TrialResetBackup_*" -Directory -ErrorAction SilentlyContinue
$backupCount = 0
foreach ($backup in $tempBackups) {
    $augmentFiles = Get-ChildItem -Path $backup.FullName -Recurse -Filter "*augment*" -ErrorAction SilentlyContinue
    if ($augmentFiles.Count -gt 0) {
        Write-Host "❌ FOUND: Trial reset backup with Augment files" -ForegroundColor Red
        Write-Host "   $($backup.FullName)" -ForegroundColor Gray
        $backupCount++
    }
}
if ($backupCount -eq 0) {
    Write-Host "✅ CLEAN: No trial reset backups with Augment files" -ForegroundColor Green
} else {
    $foundIssues += $backupCount
}

Write-Host "`n5. Checking workspace storage..." -ForegroundColor Cyan
$workspaceStoragePath = "$env:APPDATA\Code\User\workspaceStorage"
$workspaceCount = 0
if (Test-Path $workspaceStoragePath) {
    $workspaces = Get-ChildItem -Path $workspaceStoragePath -Directory -ErrorAction SilentlyContinue
    foreach ($workspace in $workspaces) {
        $augmentFiles = Get-ChildItem -Path $workspace.FullName -Recurse -Filter "*augment*" -ErrorAction SilentlyContinue
        if ($augmentFiles.Count -gt 0) {
            Write-Host "❌ FOUND: Workspace storage with Augment files" -ForegroundColor Red
            Write-Host "   $($workspace.FullName)" -ForegroundColor Gray
            $workspaceCount++
        }
    }
}
if ($workspaceCount -eq 0) {
    Write-Host "✅ CLEAN: No workspace storage with Augment files" -ForegroundColor Green
} else {
    $foundIssues += $workspaceCount
}

Write-Host "`n6. Checking recent file references..." -ForegroundColor Cyan
$foundIssues += Test-AugmentPath -Path "$env:APPDATA\Microsoft\Windows\Recent\augment.lnk" -Description "Recent file link"

Write-Host "`n7. Checking machine identification reset..." -ForegroundColor Cyan
$storageJsonPath = "$env:APPDATA\Code\User\globalStorage\storage.json"
if (Test-Path $storageJsonPath) {
    try {
        $storageContent = Get-Content $storageJsonPath -Raw | ConvertFrom-Json
        $machineId = $storageContent."telemetry.machineId"
        $sqmId = $storageContent."telemetry.sqmId"
        $deviceId = $storageContent."telemetry.devDeviceId"
        
        if ($machineId -and $sqmId -and $deviceId) {
            Write-Host "✅ CLEAN: Machine identification present (IDs should be new)" -ForegroundColor Green
            Write-Host "   Machine ID: $($machineId.Substring(0,8))..." -ForegroundColor Gray
            Write-Host "   SQM ID: $sqmId" -ForegroundColor Gray
            Write-Host "   Device ID: $($deviceId.Substring(0,8))..." -ForegroundColor Gray
        } else {
            Write-Host "❌ ISSUE: Machine identification incomplete" -ForegroundColor Yellow
            $foundIssues++
        }
    }
    catch {
        Write-Host "❌ ERROR: Could not read storage.json" -ForegroundColor Red
        $foundIssues++
    }
} else {
    Write-Host "❌ MISSING: VS Code storage.json not found" -ForegroundColor Yellow
}

Write-Host "`n" + "="*50 -ForegroundColor Cyan
Write-Host "VERIFICATION RESULTS" -ForegroundColor Cyan
Write-Host "="*50 -ForegroundColor Cyan

if ($foundIssues -eq 0) {
    Write-Host "🎉 SUCCESS: All Augment traces have been removed!" -ForegroundColor Green
    Write-Host "The cleanup was successful. You can now:" -ForegroundColor Green
    Write-Host "  • Install Augment as a new user" -ForegroundColor White
    Write-Host "  • Start a fresh trial period" -ForegroundColor White
    Write-Host "  • Use the extension without previous restrictions" -ForegroundColor White
} else {
    Write-Host "⚠️  ISSUES FOUND: $foundIssues remaining traces detected" -ForegroundColor Yellow
    Write-Host "Some Augment files or traces were not removed." -ForegroundColor Yellow
    Write-Host "You may need to:" -ForegroundColor Yellow
    Write-Host "  • Run the cleanup script again with administrator privileges" -ForegroundColor White
    Write-Host "  • Manually remove the remaining files" -ForegroundColor White
    Write-Host "  • Restart your computer and run verification again" -ForegroundColor White
}

Write-Host "`nPress any key to exit..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
