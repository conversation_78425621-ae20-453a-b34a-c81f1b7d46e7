# Locate and Copy Augment Extension for Decompilation
Write-Host "=== LOCATING AUGMENT EXTENSION ===" -ForegroundColor Cyan

# Common VS Code extension paths
$extensionPaths = @(
    "$env:USERPROFILE\.vscode\extensions",
    "$env:USERPROFILE\.vscode-insiders\extensions",
    "$env:APPDATA\Code\User\extensions",
    "$env:LOCALAPPDATA\Programs\Microsoft VS Code\resources\app\extensions"
)

$foundExtensions = @()

foreach ($basePath in $extensionPaths) {
    if (Test-Path $basePath) {
        Write-Host "Checking: $basePath" -ForegroundColor Yellow
        
        try {
            $augmentDirs = Get-ChildItem -Path $basePath -Directory -Filter "*augment*" -ErrorAction SilentlyContinue
            foreach ($dir in $augmentDirs) {
                Write-Host "  FOUND: $($dir.FullName)" -ForegroundColor Green
                $foundExtensions += $dir.FullName
            }
        }
        catch {
            Write-Host "  Could not access: $basePath" -ForegroundColor Red
        }
    }
}

if ($foundExtensions.Count -eq 0) {
    Write-Host "No Augment extensions found!" -ForegroundColor Red
    Write-Host "Checking alternative locations..." -ForegroundColor Yellow
    
    # Check for portable VS Code installations
    $portablePaths = @(
        "C:\VSCode\data\extensions",
        "D:\VSCode\data\extensions",
        "$env:USERPROFILE\VSCode\data\extensions"
    )
    
    foreach ($path in $portablePaths) {
        if (Test-Path $path) {
            $augmentDirs = Get-ChildItem -Path $path -Directory -Filter "*augment*" -ErrorAction SilentlyContinue
            foreach ($dir in $augmentDirs) {
                Write-Host "  FOUND PORTABLE: $($dir.FullName)" -ForegroundColor Green
                $foundExtensions += $dir.FullName
            }
        }
    }
}

if ($foundExtensions.Count -eq 0) {
    Write-Host "ERROR: No Augment extensions found in any location!" -ForegroundColor Red
    exit 1
}

# Copy the most recent extension for analysis
$latestExtension = $foundExtensions | Sort-Object | Select-Object -Last 1
Write-Host "`nUsing extension: $latestExtension" -ForegroundColor Cyan

# Create analysis directory
$analysisDir = ".\augment-analysis"
if (Test-Path $analysisDir) {
    Remove-Item $analysisDir -Recurse -Force
}
New-Item -ItemType Directory -Path $analysisDir -Force | Out-Null

Write-Host "Copying extension files for analysis..." -ForegroundColor Yellow

try {
    # Copy key files for analysis
    $keyFiles = @(
        "package.json",
        "out\extension.js",
        "out\extension.js.map",
        "README.md",
        "CHANGELOG.md"
    )
    
    foreach ($file in $keyFiles) {
        $sourcePath = Join-Path $latestExtension $file
        $destPath = Join-Path $analysisDir $file
        
        if (Test-Path $sourcePath) {
            $destDir = Split-Path $destPath -Parent
            if (-not (Test-Path $destDir)) {
                New-Item -ItemType Directory -Path $destDir -Force | Out-Null
            }
            
            Copy-Item $sourcePath $destPath -Force
            Write-Host "  Copied: $file" -ForegroundColor Green
        } else {
            Write-Host "  Missing: $file" -ForegroundColor Red
        }
    }
    
    Write-Host "`nExtension files copied to: $analysisDir" -ForegroundColor Green
    Write-Host "Ready for decompilation!" -ForegroundColor Green
    
} catch {
    Write-Host "Error copying files: $_" -ForegroundColor Red
    exit 1
}
