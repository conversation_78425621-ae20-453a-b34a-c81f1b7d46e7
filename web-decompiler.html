<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Augment Extension Decompiler</title>
    <style>
        body {
            font-family: 'Consolas', 'Monaco', monospace;
            background: #1e1e1e;
            color: #d4d4d4;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .upload-area {
            border: 2px dashed #569cd6;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin-bottom: 30px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .upload-area:hover {
            border-color: #4fc3f7;
            background: rgba(79, 195, 247, 0.1);
        }
        .upload-area.dragover {
            border-color: #4fc3f7;
            background: rgba(79, 195, 247, 0.2);
        }
        .file-input {
            display: none;
        }
        .results {
            display: none;
        }
        .section {
            background: #252526;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 4px solid #569cd6;
        }
        .section h3 {
            margin-top: 0;
            color: #569cd6;
        }
        .code-block {
            background: #1e1e1e;
            border: 1px solid #3c3c3c;
            border-radius: 4px;
            padding: 15px;
            overflow-x: auto;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 12px;
            line-height: 1.4;
        }
        .pattern-list {
            max-height: 200px;
            overflow-y: auto;
            background: #1e1e1e;
            border: 1px solid #3c3c3c;
            border-radius: 4px;
            padding: 10px;
        }
        .pattern-item {
            padding: 2px 0;
            border-bottom: 1px solid #3c3c3c;
        }
        .highlight {
            background: #264f78;
            padding: 2px 4px;
            border-radius: 3px;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        .stat-card {
            background: #2d2d30;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #4fc3f7;
        }
        .stat-label {
            font-size: 12px;
            color: #cccccc;
            margin-top: 5px;
        }
        .download-btn {
            background: #0e639c;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .download-btn:hover {
            background: #1177bb;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 Augment Extension Decompiler</h1>
            <p>Upload extension.js file to analyze Augment's architecture</p>
        </div>

        <div class="upload-area" id="uploadArea">
            <h3>📁 Drop extension.js file here or click to browse</h3>
            <p>Supported files: extension.js, package.json</p>
            <input type="file" id="fileInput" class="file-input" accept=".js,.json" multiple>
        </div>

        <div class="results" id="results">
            <div class="stats" id="stats"></div>
            
            <div class="section">
                <h3>📊 Analysis Summary</h3>
                <div id="summary"></div>
            </div>

            <div class="section">
                <h3>🔍 Trial System Patterns</h3>
                <div class="pattern-list" id="trialPatterns"></div>
            </div>

            <div class="section">
                <h3>🔐 License & Authentication</h3>
                <div class="pattern-list" id="licensePatterns"></div>
            </div>

            <div class="section">
                <h3>🖥️ Machine Identification</h3>
                <div class="pattern-list" id="machinePatterns"></div>
            </div>

            <div class="section">
                <h3>📈 Usage Tracking</h3>
                <div class="pattern-list" id="usagePatterns"></div>
            </div>

            <div class="section">
                <h3>🔧 VS Code APIs</h3>
                <div class="pattern-list" id="vscodeAPIs"></div>
            </div>

            <div class="section">
                <h3>📄 File Operations</h3>
                <div class="pattern-list" id="fileOps"></div>
            </div>

            <div class="section">
                <h3>🌐 Network Requests</h3>
                <div class="pattern-list" id="networkOps"></div>
            </div>

            <div class="section">
                <h3>🎨 Beautified Code</h3>
                <button class="download-btn" onclick="downloadBeautified()">Download Beautified Code</button>
                <div class="code-block" id="beautifiedCode" style="max-height: 400px; overflow-y: auto;"></div>
            </div>

            <div class="section">
                <h3>📋 Raw Analysis Data</h3>
                <button class="download-btn" onclick="downloadAnalysis()">Download Analysis JSON</button>
                <div class="code-block" id="rawData" style="max-height: 300px; overflow-y: auto;"></div>
            </div>
        </div>
    </div>

    <script>
        let analysisData = {};
        let beautifiedCode = '';

        // File upload handling
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const results = document.getElementById('results');

        uploadArea.addEventListener('click', () => fileInput.click());
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            handleFiles(e.dataTransfer.files);
        });

        fileInput.addEventListener('change', (e) => {
            handleFiles(e.target.files);
        });

        function handleFiles(files) {
            for (let file of files) {
                if (file.name.endsWith('.js') || file.name.endsWith('.json')) {
                    const reader = new FileReader();
                    reader.onload = (e) => {
                        if (file.name.endsWith('.js')) {
                            analyzeJavaScript(e.target.result, file.name);
                        } else if (file.name.endsWith('.json')) {
                            analyzePackageJson(e.target.result, file.name);
                        }
                    };
                    reader.readAsText(file);
                }
            }
        }

        function analyzeJavaScript(code, filename) {
            console.log('Analyzing JavaScript file:', filename);
            
            const analysis = {
                filename: filename,
                size: code.length,
                lines: code.split('\n').length,
                functions: extractFunctions(code),
                strings: extractStrings(code),
                imports: extractImports(code),
                vscodeAPIs: extractVSCodeAPIs(code),
                trialPatterns: extractTrialPatterns(code),
                licensePatterns: extractLicensePatterns(code),
                machinePatterns: extractMachinePatterns(code),
                usagePatterns: extractUsagePatterns(code),
                fileOperations: extractFileOperations(code),
                networkOperations: extractNetworkOperations(code)
            };

            analysisData = analysis;
            beautifiedCode = beautifyCode(code);
            displayResults(analysis);
        }

        function analyzePackageJson(content, filename) {
            try {
                const packageData = JSON.parse(content);
                console.log('Package.json analysis:', packageData);
                
                document.getElementById('summary').innerHTML += `
                    <div style="margin-bottom: 15px;">
                        <h4>📦 Package Information</h4>
                        <p><strong>Name:</strong> ${packageData.displayName || packageData.name}</p>
                        <p><strong>Version:</strong> ${packageData.version}</p>
                        <p><strong>Publisher:</strong> ${packageData.publisher}</p>
                        <p><strong>Description:</strong> ${packageData.description}</p>
                    </div>
                `;
            } catch (error) {
                console.error('Error parsing package.json:', error);
            }
        }

        function extractFunctions(code) {
            const functionRegex = /function\s+([a-zA-Z_$][a-zA-Z0-9_$]*)/g;
            const matches = [];
            let match;
            while ((match = functionRegex.exec(code)) !== null) {
                matches.push(match[1]);
            }
            return [...new Set(matches)];
        }

        function extractStrings(code) {
            const stringRegex = /(['"`])((?:(?!\1)[^\\]|\\.)*)(\1)/g;
            const matches = [];
            let match;
            while ((match = stringRegex.exec(code)) !== null) {
                const str = match[2];
                if (str.length > 3 && str.length < 100) {
                    matches.push(str);
                }
            }
            return [...new Set(matches)];
        }

        function extractImports(code) {
            const importRegex = /require\(['"`]([^'"`]+)['"`]\)/g;
            const matches = [];
            let match;
            while ((match = importRegex.exec(code)) !== null) {
                matches.push(match[1]);
            }
            return [...new Set(matches)];
        }

        function extractVSCodeAPIs(code) {
            const vscodeRegex = /vscode\.[a-zA-Z_$][a-zA-Z0-9_$.]*/g;
            return [...new Set(code.match(vscodeRegex) || [])];
        }

        function extractTrialPatterns(code) {
            const patterns = [
                /trial[a-zA-Z_$]*/gi,
                /expire[a-zA-Z_$]*/gi,
                /remaining[a-zA-Z_$]*/gi,
                /days[a-zA-Z_$]*/gi,
                /usage[a-zA-Z_$]*/gi,
                /limit[a-zA-Z_$]*/gi,
                /backup[a-zA-Z_$]*/gi
            ];
            
            const matches = [];
            patterns.forEach(pattern => {
                const found = code.match(pattern) || [];
                matches.push(...found);
            });
            
            return [...new Set(matches)];
        }

        function extractLicensePatterns(code) {
            const patterns = [
                /license[a-zA-Z_$]*/gi,
                /activation[a-zA-Z_$]*/gi,
                /subscription[a-zA-Z_$]*/gi,
                /premium[a-zA-Z_$]*/gi,
                /auth[a-zA-Z_$]*/gi,
                /token[a-zA-Z_$]*/gi
            ];
            
            const matches = [];
            patterns.forEach(pattern => {
                const found = code.match(pattern) || [];
                matches.push(...found);
            });
            
            return [...new Set(matches)];
        }

        function extractMachinePatterns(code) {
            const patterns = [
                /machineId[a-zA-Z_$]*/gi,
                /telemetry[a-zA-Z_$]*/gi,
                /fingerprint[a-zA-Z_$]*/gi,
                /device[a-zA-Z_$]*/gi,
                /hardware[a-zA-Z_$]*/gi,
                /uuid[a-zA-Z_$]*/gi,
                /guid[a-zA-Z_$]*/gi
            ];
            
            const matches = [];
            patterns.forEach(pattern => {
                const found = code.match(pattern) || [];
                matches.push(...found);
            });
            
            return [...new Set(matches)];
        }

        function extractUsagePatterns(code) {
            const patterns = [
                /count[a-zA-Z_$]*/gi,
                /track[a-zA-Z_$]*/gi,
                /analytics[a-zA-Z_$]*/gi,
                /metrics[a-zA-Z_$]*/gi,
                /stats[a-zA-Z_$]*/gi,
                /increment[a-zA-Z_$]*/gi,
                /decrement[a-zA-Z_$]*/gi
            ];
            
            const matches = [];
            patterns.forEach(pattern => {
                const found = code.match(pattern) || [];
                matches.push(...found);
            });
            
            return [...new Set(matches)];
        }

        function extractFileOperations(code) {
            const fsRegex = /fs\.[a-zA-Z_$][a-zA-Z0-9_$]*/g;
            return [...new Set(code.match(fsRegex) || [])];
        }

        function extractNetworkOperations(code) {
            const networkRegex = /(fetch|axios|request|http[s]?)\s*\(/g;
            return [...new Set(code.match(networkRegex) || [])];
        }

        function beautifyCode(code) {
            // Basic JavaScript beautification
            let beautified = code;
            
            // Add line breaks
            beautified = beautified.replace(/;(?![^"']*["'][^"']*$)/g, ';\n');
            beautified = beautified.replace(/\{(?![^"']*["'][^"']*$)/g, '{\n');
            beautified = beautified.replace(/\}(?![^"']*["'][^"']*$)/g, '\n}\n');
            
            // Basic indentation
            const lines = beautified.split('\n');
            let indentLevel = 0;
            const indentedLines = [];
            
            for (let line of lines) {
                line = line.trim();
                if (line === '') continue;
                
                if (line.includes('}')) indentLevel = Math.max(0, indentLevel - 1);
                indentedLines.push('  '.repeat(indentLevel) + line);
                if (line.includes('{')) indentLevel++;
            }
            
            return indentedLines.join('\n');
        }

        function displayResults(analysis) {
            // Show results
            results.style.display = 'block';
            
            // Display stats
            document.getElementById('stats').innerHTML = `
                <div class="stat-card">
                    <div class="stat-number">${analysis.size.toLocaleString()}</div>
                    <div class="stat-label">File Size (bytes)</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${analysis.lines.toLocaleString()}</div>
                    <div class="stat-label">Lines of Code</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${analysis.functions.length}</div>
                    <div class="stat-label">Functions Found</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${analysis.vscodeAPIs.length}</div>
                    <div class="stat-label">VS Code APIs</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${analysis.trialPatterns.length}</div>
                    <div class="stat-label">Trial Patterns</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${analysis.machinePatterns.length}</div>
                    <div class="stat-label">Machine ID Patterns</div>
                </div>
            `;
            
            // Display patterns
            displayPatterns('trialPatterns', analysis.trialPatterns);
            displayPatterns('licensePatterns', analysis.licensePatterns);
            displayPatterns('machinePatterns', analysis.machinePatterns);
            displayPatterns('usagePatterns', analysis.usagePatterns);
            displayPatterns('vscodeAPIs', analysis.vscodeAPIs);
            displayPatterns('fileOps', analysis.fileOperations);
            displayPatterns('networkOps', analysis.networkOperations);
            
            // Display beautified code
            document.getElementById('beautifiedCode').textContent = beautifiedCode.substring(0, 10000) + (beautifiedCode.length > 10000 ? '\n\n... (truncated, download full version)' : '');
            
            // Display raw analysis
            document.getElementById('rawData').textContent = JSON.stringify(analysis, null, 2);
            
            // Summary
            document.getElementById('summary').innerHTML += `
                <div>
                    <h4>🎯 Key Findings</h4>
                    <p><strong>Trial System:</strong> ${analysis.trialPatterns.length > 0 ? 'DETECTED' : 'Not detected'}</p>
                    <p><strong>License Validation:</strong> ${analysis.licensePatterns.length > 0 ? 'DETECTED' : 'Not detected'}</p>
                    <p><strong>Machine Fingerprinting:</strong> ${analysis.machinePatterns.length > 0 ? 'DETECTED' : 'Not detected'}</p>
                    <p><strong>Usage Tracking:</strong> ${analysis.usagePatterns.length > 0 ? 'DETECTED' : 'Not detected'}</p>
                    <p><strong>File Operations:</strong> ${analysis.fileOperations.length} operations found</p>
                    <p><strong>Network Operations:</strong> ${analysis.networkOperations.length} operations found</p>
                </div>
            `;
        }

        function displayPatterns(elementId, patterns) {
            const element = document.getElementById(elementId);
            if (patterns.length === 0) {
                element.innerHTML = '<div class="pattern-item">No patterns found</div>';
                return;
            }
            
            element.innerHTML = patterns.slice(0, 50).map(pattern => 
                `<div class="pattern-item"><span class="highlight">${pattern}</span></div>`
            ).join('') + (patterns.length > 50 ? '<div class="pattern-item">... and more (see raw data)</div>' : '');
        }

        function downloadBeautified() {
            const blob = new Blob([beautifiedCode], { type: 'text/javascript' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'augment-extension-beautified.js';
            a.click();
            URL.revokeObjectURL(url);
        }

        function downloadAnalysis() {
            const blob = new Blob([JSON.stringify(analysisData, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'augment-analysis.json';
            a.click();
            URL.revokeObjectURL(url);
        }
    </script>
</body>
</html>
