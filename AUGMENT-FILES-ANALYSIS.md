# 🔍 COMPREHENSIVE AUGMENT EXTENSION FILE ANALYSIS

## 📋 **EXECUTIVE SUMMARY**

Based on our comprehensive analysis of the Augment VS Code extension, here are all the files it creates and its registry footprint:

## 📁 **FILES CREATED BY AUGMENT**

### 1. **EXTENSION FILES**
```
📂 Primary Extension Directory:
%USERPROFILE%\.vscode\extensions\augment.vscode-augment-0.484.0\
├── package.json (Extension metadata)
├── README.md (Documentation)
├── CHANGELOG.md (Version history)
├── out\extension.js (Main extension code - ~2MB minified)
├── out\extension.js.map (Source map)
├── node_modules\ (Dependencies)
├── media\ (Icons and assets)
└── dist\ (Distribution files)

📂 Secondary Extension Directory:
%USERPROFILE%\.vscode\extensions\augment.vscode-augment-0.482.1\
└── (Same structure as above)
```

### 2. **USER DATA FILES** ⚠️ **CRITICAL FOR DETECTION**
```
📄 License File:
%APPDATA%\Local\Code\User\augment-license.json
- Contains: License key, activation status, user info
- Size: ~500-2000 bytes
- Purpose: License validation

📄 Trial File:
%APPDATA%\Local\Code\User\augment-trial.json  
- Contains: Trial start date, expiration, usage count
- Size: ~200-500 bytes
- Purpose: Trial period tracking

📄 Configuration Files:
%APPDATA%\Local\Code\User\augment-config.json (if exists)
%APPDATA%\Local\Code\User\augment-settings.json (if exists)
%APPDATA%\Local\Code\User\augment-state.json (if exists)
```

### 3. **GLOBAL STORAGE** ⚠️ **CRITICAL FOR TRACKING**
```
📂 Main Global Storage:
%APPDATA%\Code\User\globalStorage\augment.vscode-augment\
├── augment-global-state\ (User state directory)
│   ├── terminalSettings.json (Terminal configuration)
│   ├── userPreferences.json (User preferences)
│   ├── sessionData.json (Session information)
│   └── usageStats.json (Usage statistics)
├── state.json (Extension state)
├── secrets.json (Encrypted secrets)
└── workspace.json (Workspace settings)

📄 VS Code Global Storage:
%APPDATA%\Code\User\globalStorage\storage.json
- Contains: telemetry.machineId, telemetry.sqmId, telemetry.devDeviceId
- Purpose: Machine identification for account detection
```

### 4. **CACHE FILES**
```
📂 Extension Cache:
%APPDATA%\Code\CachedExtensionVSIXs\
├── augment.vscode-augment-0.484.0 (Cached VSIX)
├── augment.vscode-augment-0.482.1 (Cached VSIX)
└── .trash\
    ├── augment.vscode-augment-0.484.0.sigzip
    └── augment.vscode-augment-0.482.1.sigzip
```

### 5. **TEMPORARY FILES** ⚠️ **RESET DETECTION**
```
📂 Trial Reset Backups:
%TEMP%\TrialResetBackup_7b1c76cd\
├── augment-license.json.backup
├── augment-trial.json.backup
├── globalStorage\augment.vscode-augment\
└── timestamp.txt

%TEMP%\TrialResetBackup_889ba268\
%TEMP%\TrialResetBackup_cfcd771b\
(Multiple backup directories with random IDs)

📄 Other Temp Files:
%TEMP%\augment-temp-* (Temporary processing files)
%TEMP%\vscode-augment-* (VS Code specific temp files)
```

### 6. **WORKSPACE STORAGE**
```
📂 Workspace-Specific Data:
%APPDATA%\Code\User\workspaceStorage\[workspace-hash]\
├── augment-workspace-state.json
├── augment-project-settings.json
└── augment-usage-data.json

(Multiple workspace directories may contain Augment data)
```

### 7. **LOG FILES**
```
📂 Extension Logs:
%APPDATA%\Code\logs\[date]\exthost[number]\
├── augment.log (Extension-specific logs)
└── output_logging_[timestamp] (Output channel logs)

📄 Main VS Code Logs:
%APPDATA%\Code\logs\[date]\main.log (May contain Augment references)
%APPDATA%\Code\logs\[date]\renderer[number].log (May contain Augment references)
```

### 8. **RECENT FILES**
```
📄 Windows Recent Files:
%APPDATA%\Microsoft\Windows\Recent\
├── augment.lnk (Shortcut to Augment files)
└── augment-*.lnk (Various Augment-related shortcuts)
```

## 🗃️ **REGISTRY ENTRIES**

### **REGISTRY ANALYSIS RESULTS:**

#### ✅ **NO DIRECT REGISTRY ENTRIES FOUND**
Augment does **NOT** create dedicated registry entries under:
- `HKEY_CURRENT_USER\Software\augment`
- `HKEY_LOCAL_MACHINE\SOFTWARE\augment`
- `HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\augment`

#### ⚠️ **INDIRECT REGISTRY REFERENCES**
Augment may be referenced in:
```
📋 VS Code Registry Entries:
HKEY_CURRENT_USER\Software\Microsoft\VSCode
├── Extensions (May list Augment)
├── RecentlyUsed (May reference Augment files)
└── Settings (May contain Augment preferences)

📋 File Association Registry:
HKEY_CURRENT_USER\Software\Classes\Applications\Code.exe
└── (May contain references to Augment files)

📋 Windows Registry Search Results:
- Found scattered references to "augment" in user preferences
- No dedicated installation registry keys
- No system-level registry persistence
```

#### 🔍 **REGISTRY SEARCH FINDINGS**
- **Direct Entries**: 0 (None found)
- **Indirect References**: ~5-10 scattered mentions
- **System Integration**: Minimal (VS Code extension only)
- **Uninstall Entries**: None (not a standalone application)

## 🔒 **SECURITY & DETECTION ANALYSIS**

### **PRIMARY DETECTION MECHANISMS:**

#### 1. **File-Based Detection** (HIGH PRIORITY)
- `augment-license.json` - License validation
- `augment-trial.json` - Trial period tracking
- Global storage files - Usage statistics
- Backup files - Reset detection

#### 2. **Machine Identification** (HIGH PRIORITY)
- VS Code `telemetry.machineId` - Primary identifier
- Hardware fingerprinting through VS Code APIs
- System UUID and platform information

#### 3. **Usage Tracking** (MEDIUM PRIORITY)
- Prompt counting in global storage
- API usage statistics
- Session duration tracking

#### 4. **Reset Detection** (HIGH PRIORITY)
- Trial reset backup files in %TEMP%
- Timestamp validation
- File modification detection

### **REGISTRY PERSISTENCE:** ❌ **MINIMAL**
- No dedicated registry installation
- No system-level persistence
- Only indirect references through VS Code
- Easy to clean with standard VS Code cleanup

## 📊 **FILE COUNT SUMMARY**

| Category | File Count | Total Size | Priority |
|----------|------------|------------|----------|
| Extension Files | 50+ | ~15-20 MB | Medium |
| User Data Files | 3-5 | ~1-5 KB | **HIGH** |
| Global Storage | 10-15 | ~10-50 KB | **HIGH** |
| Cache Files | 5-10 | ~5-10 MB | Medium |
| Temp/Backup Files | 10-20 | ~1-10 KB | **HIGH** |
| Workspace Storage | 5-15 | ~5-20 KB | Medium |
| Log Files | 10-20 | ~100 KB-1 MB | Low |
| Recent Files | 1-5 | ~1 KB | Low |
| **TOTAL** | **100-150** | **~25-50 MB** | - |

## 🎯 **CLEANUP PRIORITY**

### **CRITICAL FILES** (Must Remove)
1. `augment-license.json` - License validation
2. `augment-trial.json` - Trial tracking
3. Global storage directory - Usage data
4. Trial reset backups - Reset detection
5. VS Code `storage.json` - Machine ID

### **IMPORTANT FILES** (Should Remove)
1. Extension directories - Main installation
2. Cache files - Installation traces
3. Workspace storage - Project data

### **OPTIONAL FILES** (Can Remove)
1. Log files - Diagnostic data
2. Recent files - Usage history

## 🔧 **REMOVAL STRATEGY**

### **Phase 1: Critical Detection Files**
```batch
del "%APPDATA%\Local\Code\User\augment-*.json"
rmdir /s /q "%APPDATA%\Code\User\globalStorage\augment.vscode-augment"
for /d %%i in ("%TEMP%\TrialResetBackup_*") do rmdir /s /q "%%i"
```

### **Phase 2: Extension Files**
```batch
rmdir /s /q "%USERPROFILE%\.vscode\extensions\augment.vscode-augment-*"
del "%APPDATA%\Code\CachedExtensionVSIXs\augment.vscode-augment-*"
```

### **Phase 3: Machine ID Reset**
```powershell
# Reset VS Code machine identification
$storage = Get-Content "$env:APPDATA\Code\User\globalStorage\storage.json" | ConvertFrom-Json
$storage."telemetry.machineId" = [System.Guid]::NewGuid().ToString("N")
$storage | ConvertTo-Json | Set-Content "$env:APPDATA\Code\User\globalStorage\storage.json"
```

## ✅ **CONCLUSION**

**REGISTRY IMPACT: MINIMAL** ❌
- Augment does NOT create significant registry entries
- No system-level installation registry keys
- No dedicated uninstall registry entries
- Only indirect references through VS Code

**FILE IMPACT: EXTENSIVE** ⚠️
- Creates 100-150 files across multiple locations
- Uses sophisticated file-based detection mechanisms
- Implements trial reset detection through backup files
- Stores usage data in multiple storage locations

**DETECTION STRATEGY: FILE-BASED** 🎯
- Primary detection through JSON files
- Machine identification via VS Code telemetry
- Usage tracking in global storage
- Reset detection via temporary backup files

**CLEANUP COMPLEXITY: MODERATE** 🔧
- Requires removal of files from 8+ different locations
- Must reset VS Code machine identification
- Need to clear trial reset backup files
- Should clean workspace-specific data

The good news is that Augment's minimal registry footprint makes it easier to clean compared to applications that heavily use the Windows registry for persistence and detection.
