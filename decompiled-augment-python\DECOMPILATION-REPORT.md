# Augment Extension Decompilation Report

## Summary
- **Functions Found**: 3160
- **VS Code APIs**: 9
- **Trial Patterns**: 38
- **License Patterns**: 76
- **Machine ID Patterns**: 78
- **Usage Tracking**: 105
- **File Operations**: 9
- **Network Operations**: 2

## Key Findings

### Trial System Detection
✅ DETECTED
- Patterns found: 38

### License Validation
✅ DETECTED
- Patterns found: 76

### Machine Fingerprinting
✅ DETECTED
- Patterns found: 78

### Usage Tracking
✅ DETECTED
- Patterns found: 105

## Trial Patterns Found
- Limit
- limitingconeangle
- TRIAL
- Remaining
- limitedEncoding
- trial
- expires
- UsageCount
- usage
- limiter
- LimitReached
- LIMITED
- Usage
- limiting
- LimitDefinition
- limitChatHistory
- Expires
- RemainingTTL
- days
- LimitsForSyncingPermission

## License Patterns Found
- Authorization
- authentication
- TokenCharCode
- authRedirectURI
- Authentication
- tokenize
- authFlow
- token
- AuthSession
- subscriptions
- authorityCertIssuer
- Authentifizierung
- Authomate
- Author
- authority
- authSession
- AuthenticatedRequest
- authSessionStore
- Token
- TokenList

## Machine ID Patterns Found
- guided_blobs
- deviceName
- guidelines_length_limit
- DEVICE_PCI_BUS_ID_NV
- device_name
- deviceid
- UUID
- devices
- uuid
- hardware
- DEVICE_BOARD_NAME_AMD
- machineId
- DEVICE_TOPOLOGY_AMD
- HardwareInformation
- Guid
- Uuid
- machineIdSync
- device_dev
- guidelinesWatcher
- device_majorClassOfDevice_string

## VS Code APIs Used
- vscode.open
- vscode.removeFromRecentlyOpened
- vscode.openFolder
- vscode.openWith
- vscode.provideDocumentSemanticTokensLegend
- vscode.closeFolder
- vscode.diff
- vscode.provideDocumentSemanticTokens
- vscode.git

## File Operations
- fs.createDirectory
- fs.stat
- fs.call
- fs.push
- fs.delete
- fs.readFile
- fs.writeFile
- fs.toLowerCase
- fs.readDirectory

---
*Report generated by Augment Decompiler*
