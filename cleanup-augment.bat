@echo off
REM Augment VS Code Extension Complete Cleanup Script
echo ========================================
echo Augment VS Code Extension Cleanup Tool
echo ========================================
echo.
echo This tool will completely remove all traces of the Augment extension.
echo.

set /p choice="Are you sure you want to proceed? (y/N): "
if /i not "%choice%"=="y" (
    echo Operation cancelled.
    pause
    exit /b 1
)

echo.
echo Starting cleanup process...
echo.

REM Stop VS Code processes
echo 1. Stopping VS Code processes...
taskkill /f /im "Code.exe" 2>nul
taskkill /f /im "code.exe" 2>nul
timeout /t 2 /nobreak >nul

REM Remove extension directories
echo 2. Removing Augment extension files...
if exist "%USERPROFILE%\.vscode\extensions\augment.vscode-augment-*" (
    echo   Removing user extension directories...
    for /d %%i in ("%USERPROFILE%\.vscode\extensions\augment.vscode-augment-*") do (
        echo     Removing: %%i
        rmdir /s /q "%%i" 2>nul
    )
)

if exist "%APPDATA%\Code\CachedExtensionVSIXs\augment.vscode-augment-*" (
    echo   Removing cached extension VSIXs...
    for %%i in ("%APPDATA%\Code\CachedExtensionVSIXs\augment.vscode-augment-*") do (
        echo     Removing: %%i
        del /f /q "%%i" 2>nul
    )
)

if exist "%APPDATA%\Code\CachedExtensionVSIXs\.trash\augment.vscode-augment-*" (
    echo   Removing trashed extension VSIXs...
    for %%i in ("%APPDATA%\Code\CachedExtensionVSIXs\.trash\augment.vscode-augment-*") do (
        echo     Removing: %%i
        del /f /q "%%i" 2>nul
    )
)

REM Remove license and trial files
echo 3. Removing license and trial files...
if exist "%APPDATA%\Local\Code\User\augment-license.json" (
    echo   Removing license file...
    del /f /q "%APPDATA%\Local\Code\User\augment-license.json" 2>nul
)

if exist "%APPDATA%\Local\Code\User\augment-trial.json" (
    echo   Removing trial file...
    del /f /q "%APPDATA%\Local\Code\User\augment-trial.json" 2>nul
)

REM Remove global storage
echo 4. Removing global storage...
if exist "%APPDATA%\Code\User\globalStorage\augment.vscode-augment" (
    echo   Removing global storage directory...
    rmdir /s /q "%APPDATA%\Code\User\globalStorage\augment.vscode-augment" 2>nul
)

REM Remove trial reset backups
echo 5. Cleaning trial reset backups...
for /d %%i in ("%TEMP%\TrialResetBackup_*") do (
    if exist "%%i\*augment*" (
        echo   Removing trial reset backup: %%i
        rmdir /s /q "%%i" 2>nul
    )
)

REM Clean workspace storage
echo 6. Cleaning workspace storage...
if exist "%APPDATA%\Code\User\workspaceStorage" (
    for /d %%i in ("%APPDATA%\Code\User\workspaceStorage\*") do (
        if exist "%%i\*augment*" (
            echo   Removing workspace storage: %%i
            rmdir /s /q "%%i" 2>nul
        )
    )
)

REM Remove recent file references
echo 7. Removing recent file references...
if exist "%APPDATA%\Microsoft\Windows\Recent\augment.lnk" (
    echo   Removing recent file link...
    del /f /q "%APPDATA%\Microsoft\Windows\Recent\augment.lnk" 2>nul
)

REM Reset machine identification using PowerShell
echo 8. Resetting machine identification...
powershell -ExecutionPolicy Bypass -Command "try { $storageJsonPath = \"$env:APPDATA\\Code\\User\\globalStorage\\storage.json\"; if (Test-Path $storageJsonPath) { $storageContent = Get-Content $storageJsonPath -Raw | ConvertFrom-Json; $storageContent.\"telemetry.machineId\" = [System.Guid]::NewGuid().ToString(\"N\"); $storageContent.\"telemetry.sqmId\" = \"{$([System.Guid]::NewGuid().ToString().ToUpper())}\"; $storageContent.\"telemetry.devDeviceId\" = [System.Guid]::NewGuid().ToString(); $storageContent | ConvertTo-Json -Depth 10 | Set-Content $storageJsonPath -Encoding UTF8; Write-Host \"  Success: Reset VS Code machine identification\" } } catch { Write-Warning \"Failed to reset machine identification\" }"

echo.
echo ========================================
echo SUCCESS: Cleanup completed successfully!
echo ========================================
echo All Augment extension traces have been removed.
echo.
echo Recommendations:
echo 1. Restart VS Code to ensure all changes take effect
echo 2. Clear browser cache if you used Augment web features
echo 3. Consider restarting your computer for complete cleanup
echo.
pause
