@echo off
REM Augment VS Code Extension Cleanup Launcher
REM This batch file runs the PowerShell cleanup script with appropriate permissions

echo ========================================
echo Augment VS Code Extension Cleanup Tool
echo ========================================
echo.
echo This tool will completely remove all traces of the Augment extension.
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo Running with administrator privileges.
) else (
    echo WARNING: Not running as administrator.
    echo Some cleanup operations may fail without admin rights.
    echo.
    set /p choice="Continue anyway? (y/N): "
    if /i not "%choice%"=="y" (
        echo Operation cancelled.
        pause
        exit /b 1
    )
)

echo.
echo Choose cleanup options:
echo 1. Quick cleanup (no backup)
echo 2. Safe cleanup (with backup)
echo 3. Verbose cleanup (with backup and detailed output)
echo 4. Force cleanup (no prompts, no backup)
echo.
set /p option="Enter your choice (1-4): "

set PS_ARGS=
if "%option%"=="1" (
    set PS_ARGS=-Force
    echo Running quick cleanup...
) else if "%option%"=="2" (
    set PS_ARGS=-Backup
    echo Running safe cleanup with backup...
) else if "%option%"=="3" (
    set PS_ARGS=-Backup -Verbose
    echo Running verbose cleanup with backup...
) else if "%option%"=="4" (
    set PS_ARGS=-Force
    echo Running force cleanup...
) else (
    echo Invalid option selected.
    pause
    exit /b 1
)

echo.
echo Starting cleanup process...
echo.

REM Execute PowerShell script
powershell.exe -ExecutionPolicy Bypass -File "%~dp0augment-cleanup.ps1" %PS_ARGS%

if %errorLevel% == 0 (
    echo.
    echo ========================================
    echo Cleanup completed successfully!
    echo ========================================
) else (
    echo.
    echo ========================================
    echo Cleanup failed with errors.
    echo ========================================
)

echo.
pause
