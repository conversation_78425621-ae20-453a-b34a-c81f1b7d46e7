#!/usr/bin/env python3
"""
Augment Extension Decompiler - Python Version
This script locates and decompiles the Augment VS Code extension
"""

import os
import json
import re
import glob
from pathlib import Path
import sys

class AugmentDecompiler:
    def __init__(self):
        self.user_home = Path.home()
        self.extension_paths = [
            self.user_home / ".vscode" / "extensions",
            self.user_home / ".vscode-insiders" / "extensions",
            Path(os.environ.get('APPDATA', '')) / "Code" / "User" / "extensions",
            Path(os.environ.get('LOCALAPPDATA', '')) / "Programs" / "Microsoft VS Code" / "resources" / "app" / "extensions"
        ]
        self.output_dir = Path("./decompiled-augment-python")
        
    def find_augment_extensions(self):
        """Find all Augment extension directories"""
        print("🔍 Searching for Augment extensions...")
        found_extensions = []
        
        for base_path in self.extension_paths:
            try:
                if base_path.exists():
                    print(f"  Checking: {base_path}")
                    
                    # Look for directories containing 'augment'
                    for item in base_path.iterdir():
                        if item.is_dir() and 'augment' in item.name.lower():
                            print(f"  ✅ Found: {item}")
                            found_extensions.append(item)
                            
            except Exception as e:
                print(f"  ❌ Cannot access: {base_path} - {e}")
                
        return found_extensions
    
    def analyze_package_json(self, extension_path):
        """Analyze the package.json file"""
        print("\n📋 Analyzing package.json...")
        
        package_path = extension_path / "package.json"
        if not package_path.exists():
            print("  ❌ package.json not found")
            return None
            
        try:
            with open(package_path, 'r', encoding='utf-8') as f:
                package_data = json.load(f)
                
            print(f"  ✅ Extension: {package_data.get('displayName', package_data.get('name', 'Unknown'))}")
            print(f"  ✅ Version: {package_data.get('version', 'Unknown')}")
            print(f"  ✅ Publisher: {package_data.get('publisher', 'Unknown')}")
            print(f"  ✅ Description: {package_data.get('description', 'No description')}")
            
            if 'main' in package_data:
                print(f"  ✅ Main file: {package_data['main']}")
                
            if 'activationEvents' in package_data:
                print(f"  ✅ Activation events: {len(package_data['activationEvents'])}")
                
            if 'contributes' in package_data:
                print(f"  ✅ Contributes: {', '.join(package_data['contributes'].keys())}")
                
            return package_data
            
        except Exception as e:
            print(f"  ❌ Error reading package.json: {e}")
            return None
    
    def find_extension_js(self, extension_path):
        """Find the main extension.js file"""
        possible_paths = [
            extension_path / "out" / "extension.js",
            extension_path / "extension.js",
            extension_path / "dist" / "extension.js",
            extension_path / "lib" / "extension.js",
            extension_path / "build" / "extension.js"
        ]
        
        for path in possible_paths:
            if path.exists():
                return path
                
        return None
    
    def analyze_javascript(self, js_path):
        """Analyze the JavaScript extension file"""
        print(f"\n🔧 Analyzing JavaScript file: {js_path}")
        
        try:
            with open(js_path, 'r', encoding='utf-8', errors='ignore') as f:
                js_content = f.read()
                
            file_size = len(js_content)
            line_count = len(js_content.split('\n'))
            
            print(f"  ✅ File size: {file_size:,} bytes")
            print(f"  ✅ Lines: {line_count:,}")
            
            # Create output directory
            self.output_dir.mkdir(exist_ok=True)
            
            # Save original file
            with open(self.output_dir / "extension-original.js", 'w', encoding='utf-8') as f:
                f.write(js_content)
            
            # Analyze patterns
            analysis = self.extract_patterns(js_content)
            
            # Save analysis
            with open(self.output_dir / "analysis.json", 'w', encoding='utf-8') as f:
                json.dump(analysis, f, indent=2)
            
            # Beautify code
            beautified = self.beautify_code(js_content)
            with open(self.output_dir / "extension-beautified.js", 'w', encoding='utf-8') as f:
                f.write(beautified)
            
            print(f"  ✅ Analysis saved to: {self.output_dir}")
            
            return analysis
            
        except Exception as e:
            print(f"  ❌ Error analyzing JavaScript: {e}")
            return None
    
    def extract_patterns(self, code):
        """Extract patterns from the JavaScript code"""
        print("  🔍 Extracting patterns...")
        
        patterns = {
            'functions': self.extract_functions(code),
            'strings': self.extract_strings(code),
            'imports': self.extract_imports(code),
            'vscode_apis': self.extract_vscode_apis(code),
            'trial_patterns': self.extract_trial_patterns(code),
            'license_patterns': self.extract_license_patterns(code),
            'machine_patterns': self.extract_machine_patterns(code),
            'usage_patterns': self.extract_usage_patterns(code),
            'file_operations': self.extract_file_operations(code),
            'network_operations': self.extract_network_operations(code)
        }
        
        return patterns
    
    def extract_functions(self, code):
        """Extract function names"""
        pattern = r'function\s+([a-zA-Z_$][a-zA-Z0-9_$]*)'
        matches = re.findall(pattern, code)
        return list(set(matches))
    
    def extract_strings(self, code):
        """Extract string literals"""
        pattern = r'[\'"`]([^\'"`\n]{3,100})[\'"`]'
        matches = re.findall(pattern, code)
        return list(set(matches))
    
    def extract_imports(self, code):
        """Extract require/import statements"""
        pattern = r'require\([\'"`]([^\'"`]+)[\'"`]\)'
        matches = re.findall(pattern, code)
        return list(set(matches))
    
    def extract_vscode_apis(self, code):
        """Extract VS Code API calls"""
        pattern = r'vscode\.[a-zA-Z_$][a-zA-Z0-9_$\.]*'
        matches = re.findall(pattern, code)
        return list(set(matches))
    
    def extract_trial_patterns(self, code):
        """Extract trial-related patterns"""
        patterns = [
            r'trial[a-zA-Z_$]*',
            r'expire[a-zA-Z_$]*',
            r'remaining[a-zA-Z_$]*',
            r'days[a-zA-Z_$]*',
            r'usage[a-zA-Z_$]*',
            r'limit[a-zA-Z_$]*',
            r'backup[a-zA-Z_$]*'
        ]
        
        matches = []
        for pattern in patterns:
            found = re.findall(pattern, code, re.IGNORECASE)
            matches.extend(found)
            
        return list(set(matches))
    
    def extract_license_patterns(self, code):
        """Extract license-related patterns"""
        patterns = [
            r'license[a-zA-Z_$]*',
            r'activation[a-zA-Z_$]*',
            r'subscription[a-zA-Z_$]*',
            r'premium[a-zA-Z_$]*',
            r'auth[a-zA-Z_$]*',
            r'token[a-zA-Z_$]*'
        ]
        
        matches = []
        for pattern in patterns:
            found = re.findall(pattern, code, re.IGNORECASE)
            matches.extend(found)
            
        return list(set(matches))
    
    def extract_machine_patterns(self, code):
        """Extract machine identification patterns"""
        patterns = [
            r'machineId[a-zA-Z_$]*',
            r'telemetry[a-zA-Z_$]*',
            r'fingerprint[a-zA-Z_$]*',
            r'device[a-zA-Z_$]*',
            r'hardware[a-zA-Z_$]*',
            r'uuid[a-zA-Z_$]*',
            r'guid[a-zA-Z_$]*'
        ]
        
        matches = []
        for pattern in patterns:
            found = re.findall(pattern, code, re.IGNORECASE)
            matches.extend(found)
            
        return list(set(matches))
    
    def extract_usage_patterns(self, code):
        """Extract usage tracking patterns"""
        patterns = [
            r'count[a-zA-Z_$]*',
            r'track[a-zA-Z_$]*',
            r'analytics[a-zA-Z_$]*',
            r'metrics[a-zA-Z_$]*',
            r'stats[a-zA-Z_$]*',
            r'increment[a-zA-Z_$]*',
            r'decrement[a-zA-Z_$]*'
        ]
        
        matches = []
        for pattern in patterns:
            found = re.findall(pattern, code, re.IGNORECASE)
            matches.extend(found)
            
        return list(set(matches))
    
    def extract_file_operations(self, code):
        """Extract file system operations"""
        pattern = r'fs\.[a-zA-Z_$][a-zA-Z0-9_$]*'
        matches = re.findall(pattern, code)
        return list(set(matches))
    
    def extract_network_operations(self, code):
        """Extract network operations"""
        pattern = r'(fetch|axios|request|https?)\s*\('
        matches = re.findall(pattern, code)
        return list(set(matches))
    
    def beautify_code(self, code):
        """Basic JavaScript beautification"""
        print("  🎨 Beautifying code...")
        
        # Basic beautification
        beautified = code
        beautified = re.sub(r';(?![^"\']*["\'][^"\']*$)', ';\n', beautified)
        beautified = re.sub(r'\{(?![^"\']*["\'][^"\']*$)', '{\n', beautified)
        beautified = re.sub(r'\}(?![^"\']*["\'][^"\']*$)', '\n}\n', beautified)
        
        # Basic indentation
        lines = beautified.split('\n')
        indent_level = 0
        indented_lines = []
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            if '}' in line:
                indent_level = max(0, indent_level - 1)
                
            indented_lines.append('  ' * indent_level + line)
            
            if '{' in line:
                indent_level += 1
        
        return '\n'.join(indented_lines)
    
    def generate_report(self, package_info, analysis):
        """Generate a comprehensive report"""
        print("\n📄 Generating report...")
        
        report = {
            'timestamp': str(Path().cwd()),
            'package_info': package_info,
            'analysis': analysis,
            'summary': {
                'total_functions': len(analysis.get('functions', [])),
                'total_strings': len(analysis.get('strings', [])),
                'vscode_apis': len(analysis.get('vscode_apis', [])),
                'trial_patterns': len(analysis.get('trial_patterns', [])),
                'license_patterns': len(analysis.get('license_patterns', [])),
                'machine_patterns': len(analysis.get('machine_patterns', [])),
                'usage_patterns': len(analysis.get('usage_patterns', [])),
                'file_operations': len(analysis.get('file_operations', [])),
                'network_operations': len(analysis.get('network_operations', []))
            }
        }
        
        with open(self.output_dir / "decompilation-report.json", 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2)
        
        # Generate markdown report
        self.generate_markdown_report(report)
        
        print(f"  ✅ Report saved to: {self.output_dir}")
        
        return report
    
    def generate_markdown_report(self, report):
        """Generate a markdown report"""
        summary = report['summary']
        analysis = report['analysis']
        
        markdown = f"""# Augment Extension Decompilation Report

## Summary
- **Functions Found**: {summary['total_functions']}
- **VS Code APIs**: {summary['vscode_apis']}
- **Trial Patterns**: {summary['trial_patterns']}
- **License Patterns**: {summary['license_patterns']}
- **Machine ID Patterns**: {summary['machine_patterns']}
- **Usage Tracking**: {summary['usage_patterns']}
- **File Operations**: {summary['file_operations']}
- **Network Operations**: {summary['network_operations']}

## Key Findings

### Trial System Detection
{'✅ DETECTED' if summary['trial_patterns'] > 0 else '❌ NOT DETECTED'}
- Patterns found: {summary['trial_patterns']}

### License Validation
{'✅ DETECTED' if summary['license_patterns'] > 0 else '❌ NOT DETECTED'}
- Patterns found: {summary['license_patterns']}

### Machine Fingerprinting
{'✅ DETECTED' if summary['machine_patterns'] > 0 else '❌ NOT DETECTED'}
- Patterns found: {summary['machine_patterns']}

### Usage Tracking
{'✅ DETECTED' if summary['usage_patterns'] > 0 else '❌ NOT DETECTED'}
- Patterns found: {summary['usage_patterns']}

## Trial Patterns Found
{chr(10).join(f"- {pattern}" for pattern in analysis.get('trial_patterns', [])[:20])}

## License Patterns Found
{chr(10).join(f"- {pattern}" for pattern in analysis.get('license_patterns', [])[:20])}

## Machine ID Patterns Found
{chr(10).join(f"- {pattern}" for pattern in analysis.get('machine_patterns', [])[:20])}

## VS Code APIs Used
{chr(10).join(f"- {api}" for api in analysis.get('vscode_apis', [])[:20])}

## File Operations
{chr(10).join(f"- {op}" for op in analysis.get('file_operations', [])[:20])}

---
*Report generated by Augment Decompiler*
"""
        
        with open(self.output_dir / "DECOMPILATION-REPORT.md", 'w', encoding='utf-8') as f:
            f.write(markdown)
    
    def decompile(self):
        """Main decompilation process"""
        print("🚀 Starting Augment Extension Decompilation...\n")
        
        try:
            # Find extensions
            extensions = self.find_augment_extensions()
            
            if not extensions:
                print("❌ No Augment extensions found!")
                return
            
            # Use the most recent extension
            latest_extension = extensions[-1]
            print(f"\n🎯 Analyzing: {latest_extension}")
            
            # Analyze package.json
            package_info = self.analyze_package_json(latest_extension)
            
            # Find and analyze extension.js
            js_path = self.find_extension_js(latest_extension)
            if js_path:
                analysis = self.analyze_javascript(js_path)
                
                if analysis:
                    # Generate report
                    report = self.generate_report(package_info, analysis)
                    
                    print("\n🎉 Decompilation completed successfully!")
                    print(f"📁 Results saved to: {self.output_dir}")
                    
                    # Print summary
                    print("\n📊 Quick Summary:")
                    print(f"  Functions: {len(analysis.get('functions', []))}")
                    print(f"  VS Code APIs: {len(analysis.get('vscode_apis', []))}")
                    print(f"  Trial patterns: {len(analysis.get('trial_patterns', []))}")
                    print(f"  License patterns: {len(analysis.get('license_patterns', []))}")
                    print(f"  Machine ID patterns: {len(analysis.get('machine_patterns', []))}")
                    
                else:
                    print("❌ Failed to analyze JavaScript file")
            else:
                print("❌ No extension.js file found")
                
        except Exception as e:
            print(f"❌ Decompilation failed: {e}")

if __name__ == "__main__":
    decompiler = AugmentDecompiler()
    decompiler.decompile()
