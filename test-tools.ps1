# Test Script for Augment Analysis Tools
# This script tests our decompiler and cleanup tools

Write-Host "=== Testing Augment Analysis Tools ===" -ForegroundColor Cyan
Write-Host "Starting tool validation..." -ForegroundColor Yellow

# Test 1: Check if Augment extension exists
Write-Host "`n1. Checking for Augment extension..." -ForegroundColor Green

$augmentPaths = @(
    "$env:USERPROFILE\.vscode\extensions\augment.vscode-augment-0.484.0",
    "$env:USERPROFILE\.vscode\extensions\augment.vscode-augment-0.482.1"
)

$foundExtensions = @()
foreach ($path in $augmentPaths) {
    if (Test-Path $path) {
        $foundExtensions += $path
        Write-Host "  Found: $path" -ForegroundColor White
    }
}

if ($foundExtensions.Count -eq 0) {
    Write-Host "  No Augment extensions found" -ForegroundColor Red
    Write-Host "  Cannot proceed with testing" -ForegroundColor Red
    exit 1
}

# Test 2: Check for Augment files
Write-Host "`n2. Checking for Augment files..." -ForegroundColor Green

$augmentFiles = @(
    "$env:APPDATA\Local\Code\User\augment-license.json",
    "$env:APPDATA\Local\Code\User\augment-trial.json",
    "$env:APPDATA\Code\User\globalStorage\augment.vscode-augment"
)

$foundFiles = @()
foreach ($file in $augmentFiles) {
    if (Test-Path $file) {
        $foundFiles += $file
        Write-Host "  Found: $file" -ForegroundColor White
    } else {
        Write-Host "  Not found: $file" -ForegroundColor Gray
    }
}

Write-Host "  Total Augment files found: $($foundFiles.Count)" -ForegroundColor White

# Test 3: Test basic file operations
Write-Host "`n3. Testing file operations..." -ForegroundColor Green

try {
    # Test creating a test file
    $testFile = ".\test-file.txt"
    "Test content" | Out-File $testFile -Encoding UTF8
    
    if (Test-Path $testFile) {
        Write-Host "  File creation: SUCCESS" -ForegroundColor Green
        Remove-Item $testFile -Force
        Write-Host "  File deletion: SUCCESS" -ForegroundColor Green
    } else {
        Write-Host "  File creation: FAILED" -ForegroundColor Red
    }
} catch {
    Write-Host "  File operations: FAILED - $_" -ForegroundColor Red
}

# Test 4: Test PowerShell execution
Write-Host "`n4. Testing PowerShell capabilities..." -ForegroundColor Green

try {
    $guid = [System.Guid]::NewGuid().ToString()
    Write-Host "  GUID generation: SUCCESS ($guid)" -ForegroundColor Green
    
    $json = @{ test = "value" } | ConvertTo-Json
    Write-Host "  JSON conversion: SUCCESS" -ForegroundColor Green
    
    $date = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    Write-Host "  Date formatting: SUCCESS ($date)" -ForegroundColor Green
    
} catch {
    Write-Host "  PowerShell capabilities: FAILED - $_" -ForegroundColor Red
}

# Test 5: Test VS Code storage access
Write-Host "`n5. Testing VS Code storage access..." -ForegroundColor Green

$storageJsonPath = "$env:APPDATA\Code\User\globalStorage\storage.json"
if (Test-Path $storageJsonPath) {
    try {
        $storageContent = Get-Content $storageJsonPath -Raw | ConvertFrom-Json
        $machineId = $storageContent."telemetry.machineId"
        
        if ($machineId) {
            Write-Host "  VS Code storage access: SUCCESS" -ForegroundColor Green
            Write-Host "  Current machine ID: $($machineId.Substring(0,8))..." -ForegroundColor White
        } else {
            Write-Host "  VS Code storage access: PARTIAL (no machine ID)" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "  VS Code storage access: FAILED - $_" -ForegroundColor Red
    }
} else {
    Write-Host "  VS Code storage not found" -ForegroundColor Red
}

# Test 6: Test our script files exist
Write-Host "`n6. Checking our tool files..." -ForegroundColor Green

$toolFiles = @(
    ".\augment-decompiler.ps1",
    ".\verify-cleanup.ps1",
    ".\cleanup-augment.bat",
    ".\extension-decompiler.js"
)

foreach ($tool in $toolFiles) {
    if (Test-Path $tool) {
        $size = (Get-Item $tool).Length
        Write-Host "  Found: $tool ($size bytes)" -ForegroundColor Green
    } else {
        Write-Host "  Missing: $tool" -ForegroundColor Red
    }
}

# Test 7: Simple decompiler test
Write-Host "`n7. Testing simple extension analysis..." -ForegroundColor Green

if ($foundExtensions.Count -gt 0) {
    $extensionPath = $foundExtensions[0]
    
    try {
        # Test package.json reading
        $packagePath = Join-Path $extensionPath "package.json"
        if (Test-Path $packagePath) {
            $packageContent = Get-Content $packagePath -Raw | ConvertFrom-Json
            Write-Host "  Package.json analysis: SUCCESS" -ForegroundColor Green
            Write-Host "    Extension: $($packageContent.displayName)" -ForegroundColor White
            Write-Host "    Version: $($packageContent.version)" -ForegroundColor White
            Write-Host "    Publisher: $($packageContent.publisher)" -ForegroundColor White
        }
        
        # Test extension.js reading
        $extensionFile = Join-Path $extensionPath "out\extension.js"
        if (Test-Path $extensionFile) {
            $fileSize = (Get-Item $extensionFile).Length
            Write-Host "  Extension.js analysis: SUCCESS ($fileSize bytes)" -ForegroundColor Green
            
            # Try to read first 1000 characters
            $codeSnippet = Get-Content $extensionFile -Raw -TotalCount 1000
            if ($codeSnippet.Length -gt 0) {
                Write-Host "  Code reading: SUCCESS" -ForegroundColor Green
                
                # Test pattern matching
                if ($codeSnippet -match "vscode") {
                    Write-Host "  Pattern matching: SUCCESS (found 'vscode')" -ForegroundColor Green
                } else {
                    Write-Host "  Pattern matching: PARTIAL (no 'vscode' found in snippet)" -ForegroundColor Yellow
                }
            }
        } else {
            Write-Host "  Extension.js not found" -ForegroundColor Red
        }
        
    } catch {
        Write-Host "  Extension analysis: FAILED - $_" -ForegroundColor Red
    }
}

# Test 8: Test cleanup simulation (dry run)
Write-Host "`n8. Testing cleanup simulation..." -ForegroundColor Green

try {
    $testDir = ".\test-cleanup"
    New-Item -ItemType Directory -Path $testDir -Force | Out-Null
    
    # Create test files
    "test" | Out-File "$testDir\test-license.json" -Encoding UTF8
    "test" | Out-File "$testDir\test-trial.json" -Encoding UTF8
    
    # Test removal
    if ((Test-Path "$testDir\test-license.json") -and (Test-Path "$testDir\test-trial.json")) {
        Remove-Item "$testDir\*" -Force
        
        if (-not (Test-Path "$testDir\test-license.json")) {
            Write-Host "  Cleanup simulation: SUCCESS" -ForegroundColor Green
        } else {
            Write-Host "  Cleanup simulation: FAILED" -ForegroundColor Red
        }
    }
    
    # Cleanup test directory
    Remove-Item $testDir -Recurse -Force -ErrorAction SilentlyContinue
    
} catch {
    Write-Host "  Cleanup simulation: FAILED - $_" -ForegroundColor Red
}

# Summary
Write-Host "`n=== TEST SUMMARY ===" -ForegroundColor Cyan
Write-Host "Extensions found: $($foundExtensions.Count)" -ForegroundColor White
Write-Host "Augment files found: $($foundFiles.Count)" -ForegroundColor White

if ($foundExtensions.Count -gt 0 -and $foundFiles.Count -gt 0) {
    Write-Host "STATUS: READY FOR TESTING" -ForegroundColor Green
    Write-Host "You can now run the full decompiler and cleanup tools" -ForegroundColor Green
} elseif ($foundExtensions.Count -gt 0) {
    Write-Host "STATUS: PARTIAL READY" -ForegroundColor Yellow
    Write-Host "Extension found but limited Augment files detected" -ForegroundColor Yellow
} else {
    Write-Host "STATUS: NO TARGET FOUND" -ForegroundColor Red
    Write-Host "No Augment extension detected for testing" -ForegroundColor Red
}

Write-Host "`nNext steps:" -ForegroundColor Cyan
Write-Host "1. Run: .\augment-decompiler.ps1 (if extensions found)" -ForegroundColor White
Write-Host "2. Run: .\verify-cleanup.ps1 (to see current state)" -ForegroundColor White
Write-Host "3. Run: .\cleanup-augment.bat (to clean up)" -ForegroundColor White
