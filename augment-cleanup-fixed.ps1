# Augment VS Code Extension Complete Cleanup Script
# This script removes all traces of the Augment extension and resets detection mechanisms

param(
    [switch]$Force,
    [switch]$Backup,
    [switch]$Verbose
)

# Set execution policy for current session
Set-ExecutionPolicy -ExecutionPolicy Bypass -Scope Process -Force

# Enable verbose output if requested
if ($Verbose) {
    $VerbosePreference = "Continue"
}

Write-Host "=== Augment VS Code Extension Cleanup Script ===" -ForegroundColor Cyan
Write-Host "This script will completely remove all traces of the Augment extension." -ForegroundColor Yellow

if (-not $Force) {
    $confirmation = Read-Host "Are you sure you want to proceed? (y/N)"
    if ($confirmation -ne 'y' -and $confirmation -ne 'Y') {
        Write-Host "Operation cancelled." -ForegroundColor Red
        exit 1
    }
}

# Function to safely remove files/directories
function Remove-SafelyWithBackup {
    param(
        [string]$Path,
        [string]$Description
    )
    
    if (Test-Path $Path) {
        Write-Host "Removing: $Description" -ForegroundColor Green
        Write-Verbose "Path: $Path"
        
        if ($Backup) {
            $backupPath = "$env:TEMP\AugmentCleanupBackup_$(Get-Date -Format 'yyyyMMdd_HHmmss')"
            if (-not (Test-Path $backupPath)) {
                New-Item -ItemType Directory -Path $backupPath -Force | Out-Null
            }
            
            try {
                $relativePath = $Path.Replace("$env:USERPROFILE", "").TrimStart('\')
                $backupTarget = Join-Path $backupPath $relativePath
                $backupDir = Split-Path $backupTarget -Parent
                
                if (-not (Test-Path $backupDir)) {
                    New-Item -ItemType Directory -Path $backupDir -Force | Out-Null
                }
                
                Copy-Item -Path $Path -Destination $backupTarget -Recurse -Force
                Write-Verbose "Backed up to: $backupTarget"
            }
            catch {
                Write-Warning "Failed to backup $Path : $_"
            }
        }
        
        try {
            Remove-Item -Path $Path -Recurse -Force -ErrorAction Stop
            Write-Host "  ✓ Removed successfully" -ForegroundColor Green
        }
        catch {
            Write-Warning "Failed to remove $Path : $_"
        }
    }
    else {
        Write-Verbose "Not found: $Path"
    }
}

# Function to stop VS Code processes
function Stop-VSCodeProcesses {
    Write-Host "Stopping VS Code processes..." -ForegroundColor Yellow
    
    $processes = Get-Process | Where-Object { $_.ProcessName -like "*code*" -or $_.ProcessName -like "*Code*" }
    
    foreach ($process in $processes) {
        try {
            Write-Host "  Stopping process: $($process.ProcessName) (PID: $($process.Id))" -ForegroundColor Yellow
            $process.Kill()
            $process.WaitForExit(5000)
        }
        catch {
            Write-Warning "Failed to stop process $($process.ProcessName): $_"
        }
    }
    
    Start-Sleep -Seconds 2
}

# Function to reset machine identification
function Reset-MachineIdentification {
    Write-Host "Resetting machine identification..." -ForegroundColor Cyan
    
    # Reset VS Code telemetry machine ID
    $storageJsonPath = "$env:APPDATA\Code\User\globalStorage\storage.json"
    if (Test-Path $storageJsonPath) {
        try {
            $storageContent = Get-Content $storageJsonPath -Raw | ConvertFrom-Json
            
            # Generate new machine IDs
            $newMachineId = [System.Guid]::NewGuid().ToString("N")
            $newSqmId = "{$([System.Guid]::NewGuid().ToString().ToUpper())}"
            $newDeviceId = [System.Guid]::NewGuid().ToString()
            
            $storageContent."telemetry.machineId" = $newMachineId
            $storageContent."telemetry.sqmId" = $newSqmId
            $storageContent."telemetry.devDeviceId" = $newDeviceId
            
            $storageContent | ConvertTo-Json -Depth 10 | Set-Content $storageJsonPath -Encoding UTF8
            Write-Host "  ✓ Reset VS Code machine identification" -ForegroundColor Green
        }
        catch {
            Write-Warning "Failed to reset machine identification: $_"
        }
    }
}

# Main cleanup function
function Start-AugmentCleanup {
    Write-Host "`n1. Stopping VS Code processes..." -ForegroundColor Cyan
    Stop-VSCodeProcesses
    
    Write-Host "`n2. Removing Augment extension files..." -ForegroundColor Cyan
    
    # Remove extension directories
    $extensionPaths = @(
        "$env:USERPROFILE\.vscode\extensions\augment.vscode-augment-*",
        "$env:APPDATA\Code\CachedExtensionVSIXs\augment.vscode-augment-*",
        "$env:APPDATA\Code\CachedExtensionVSIXs\.trash\augment.vscode-augment-*"
    )
    
    foreach ($pattern in $extensionPaths) {
        $parentPath = Split-Path $pattern -Parent
        $filter = Split-Path $pattern -Leaf
        if (Test-Path $parentPath) {
            $paths = Get-ChildItem -Path $parentPath -Filter $filter -ErrorAction SilentlyContinue
            foreach ($path in $paths) {
                Remove-SafelyWithBackup -Path $path.FullName -Description "Extension directory: $($path.Name)"
            }
        }
    }
    
    Write-Host "`n3. Removing license and trial files..." -ForegroundColor Cyan
    
    # Remove license and trial files
    $licenseFiles = @(
        "$env:APPDATA\Local\Code\User\augment-license.json",
        "$env:APPDATA\Local\Code\User\augment-trial.json"
    )
    
    foreach ($file in $licenseFiles) {
        Remove-SafelyWithBackup -Path $file -Description "License/Trial file: $(Split-Path $file -Leaf)"
    }
    
    Write-Host "`n4. Removing global storage..." -ForegroundColor Cyan
    
    # Remove global storage
    $globalStoragePath = "$env:APPDATA\Code\User\globalStorage\augment.vscode-augment"
    Remove-SafelyWithBackup -Path $globalStoragePath -Description "Global storage directory"
    
    Write-Host "`n5. Cleaning trial reset backups..." -ForegroundColor Cyan
    
    # Remove trial reset backups
    if (Test-Path "$env:TEMP") {
        $tempBackups = Get-ChildItem -Path "$env:TEMP" -Filter "TrialResetBackup_*" -Directory -ErrorAction SilentlyContinue
        foreach ($backup in $tempBackups) {
            $augmentFiles = Get-ChildItem -Path $backup.FullName -Recurse -Filter "*augment*" -ErrorAction SilentlyContinue
            if ($augmentFiles.Count -gt 0) {
                Remove-SafelyWithBackup -Path $backup.FullName -Description "Trial reset backup: $($backup.Name)"
            }
        }
    }
    
    Write-Host "`n6. Resetting machine identification..." -ForegroundColor Cyan
    Reset-MachineIdentification
    
    Write-Host "`n7. Cleaning workspace storage..." -ForegroundColor Cyan
    
    # Clean workspace storage that might contain Augment data
    $workspaceStoragePath = "$env:APPDATA\Code\User\workspaceStorage"
    if (Test-Path $workspaceStoragePath) {
        $workspaces = Get-ChildItem -Path $workspaceStoragePath -Directory -ErrorAction SilentlyContinue
        foreach ($workspace in $workspaces) {
            $augmentFiles = Get-ChildItem -Path $workspace.FullName -Recurse -Filter "*augment*" -ErrorAction SilentlyContinue
            if ($augmentFiles.Count -gt 0) {
                Remove-SafelyWithBackup -Path $workspace.FullName -Description "Workspace storage: $($workspace.Name)"
            }
        }
    }
    
    Write-Host "`n8. Removing recent file references..." -ForegroundColor Cyan
    
    # Remove recent file references
    $recentPath = "$env:APPDATA\Microsoft\Windows\Recent\augment.lnk"
    Remove-SafelyWithBackup -Path $recentPath -Description "Recent file link"
    
    Write-Host "`n✓ Cleanup completed successfully!" -ForegroundColor Green
    Write-Host "All Augment extension traces have been removed." -ForegroundColor Green
    
    if ($Backup) {
        Write-Host "`nBackup files are stored in: $env:TEMP\AugmentCleanupBackup_*" -ForegroundColor Yellow
    }
    
    Write-Host "`nRecommendations:" -ForegroundColor Yellow
    Write-Host "1. Restart VS Code to ensure all changes take effect" -ForegroundColor White
    Write-Host "2. Clear browser cache if you used Augment web features" -ForegroundColor White
    Write-Host "3. Consider restarting your computer for complete cleanup" -ForegroundColor White
}

# Execute cleanup
try {
    Start-AugmentCleanup
}
catch {
    Write-Error "An error occurred during cleanup: $_"
    exit 1
}

Write-Host "`nCleanup script completed." -ForegroundColor Gray
