# Augment Extension Decompiler and Analyzer
# This script analyzes the Augment VS Code extension to understand its detection mechanisms

param(
    [string]$ExtensionPath = "$env:USERPROFILE\.vscode\extensions\augment.vscode-augment-0.484.0",
    [string]$OutputDir = ".\decompiled-output"
)

Write-Host "=== Augment Extension Decompiler ===" -ForegroundColor Cyan
Write-Host "Analyzing extension at: $ExtensionPath" -ForegroundColor Yellow

# Create output directory
if (-not (Test-Path $OutputDir)) {
    New-Item -ItemType Directory -Path $OutputDir -Force | Out-Null
}

# Function to analyze package.json
function Analyze-PackageJson {
    param([string]$Path)
    
    Write-Host "`n1. Analyzing package.json..." -ForegroundColor Green
    
    $packagePath = Join-Path $Path "package.json"
    if (Test-Path $packagePath) {
        $packageContent = Get-Content $packagePath -Raw | ConvertFrom-Json
        
        Write-Host "  Extension: $($packageContent.displayName)" -ForegroundColor White
        Write-Host "  Version: $($packageContent.version)" -ForegroundColor White
        Write-Host "  Publisher: $($packageContent.publisher)" -ForegroundColor White
        Write-Host "  Description: $($packageContent.description)" -ForegroundColor White
        
        # Save analysis
        $packageContent | ConvertTo-Json -Depth 10 | Out-File "$OutputDir\package-analysis.json" -Encoding UTF8
        
        return $packageContent
    }
    else {
        Write-Warning "package.json not found"
        return $null
    }
}

# Function to analyze file structure
function Analyze-FileStructure {
    param([string]$Path)
    
    Write-Host "`n2. Analyzing file structure..." -ForegroundColor Green
    
    $structure = @{}
    
    try {
        Get-ChildItem -Path $Path -Recurse -File | ForEach-Object {
            $relativePath = $_.FullName.Replace($Path, "").TrimStart('\')
            $structure[$relativePath] = @{
                Size = $_.Length
                Extension = $_.Extension
                LastModified = $_.LastWriteTime
            }
        }
        
        Write-Host "  Found $($structure.Count) files" -ForegroundColor White
        
        # Save structure
        $structure | ConvertTo-Json -Depth 10 | Out-File "$OutputDir\file-structure.json" -Encoding UTF8
        
        return $structure
    }
    catch {
        Write-Warning "Failed to analyze file structure: $_"
        return @{}
    }
}

# Function to analyze main extension file
function Analyze-ExtensionCode {
    param([string]$Path)
    
    Write-Host "`n3. Analyzing extension code..." -ForegroundColor Green
    
    $extensionFile = Join-Path $Path "out\extension.js"
    if (-not (Test-Path $extensionFile)) {
        Write-Warning "Main extension file not found at: $extensionFile"
        return @{}
    }
    
    try {
        $code = Get-Content $extensionFile -Raw
        Write-Host "  Extension file size: $($code.Length) characters" -ForegroundColor White
        
        # Basic code analysis
        $analysis = @{
            TotalSize = $code.Length
            TotalLines = ($code -split "`n").Count
            Functions = @()
            Imports = @()
            VSCodeAPIs = @()
            StorageOps = @()
            NetworkCalls = @()
        }
        
        # Extract function names (basic pattern matching)
        $functionMatches = [regex]::Matches($code, 'function\s+([a-zA-Z_$][a-zA-Z0-9_$]*)')
        $analysis.Functions = $functionMatches | ForEach-Object { $_.Groups[1].Value } | Sort-Object -Unique
        
        # Extract require/import statements
        $requireMatches = [regex]::Matches($code, 'require\([''"`]([^''"`]+)[''"`]\)')
        $analysis.Imports = $requireMatches | ForEach-Object { $_.Groups[1].Value } | Sort-Object -Unique
        
        # Extract VS Code API calls
        $vscodeMatches = [regex]::Matches($code, 'vscode\.[a-zA-Z_$][a-zA-Z0-9_$.]*')
        $analysis.VSCodeAPIs = $vscodeMatches | ForEach-Object { $_.Value } | Sort-Object -Unique
        
        # Extract storage operations
        $storageMatches = [regex]::Matches($code, '(localStorage|sessionStorage|globalState|workspaceState)\.[a-zA-Z_$][a-zA-Z0-9_$]*')
        $analysis.StorageOps = $storageMatches | ForEach-Object { $_.Value } | Sort-Object -Unique
        
        # Extract network calls
        $networkMatches = [regex]::Matches($code, '(fetch|axios|request|https?)\s*\(')
        $analysis.NetworkCalls = $networkMatches | ForEach-Object { $_.Value } | Sort-Object -Unique
        
        Write-Host "  Functions found: $($analysis.Functions.Count)" -ForegroundColor White
        Write-Host "  VS Code APIs: $($analysis.VSCodeAPIs.Count)" -ForegroundColor White
        Write-Host "  Storage operations: $($analysis.StorageOps.Count)" -ForegroundColor White
        Write-Host "  Network calls: $($analysis.NetworkCalls.Count)" -ForegroundColor White
        
        # Save analysis
        $analysis | ConvertTo-Json -Depth 10 | Out-File "$OutputDir\code-analysis.json" -Encoding UTF8
        
        # Save beautified code (basic formatting)
        $beautifiedCode = $code -replace ';', ";\n" -replace '\{', "{\n" -replace '\}', "\n}\n"
        $beautifiedCode | Out-File "$OutputDir\extension-beautified.js" -Encoding UTF8
        
        return @{
            Code = $code
            Analysis = $analysis
        }
    }
    catch {
        Write-Warning "Failed to analyze extension code: $_"
        return @{}
    }
}

# Function to extract strings and patterns
function Extract-Patterns {
    param([string]$Code)
    
    Write-Host "`n4. Extracting strings and patterns..." -ForegroundColor Green
    
    $patterns = @{
        Strings = @()
        URLs = @()
        FilePaths = @()
        AuthTokens = @()
        LicenseKeys = @()
        MachineIds = @()
        TrialData = @()
        APIEndpoints = @()
    }
    
    # Extract string literals
    $stringMatches = [regex]::Matches($code, '[''"`]([^''"`\n]{3,})[''"`]')
    $allStrings = $stringMatches | ForEach-Object { $_.Groups[1].Value } | Where-Object { $_.Length -gt 3 }
    $patterns.Strings = $allStrings | Sort-Object -Unique
    
    # Filter specific patterns
    $patterns.URLs = $patterns.Strings | Where-Object { $_ -match '^https?://' -or $_ -match 'api\.' -or $_ -match '\.com' }
    $patterns.FilePaths = $patterns.Strings | Where-Object { $_ -match '[/\\]' -or $_ -match '\.(json|js|exe)$' }
    $patterns.AuthTokens = $patterns.Strings | Where-Object { $_ -match 'token|auth|key|secret' }
    $patterns.LicenseKeys = $patterns.Strings | Where-Object { $_ -match 'license|trial|subscription|premium' }
    $patterns.MachineIds = $patterns.Strings | Where-Object { $_ -match 'machine|device|fingerprint|uuid|guid' }
    $patterns.TrialData = $patterns.Strings | Where-Object { $_ -match 'trial|expire|remaining|days|count' }
    $patterns.APIEndpoints = $patterns.Strings | Where-Object { $_ -match '/api/|/v1/|endpoint' }
    
    Write-Host "  Total strings: $($patterns.Strings.Count)" -ForegroundColor White
    Write-Host "  URLs found: $($patterns.URLs.Count)" -ForegroundColor White
    Write-Host "  Auth-related: $($patterns.AuthTokens.Count)" -ForegroundColor White
    Write-Host "  License-related: $($patterns.LicenseKeys.Count)" -ForegroundColor White
    Write-Host "  Machine ID-related: $($patterns.MachineIds.Count)" -ForegroundColor White
    Write-Host "  Trial-related: $($patterns.TrialData.Count)" -ForegroundColor White
    
    # Save patterns
    $patterns | ConvertTo-Json -Depth 10 | Out-File "$OutputDir\extracted-patterns.json" -Encoding UTF8
    
    return $patterns
}

# Function to analyze detection mechanisms
function Analyze-DetectionMechanisms {
    param([string]$Code)
    
    Write-Host "`n5. Analyzing detection mechanisms..." -ForegroundColor Green
    
    $mechanisms = @{
        TrialDetection = @{
            TrialFiles = @()
            TrialChecks = @()
            DateValidation = @()
            UsageCounters = @()
        }
        AccountDetection = @{
            MachineIdChecks = @()
            UserIdentification = @()
            DeviceFingerprinting = @()
        }
        UsageTracking = @{
            PromptCounting = @()
            SessionTracking = @()
            Analytics = @()
        }
    }
    
    # Trial detection patterns
    $trialFileMatches = [regex]::Matches($code, '[''"`](.*trial.*\.json)[''"`]', [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)
    $mechanisms.TrialDetection.TrialFiles = $trialFileMatches | ForEach-Object { $_.Groups[1].Value }
    
    $trialCheckPatterns = @('trial.*expired', 'trial.*remaining', 'trial.*days', 'isTrialExpired', 'checkTrial')
    foreach ($pattern in $trialCheckPatterns) {
        $matches = [regex]::Matches($code, $pattern, [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)
        $mechanisms.TrialDetection.TrialChecks += $matches | ForEach-Object { $_.Value }
    }
    
    # Account detection patterns
    $machineIdPatterns = @('machineId', 'telemetry\.machineId', 'device.*id', 'hardware.*id')
    foreach ($pattern in $machineIdPatterns) {
        $matches = [regex]::Matches($code, $pattern, [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)
        $mechanisms.AccountDetection.MachineIdChecks += $matches | ForEach-Object { $_.Value }
    }
    
    # Usage tracking patterns
    $promptPatterns = @('prompt.*count', 'message.*count', 'request.*count', 'usage.*limit')
    foreach ($pattern in $promptPatterns) {
        $matches = [regex]::Matches($code, $pattern, [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)
        $mechanisms.UsageTracking.PromptCounting += $matches | ForEach-Object { $_.Value }
    }
    
    Write-Host "  Trial files detected: $($mechanisms.TrialDetection.TrialFiles.Count)" -ForegroundColor White
    Write-Host "  Trial checks: $($mechanisms.TrialDetection.TrialChecks.Count)" -ForegroundColor White
    Write-Host "  Machine ID checks: $($mechanisms.AccountDetection.MachineIdChecks.Count)" -ForegroundColor White
    Write-Host "  Prompt counting: $($mechanisms.UsageTracking.PromptCounting.Count)" -ForegroundColor White
    
    # Save mechanisms
    $mechanisms | ConvertTo-Json -Depth 10 | Out-File "$OutputDir\detection-mechanisms.json" -Encoding UTF8
    
    return $mechanisms
}

# Function to generate comprehensive report
function Generate-Report {
    param(
        [object]$PackageInfo,
        [object]$FileStructure,
        [object]$CodeAnalysis,
        [object]$Patterns,
        [object]$Mechanisms
    )

    Write-Host "`n6. Generating comprehensive report..." -ForegroundColor Green

    $report = @{
        Timestamp = (Get-Date).ToString("yyyy-MM-dd HH:mm:ss")
        ExtensionInfo = @{
            Name = $PackageInfo.displayName
            Version = $PackageInfo.version
            Publisher = $PackageInfo.publisher
            Description = $PackageInfo.description
        }
        Summary = @{
            TotalFiles = $FileStructure.Count
            CodeSize = $CodeAnalysis.Analysis.TotalSize
            FunctionsFound = $CodeAnalysis.Analysis.Functions.Count
            StringsExtracted = $Patterns.Strings.Count
            DetectionMechanisms = @{
                TrialDetection = $Mechanisms.TrialDetection.TrialFiles.Count + $Mechanisms.TrialDetection.TrialChecks.Count
                AccountDetection = $Mechanisms.AccountDetection.MachineIdChecks.Count
                UsageTracking = $Mechanisms.UsageTracking.PromptCounting.Count
            }
        }
        KeyFindings = @{
            TrialFiles = $Mechanisms.TrialDetection.TrialFiles
            MachineIdUsage = $Mechanisms.AccountDetection.MachineIdChecks
            PromptTracking = $Mechanisms.UsageTracking.PromptCounting
            SuspiciousURLs = $Patterns.URLs
            AuthRelatedStrings = $Patterns.AuthTokens
            LicenseStrings = $Patterns.LicenseKeys
        }
        Recommendations = @()
    }

    # Generate recommendations
    if ($Mechanisms.TrialDetection.TrialFiles.Count -gt 0) {
        $report.Recommendations += @{
            Type = "Trial Bypass"
            Description = "Extension uses trial files for license validation"
            Action = "Remove or modify trial tracking files: $($Mechanisms.TrialDetection.TrialFiles -join ', ')"
        }
    }

    if ($Mechanisms.AccountDetection.MachineIdChecks.Count -gt 0) {
        $report.Recommendations += @{
            Type = "Machine ID Reset"
            Description = "Extension tracks machine identification"
            Action = "Reset VS Code telemetry machine ID and device identifiers"
        }
    }

    if ($Mechanisms.UsageTracking.PromptCounting.Count -gt 0) {
        $report.Recommendations += @{
            Type = "Usage Reset"
            Description = "Extension tracks usage and prompt counts"
            Action = "Clear usage tracking data and reset counters"
        }
    }

    # Save comprehensive report
    $report | ConvertTo-Json -Depth 10 | Out-File "$OutputDir\decompilation-report.json" -Encoding UTF8

    # Generate human-readable report
    $readableReport = @"
# Augment Extension Decompilation Report

## Extension Information
- **Name**: $($report.ExtensionInfo.Name)
- **Version**: $($report.ExtensionInfo.Version)
- **Publisher**: $($report.ExtensionInfo.Publisher)
- **Analysis Date**: $($report.Timestamp)

## Summary
- **Total Files**: $($report.Summary.TotalFiles)
- **Code Size**: $($report.Summary.CodeSize) characters
- **Functions Found**: $($report.Summary.FunctionsFound)
- **Strings Extracted**: $($report.Summary.StringsExtracted)

## Detection Mechanisms
- **Trial Detection**: $($report.Summary.DetectionMechanisms.TrialDetection) instances
- **Account Detection**: $($report.Summary.DetectionMechanisms.AccountDetection) instances
- **Usage Tracking**: $($report.Summary.DetectionMechanisms.UsageTracking) instances

## Key Findings

### Trial Files Detected
$($report.KeyFindings.TrialFiles | ForEach-Object { "- $_" } | Out-String)

### Machine ID Usage
$($report.KeyFindings.MachineIdUsage | ForEach-Object { "- $_" } | Out-String)

### Prompt Tracking
$($report.KeyFindings.PromptTracking | ForEach-Object { "- $_" } | Out-String)

### Suspicious URLs
$($report.KeyFindings.SuspiciousURLs | ForEach-Object { "- $_" } | Out-String)

### Authentication Related Strings
$($report.KeyFindings.AuthRelatedStrings | ForEach-Object { "- $_" } | Out-String)

### License Related Strings
$($report.KeyFindings.LicenseStrings | ForEach-Object { "- $_" } | Out-String)

## Recommendations
$($report.Recommendations | ForEach-Object {
    "### $($_.Type)`n- **Description**: $($_.Description)`n- **Action**: $($_.Action)`n"
} | Out-String)

## Files Generated
- `decompilation-report.json` - Complete analysis data
- `package-analysis.json` - Package.json analysis
- `file-structure.json` - Extension file structure
- `code-analysis.json` - Code analysis results
- `extracted-patterns.json` - Extracted strings and patterns
- `detection-mechanisms.json` - Detection mechanism analysis
- `extension-beautified.js` - Beautified extension code
- `ANALYSIS-REPORT.md` - This human-readable report

## How Augment Works (Based on Analysis)

### Trial System
Augment uses JSON files to track trial status and expiration dates. The extension checks these files on startup and during usage to enforce trial limitations.

### Account Detection
The extension uses VS Code's telemetry machine ID and other device identifiers to track unique users and prevent multiple account creation.

### Usage Tracking
Prompt counts and API usage are tracked and stored locally, with limits enforced based on subscription tier.

### Bypass Methods
1. **Trial Reset**: Remove trial tracking files and reset timestamps
2. **Machine ID Reset**: Generate new machine identification values
3. **Usage Reset**: Clear usage counters and tracking data
4. **File Cleanup**: Remove all extension-related storage and cache files

"@

    $readableReport | Out-File "$OutputDir\ANALYSIS-REPORT.md" -Encoding UTF8

    Write-Host "  Report generated successfully" -ForegroundColor White

    return $report
}

# Main execution
try {
    if (-not (Test-Path $ExtensionPath)) {
        Write-Error "Extension path does not exist: $ExtensionPath"
        exit 1
    }

    Write-Host "Starting decompilation process..." -ForegroundColor Yellow

    # Step 1: Analyze package.json
    $packageInfo = Analyze-PackageJson -Path $ExtensionPath

    # Step 2: Analyze file structure
    $fileStructure = Analyze-FileStructure -Path $ExtensionPath

    # Step 3: Analyze extension code
    $codeAnalysis = Analyze-ExtensionCode -Path $ExtensionPath

    # Step 4: Extract patterns
    $patterns = Extract-Patterns -Code $codeAnalysis.Code

    # Step 5: Analyze detection mechanisms
    $mechanisms = Analyze-DetectionMechanisms -Code $codeAnalysis.Code

    # Step 6: Generate comprehensive report
    $report = Generate-Report -PackageInfo $packageInfo -FileStructure $fileStructure -CodeAnalysis $codeAnalysis -Patterns $patterns -Mechanisms $mechanisms

    Write-Host "`n=== DECOMPILATION COMPLETED ===" -ForegroundColor Green
    Write-Host "Results saved to: $OutputDir" -ForegroundColor Yellow
    Write-Host "Check ANALYSIS-REPORT.md for human-readable results" -ForegroundColor Yellow

    # Display key findings
    Write-Host "`n=== KEY FINDINGS ===" -ForegroundColor Cyan
    Write-Host "Trial files found: $($report.KeyFindings.TrialFiles.Count)" -ForegroundColor White
    Write-Host "Machine ID checks: $($report.KeyFindings.MachineIdUsage.Count)" -ForegroundColor White
    Write-Host "Usage tracking: $($report.KeyFindings.PromptTracking.Count)" -ForegroundColor White
    Write-Host "Suspicious URLs: $($report.KeyFindings.SuspiciousURLs.Count)" -ForegroundColor White

    Write-Host "`n=== NEXT STEPS ===" -ForegroundColor Cyan
    Write-Host "1. Review the generated reports in $OutputDir" -ForegroundColor White
    Write-Host "2. Check extension-beautified.js for detailed code analysis" -ForegroundColor White
    Write-Host "3. Use the cleanup script to remove Augment traces" -ForegroundColor White
    Write-Host "4. Reset machine identification to bypass detection" -ForegroundColor White

}
catch {
    Write-Error "Decompilation failed: $_"
    exit 1
}
