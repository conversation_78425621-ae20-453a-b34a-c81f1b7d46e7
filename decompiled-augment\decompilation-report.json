{"timestamp": "2025-06-18T00:44:41.508Z", "extension": {"name": "vscode-augment", "displayName": "Augment", "publisher": "Augment", "repository": {}, "private": true, "preview": false, "license": "https://www.augmentcode.com/terms-of-service", "description": "Augment yourself with the best AI pair programmer", "version": "0.482.1", "engines": {"vscode": "^1.82.0", "node": ">= 18.15.0", "npm": "please-use-pnpm", "yarn": "please-use-pnpm", "pnpm": "9"}, "categories": ["AI", "Cha<PERSON>", "Programming Languages", "Snippets"], "activationEvents": ["onStartupFinished"], "icon": "icon.png", "galleryBanner": {"color": "#000000", "theme": "dark"}, "main": "./out/extension.js", "contributes": {"customEditors": [{"viewType": "rules.augment", "displayName": "Augment Rules Viewer", "selector": [{"filenamePattern": "**/.augment/rules/**/*.md"}], "priority": "default"}, {"viewType": "memories.augment", "displayName": "Augment Memories Viewer", "selector": [{"filenamePattern": "**/workspaceStorage/*/Augment.vscode-augment/Augment-Memories"}], "priority": "default"}], "configuration": [{"title": "Augment", "properties": {"augment.completions.enableAutomaticCompletions": {"type": "boolean", "order": 0, "default": true, "description": "Provide automatic inline code completions (manual code completions are always available)."}, "augment.completions.enableQuickSuggestions": {"type": "boolean", "order": 1, "default": true, "description": "Add Augment to the IntelliSense pop-up suggestions."}, "augment.completions.disableCompletionsByLanguage": {"type": "array", "order": 2, "default": ["git-commit", "scminput"], "markdownDescription": "Disable completions by [language identifiers](https://code.visualstudio.com/docs/languages/identifiers).", "items": {"type": "string"}, "uniqueItems": true}, "augment.enableEmptyFileHint": {"type": "boolean", "order": 3, "default": true, "description": "Display a hint to use Augment Chat when an empty file is open."}, "augment.conflictingCodingAssistantCheck": {"type": "boolean", "order": 4, "default": true, "description": "Check for conflicting coding assistants when starting up and installing extensions."}, "augment.advanced": {"type": "object", "order": 99999, "default": {}, "properties": {"apiToken": {"type": "string", "default": "", "description": "API token for Augment access."}, "completionURL": {"type": "string", "default": "", "description": "URL of completion server."}, "completions": {"type": "object", "default": {}, "properties": {"timeoutMs": {"default": 800, "type": ["number", "null"], "description": "The default timeout for completions (in milliseconds)."}, "maxWaitMs": {"default": 1600, "type": ["number", "null"], "description": "The max timeout for completions items (in milliseconds). This allows Augment to retry completions that are cancelled due to changes in the editor."}, "addIntelliSenseSuggestions": {"default": true, "type": "boolean", "description": "Enable completions in the intellisense pop-up."}}}, "mcpServers": {"type": "array", "default": [{}], "items": {"type": "object", "properties": {"command": {"type": "string", "description": "The command to run the MCP server"}, "args": {"type": "array", "items": {"type": "string"}, "description": "Arguments to pass to the MCP server command"}, "timeoutMs": {"type": "number", "description": "Timeout in milliseconds for MCP server operations"}, "env": {"type": "object", "additionalProperties": {"type": "string"}, "description": "Dictionary of Environment variables to set for the MCP server"}}}, "description": "List of MCP server configurations"}, "integrations": {"type": "object", "default": {}, "properties": {"atlassian": {"type": "object", "default": {}, "properties": {"serverUrl": {"type": "string", "default": "", "description": "Atlassian server URL"}, "personalApiToken": {"type": "string", "default": "", "description": "Personal API token for Atlassian"}, "username": {"type": "string", "default": "", "description": "Atlassian username"}}}, "notion": {"type": "object", "default": {}, "properties": {"apiToken": {"type": "string", "default": "", "description": "API token for Notion"}}}, "linear": {"type": "object", "default": {}, "properties": {"apiToken": {"type": "string", "default": "", "description": "API token for Linear"}}}, "github": {"type": "object", "default": {}, "properties": {"apiToken": {"type": "string", "default": "", "description": "API token for GitHub"}}}}, "description": "Integration configurations for third-party services"}}}}}, {"title": "Next Edit", "properties": {"augment.nextEdit.enableBackgroundSuggestions": {"type": "boolean", "order": 0, "default": true, "description": "Enable Next Edit to run in the background and suggest changes in the editor."}, "augment.nextEdit.enableGlobalBackgroundSuggestions": {"type": "boolean", "order": 1, "default": false, "description": "Enable Next Edit to hint changes in files beyond the active editor tab."}, "augment.nextEdit.enableAutoApply": {"type": "boolean", "order": 2, "default": true, "description": "Automatically apply suggestions when you jump to them."}, "augment.nextEdit.showDiffInHover": {"type": "boolean", "order": 3, "default": false, "description": "Show a diff of the suggested change in the hover."}, "augment.nextEdit.highlightSuggestionsInTheEditor": {"type": "boolean", "order": 4, "default": false, "description": "Highlight all lines with a suggestion in addition to showing gutter icons and gray hint-text."}}}, {"title": "Experimental", "properties": {"augment.chat.userGuidelines": {"type": "string", "order": 5, "default": "", "description": "Edit this field on the Augment settings page."}}}], "commands": [{"command": "vscode-augment.autofixCommand", "title": "Augment: Autofix", "category": "Augment"}, {"category": "Augment", "command": "vscode-augment.internal-dv.o", "title": "View Diff"}, {"category": "Augment", "command": "vscode-augment.internal-dv.i", "title": "Code Instruction"}, {"category": "Augment", "command": "vscode-augment.internal-dv.aac", "title": "Accept All Chunks"}, {"category": "Augment", "command": "vscode-augment.internal-dv.afc", "title": "Accept Focused Chunk"}, {"category": "Augment", "command": "vscode-augment.internal-dv.rfc", "title": "Reject Focused Chunk"}, {"category": "Augment", "command": "vscode-augment.internal-dv.fpc", "title": "Focus Previous Chunk"}, {"category": "Augment", "command": "vscode-augment.internal-dv.fnc", "title": "Focus Next Chunk"}, {"category": "Augment", "command": "vscode-augment.internal-dv.c", "title": "Close Diff View"}, {"category": "Augment", "command": "vscode-augment.insertCompletion", "title": "Insert Completion"}, {"category": "Augment", "command": "vscode-augment.settings", "title": "$(gear) Edit Settings..."}, {"category": "Augment", "command": "vscode-augment.keyboard-shortcuts", "title": "$(keyboard) Edit Keyboard Shortcuts..."}, {"category": "Augment", "command": "vscode-augment.showDocs", "title": "Help", "icon": "$(question)"}, {"category": "Augment", "command": "vscode-augment.showAccountPage", "title": "Account & Billing", "icon": "$(note)"}, {"category": "Augment", "command": "vscode-augment.toggleAutomaticCompletionSetting", "title": "Toggle Automatic Completions"}, {"command": "vscode-augment.manageAccountCommunity", "title": "$(accounts-view-bar-icon) Manage Account (Community)", "when": "augment.userTier == 'community'"}, {"command": "vscode-augment.manageAccountProfessional", "title": "$(accounts-view-bar-icon) Manage Account (Self-Serve)", "when": "augment.userTier == 'professional'"}, {"command": "vscode-augment.manageAccountEnterprise", "title": "$(accounts-view-bar-icon) Manage Account (Enterprise)", "when": "augment.userTier == 'enterprise'"}, {"category": "Augment", "command": "vscode-augment.signIn", "title": "$(sign-in) Sign In"}, {"category": "Augment", "command": "vscode-augment.signOut", "title": "$(sign-out) Sign Out"}, {"category": "Augment", "command": "vscode-augment.chat.slash.fix", "title": "Fix using Augment"}, {"category": "Augment", "command": "vscode-augment.chat.slash.explain", "title": "Explain using Augment"}, {"category": "Augment", "command": "vscode-augment.chat.slash.test", "title": "Write test using Augment"}, {"category": "Augment", "command": "vscode-augment.chat.slash.document", "title": "Document using Augment"}, {"category": "Augment", "command": "vscode-augment.showHistoryPanel", "title": "$(history) Show History"}, {"category": "Augment", "command": "vscode-augment.copySessionId", "title": "Copy Session ID"}, {"category": "Augment", "command": "_vscode-augment.showSidebarWorkspaceContext", "title": "Manage Workspace Context", "icon": "$(folder-opened)"}, {"category": "Augment", "command": "_vscode-augment.showSidebarChat", "title": "Show Chat", "icon": "$(comment-discussion)"}, {"category": "Augment", "command": "vscode-augment.generateCommitMessage", "title": "Generate Commit Message with Augment", "icon": "$(sparkle)", "when": "vscode-augment.enableGenerateCommitMessage && gitOpenRepositoryCount != 0"}, {"category": "Augment", "command": "vscode-augment.showSettingsPanel", "title": "Settings", "icon": "$(settings-gear)"}, {"category": "Augment", "command": "vscode-augment.showAugmentCommands", "title": "Show Augment Commands", "icon": "$(menu)"}, {"category": "Augment", "command": "vscode-augment.focusAugmentPanel", "title": "$(layout-sidebar-left) Open Augment"}, {"category": "Augment", "command": "vscode-augment.startNewChat", "title": "Start New Chat"}, {"category": "Augment", "command": "vscode-augment.clear-recent-editing-history", "title": "Clear Recent Editing History"}, {"category": "Augment", "command": "vscode-augment.showRemoteAgentsPanel", "title": "Show Remote Agents Panel", "when": "vscode-augment.featureFlags.enableRemoteAgents"}, {"category": "Augment", "command": "vscode-augment.openSshConfig", "title": "Open Augment SSH Config", "when": "vscode-augment.featureFlags.enableRemoteAgents"}, {"category": "Augment", "command": "vscode-augment.next-edit.force", "title": "View Nearby Next Edit Suggestions (Forced)", "when": "vscode-augment.enableNextEdit && (editorTextFocus || editorHoverFocused)"}, {"category": "Augment", "command": "vscode-augment.next-edit.toggle-panel-horizontal-split", "title": "Toggle Side Panel Split", "icon": "$(split-horizontal)", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.enablePanel"}, {"category": "Augment", "command": "vscode-augment.next-edit.update", "title": "Update Next Edit Suggestions", "icon": {"light": "media/next-edit/nextedit-update-light.svg", "dark": "media/next-edit/nextedit-update-dark.svg"}, "when": "vscode-augment.enableNextEdit"}, {"command": "_vscode-augment.next-edit.update.loading", "title": "Updating Suggestions...", "icon": {"light": "media/next-edit/nextedit-update-loading-light.svg", "dark": "media/next-edit/nextedit-update-loading-dark.svg"}, "when": "vscode-augment.enableNextEdit"}, {"command": "_vscode-augment.next-edit.update.disabled-no-changes", "title": "No Updates Available", "icon": {"light": "media/next-edit/nextedit-update-disabled-light.svg", "dark": "media/next-edit/nextedit-update-disabled-dark.svg"}, "when": "vscode-augment.enableNextEdit"}, {"command": "_vscode-augment.next-edit.update.disabled-cached", "title": "Suggestions Up To Date", "icon": {"light": "media/next-edit/nextedit-update-complete-light.svg", "dark": "media/next-edit/nextedit-update-complete-dark.svg"}, "when": "vscode-augment.enableNextEdit"}, {"command": "vscode-augment.next-edit.learn-more", "title": "Learn about Next Edit...", "when": "vscode-augment.enableNextEdit"}, {"category": "Augment", "command": "vscode-augment.next-edit.open-panel", "title": "Open Next Edit Panel", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.enablePanel"}, {"category": "Augment", "command": "vscode-augment.next-edit.background.accept", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canAccept && (((editorTextFocus || editorHoverFocused) && !suggestWidgetVisible) || activePanel == 'workbench.view.extension.augment-panel' || activeViewlet == 'workbench.view.extension.augment-panel')", "title": "Accept Suggestion"}, {"category": "Augment", "command": "_vscode-augment.next-edit.background.accept-code-action", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canAcceptCodeAction", "title": "Accept Suggestion"}, {"category": "Augment", "command": "vscode-augment.next-edit.background.accept-all", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canAcceptAll", "title": "Accept All Suggestions"}, {"category": "Augment", "command": "vscode-augment.next-edit.background.reject", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canReject && (((editorTextFocus || editorHoverFocused) && !suggestWidgetVisible) || activePanel == 'workbench.view.extension.augment-panel' || activeViewlet == 'workbench.view.extension.augment-panel')", "title": "Reject Suggestion"}, {"category": "Augment", "command": "vscode-augment.next-edit.background.reject-all", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canRejectAll", "title": "Reject All Suggestions"}, {"category": "Augment", "command": "vscode-augment.next-edit.background.dismiss", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canDismiss && (((editorTextFocus || editorHoverFocused) && !suggestWidgetVisible) || activePanel == 'workbench.view.extension.augment-panel' || activeViewlet == 'workbench.view.extension.augment-panel')", "title": "Dismiss Suggestion Highlights"}, {"category": "Augment", "command": "vscode-augment.next-edit.background.next", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canNextSmart", "title": "Go to Next Suggestion (Smart)"}, {"category": "Augment", "command": "vscode-augment.next-edit.background.next-forward", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canNext", "title": "Go to Next Suggestion", "icon": {"light": "media/next-edit/right-light-enabled.svg", "dark": "media/next-edit/right-dark-enabled.svg"}}, {"category": "Augment", "command": "vscode-augment.next-edit.background.previous", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canPrevious", "title": "Go to Previous Suggestion", "icon": {"light": "media/next-edit/left-light-enabled.svg", "dark": "media/next-edit/left-dark-enabled.svg"}}, {"category": "Augment", "command": "_vscode-augment.next-edit.background.next-forward.disabled", "title": "Go to Next Suggestion", "icon": {"light": "media/next-edit/right-light-disabled.svg", "dark": "media/next-edit/right-dark-disabled.svg"}, "when": "vscode-augment.enableNextEdit"}, {"category": "Augment", "command": "_vscode-augment.next-edit.background.previous.disabled", "title": "Go to Previous Suggestion", "icon": {"light": "media/next-edit/left-light-disabled.svg", "dark": "media/next-edit/left-dark-disabled.svg"}, "when": "vscode-augment.enableNextEdit"}, {"category": "Augment", "command": "_vscode-augment.next-edit.background.open", "title": "Augment Next Edit: View Suggestion", "when": "vscode-augment.enableNextEdit"}, {"category": "Augment", "command": "vscode-augment.next-edit.toggle-bg", "title": "Toggle Background Suggestions", "when": "vscode-augment.enableNextEdit"}, {"category": "Augment", "command": "vscode-augment.next-edit.enable-bg", "title": "Enable Background Suggestions", "when": "vscode-augment.enableNextEdit && !vscode-augment.enableNextEditBackgroundSuggestions"}, {"category": "Augment", "command": "vscode-augment.next-edit.disable-bg", "title": "Disable Background Suggestions", "when": "vscode-augment.enableNextEdit && vscode-augment.enableNextEditBackgroundSuggestions"}, {"category": "Augment", "command": "vscode-augment.next-edit.toggle-all-highlights", "title": "Toggle Suggestion Highlights", "when": "vscode-augment.enableNextEdit"}, {"category": "Augment", "command": "vscode-augment.next-edit.settings", "title": "Next Edit Settings...", "when": "vscode-augment.enableNextEdit"}], "icons": {"augment-icon-simple": {"description": "Augment logo (simple)", "default": {"fontPath": "augment-icon-font.woff", "fontCharacter": "\\E900"}}, "augment-icon-smile": {"description": "Augment logo (smile)", "default": {"fontPath": "augment-icon-font.woff", "fontCharacter": "\\E901"}}, "augment-icon-zero": {"description": "Augment logo (zero)", "default": {"fontPath": "augment-icon-font.woff", "fontCharacter": "\\E902"}}, "augment-icon-error": {"description": "Augment logo (error)", "default": {"fontPath": "augment-icon-font.woff", "fontCharacter": "\\E903"}}, "augment-icon-closed-eyes": {"description": "Augment logo (closed eyes)", "default": {"fontPath": "augment-icon-font.woff", "fontCharacter": "\\E904"}}, "augment-icon-dots": {"description": "Augment logo (dots)", "default": {"fontPath": "augment-icon-font.woff", "fontCharacter": "\\E905"}}, "augment-kb-z": {"description": "Keyboard icon Z", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f101"}}, "augment-kb-y": {"description": "Keyboard icon Y", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f102"}}, "augment-kb-x": {"description": "Keyboard icon X", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f103"}}, "augment-kb-win": {"description": "Keyboard icon Win", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f104"}}, "augment-kb-w": {"description": "Keyboard icon W", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f105"}}, "augment-kb-v": {"description": "Keyboard icon V", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f106"}}, "augment-kb-u": {"description": "Keyboard icon U", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f107"}}, "augment-kb-tab": {"description": "Keyboard icon Tab", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f108"}}, "augment-kb-t": {"description": "Keyboard icon T", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f109"}}, "augment-kb-shift": {"description": "Keyboard icon Shift", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f10a"}}, "augment-kb-semicolon": {"description": "Keyboard icon Semicolon", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f10b"}}, "augment-kb-s": {"description": "Keyboard icon S", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f10c"}}, "augment-kb-return": {"description": "Keyboard icon Return", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f10d"}}, "augment-kb-r": {"description": "Keyboard icon R", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f10e"}}, "augment-kb-q": {"description": "Keyboard icon Q", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f10f"}}, "augment-kb-p": {"description": "Keyboard icon P", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f110"}}, "augment-kb-option": {"description": "Keyboard icon Option", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f111"}}, "augment-kb-o": {"description": "Keyboard icon O", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f112"}}, "augment-kb-n": {"description": "Keyboard icon N", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f113"}}, "augment-kb-meta": {"description": "Keyboard icon <PERSON><PERSON>", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f114"}}, "augment-kb-m": {"description": "Keyboard icon M", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f115"}}, "augment-kb-l": {"description": "Keyboard icon L", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f116"}}, "augment-kb-k": {"description": "Keyboard icon K", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f117"}}, "augment-kb-j": {"description": "Keyboard icon J", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f118"}}, "augment-kb-i": {"description": "Keyboard icon I", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f119"}}, "augment-kb-h": {"description": "Keyboard icon H", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f11a"}}, "augment-kb-g": {"description": "Keyboard icon G", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f11b"}}, "augment-kb-f": {"description": "Keyboard icon F", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f11c"}}, "augment-kb-escape": {"description": "Keyboard icon Escape", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f11d"}}, "augment-kb-e": {"description": "Keyboard icon E", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f11e"}}, "augment-kb-delete": {"description": "Keyboard icon Delete", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f11f"}}, "augment-kb-d": {"description": "Keyboard icon D", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f120"}}, "augment-kb-ctrl": {"description": "Keyboard icon Ctrl", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f121"}}, "augment-kb-control": {"description": "Keyboard icon Control", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f122"}}, "augment-kb-command": {"description": "Keyboard icon Command", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f123"}}, "augment-kb-c": {"description": "Keyboard icon C", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f124"}}, "augment-kb-backspace": {"description": "Keyboard icon Backspace", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f125"}}, "augment-kb-b": {"description": "Keyboard icon B", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f126"}}, "augment-kb-alt": {"description": "Keyboard icon Alt", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f127"}}, "augment-kb-a": {"description": "Keyboard icon A", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f128"}}}, "keybindings": [{"command": "vscode-augment.insertCompletion", "when": "editorHasCompletionItemProvider && editorTextFocus && !editorR<PERSON>only", "key": "ctrl-f11", "mac": "ctrl-f11"}, {"command": "vscode-augment.focusAugmentPanel", "key": "ctrl-alt-i", "mac": "cmd-ctrl-i"}, {"command": "vscode-augment.focusAugmentPanel", "key": "ctrl-l", "mac": "cmd-l"}, {"command": "vscode-augment.internal-dv.o", "when": "vscode-augment.enableDebugFeatures", "key": "ctrl-alt-o", "mac": "cmd-ctrl-o"}, {"command": "vscode-augment.internal-dv.i", "when": "(vscode-augment.internal-dv.enabled || vscode-augment.enableDebugFeatures) && !editorReadonly && !terminalFocus", "key": "ctrl-i", "mac": "cmd-i"}, {"command": "vscode-augment.internal-dv.aac", "when": "(vscode-augment.internal-dv.enabled || vscode-augment.enableDebugFeatures) && activeWebviewPanelId == augmentDiffView && vscode-augment.internal-dv.panel-focused", "key": "ctrl-enter", "mac": "cmd-enter"}, {"command": "vscode-augment.internal-dv.afc", "when": "(vscode-augment.internal-dv.enabled || vscode-augment.enableDebugFeatures) && activeWebviewPanelId == augmentDiffView && vscode-augment.internal-dv.panel-focused", "key": "enter", "mac": "enter"}, {"command": "vscode-augment.internal-dv.rfc", "when": "(vscode-augment.internal-dv.enabled || vscode-augment.enableDebugFeatures) && activeWebviewPanelId == augmentDiffView && vscode-augment.internal-dv.panel-focused", "key": "backspace"}, {"command": "vscode-augment.internal-dv.fpc", "when": "(vscode-augment.internal-dv.enabled || vscode-augment.enableDebugFeatures) && activeWebviewPanelId == augmentDiffView && vscode-augment.internal-dv.panel-focused", "key": "up"}, {"command": "vscode-augment.internal-dv.fnc", "when": "(vscode-augment.internal-dv.enabled || vscode-augment.enableDebugFeatures) && activeWebviewPanelId == augmentDiffView && vscode-augment.internal-dv.panel-focused", "key": "down"}, {"command": "vscode-augment.internal-dv.c", "when": "(vscode-augment.internal-dv.enabled || vscode-augment.enableDebugFeatures) && activeWebviewPanelId == augmentDiffView && vscode-augment.internal-dv.panel-focused", "key": "escape"}, {"command": "vscode-augment.showAugmentCommands", "key": "ctrl-shift-a", "mac": "cmd-shift-a"}, {"command": "vscode-augment.toggleAutomaticCompletionSetting", "key": "ctrl-alt-a", "mac": "cmd-alt-a"}, {"command": "vscode-augment.showRemoteAgentsPanel", "when": "vscode-augment.featureFlags.enableRemoteAgents", "key": "ctrl-shift-r", "mac": "cmd-shift-r", "args": "keybinding"}, {"command": "vscode-augment.next-edit.background.accept", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canAccept && !editorTextFocus && (activePanel == 'workbench.view.extension.augment-panel' || activeViewlet == 'workbench.view.extension.augment-panel')", "key": "ctrl-y", "mac": "cmd-shift-z", "args": "keybinding"}, {"command": "vscode-augment.next-edit.background.accept", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canAccept && (((editorTextFocus || editorHoverFocused) && !suggestWidgetVisible) || activePanel == 'workbench.view.extension.augment-panel' || activeViewlet == 'workbench.view.extension.augment-panel')", "key": "ctrl-enter", "mac": "cmd-enter", "args": "keybinding"}, {"command": "vscode-augment.next-edit.background.accept", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canAccept && (((editorTextFocus || editorHoverFocused) && !suggestWidgetVisible) || activePanel == 'workbench.view.extension.augment-panel' || activeViewlet == 'workbench.view.extension.augment-panel')", "key": "enter", "mac": "enter", "args": "keybinding"}, {"command": "vscode-augment.next-edit.background.accept", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canAccept && (((editorTextFocus || editorHoverFocused) && !suggestWidgetVisible) || activePanel == 'workbench.view.extension.augment-panel' || activeViewlet == 'workbench.view.extension.augment-panel')", "key": "tab", "mac": "tab", "args": "keybinding"}, {"command": "vscode-augment.next-edit.background.accept-all", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canAcceptAll && augment-next-edit.active", "key": "ctrl-alt-enter", "mac": "cmd-alt-enter", "args": "keybinding"}, {"command": "vscode-augment.next-edit.background.reject", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canReject && (((editorTextFocus || editorHoverFocused) && !suggestWidgetVisible) || activePanel == 'workbench.view.extension.augment-panel' || activeViewlet == 'workbench.view.extension.augment-panel')", "key": "ctrl-backspace", "mac": "cmd-backspace", "args": "keybinding"}, {"command": "vscode-augment.next-edit.background.reject", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canReject && (((editorTextFocus || editorHoverFocused) && !suggestWidgetVisible) || activePanel == 'workbench.view.extension.augment-panel' || activeViewlet == 'workbench.view.extension.augment-panel')", "key": "backspace", "mac": "backspace", "args": "keybinding"}, {"command": "vscode-augment.next-edit.background.reject-all", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canRejectAll && augment-next-edit.active", "key": "ctrl-alt-backspace", "mac": "cmd-alt-backspace", "args": "keybinding"}, {"command": "vscode-augment.next-edit.background.dismiss", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canDismiss && (((editorTextFocus || editorHoverFocused) && !suggestWidgetVisible) || activePanel == 'workbench.view.extension.augment-panel' || activeViewlet == 'workbench.view.extension.augment-panel') && !inlineSuggestionVisible && !editorHasSelection && (!vim.active || vim.mode == 'Normal')", "key": "escape", "mac": "escape", "args": "keybinding"}, {"command": "vscode-augment.next-edit.background.next", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canNextSmart", "key": "ctrl-;", "mac": "cmd-;", "args": "keybinding"}, {"command": "vscode-augment.next-edit.background.next-forward", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canNext", "key": "ctrl-shift-'", "mac": "cmd-shift-'", "args": "keybinding"}, {"command": "vscode-augment.next-edit.background.previous", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canPrevious", "key": "ctrl-shift-;", "mac": "cmd-shift-;", "args": "keybinding"}, {"command": "vscode-augment.next-edit.force", "when": "vscode-augment.enableNextEdit && (editorTextFocus || editorHoverFocused || activePanel == 'workbench.view.extension.augment-panel' || activeViewlet == 'workbench.view.extension.augment-panel')", "key": "ctrl-alt-;", "mac": "cmd-ctrl-;", "args": "keybinding"}, {"command": "vscode-augment.next-edit.open-panel", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.enablePanel", "key": "ctrl-'", "mac": "cmd-'", "args": "keybinding"}, {"command": "_vscode-augment.next-edit.undo-accept-suggestion", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canUndoAcceptSuggestion && !editorTextFocus && (activePanel == 'workbench.view.extension.augment-panel' || activeViewlet == 'workbench.view.extension.augment-panel')", "key": "ctrl-z", "mac": "cmd-z", "args": "keybinding"}], "submenus": [{"id": "vscode-augment.context-submenu", "label": "Send to Augment"}, {"id": "vscode-augment.viewTitleMenuEntryPoint", "label": "Augment Options", "icon": "$(menu)"}, {"id": "vscode-augment.next-edit.editor-action-submenu", "label": "Next Edit", "icon": {"light": "media/next-edit/nextedit-available-light.svg", "dark": "media/next-edit/nextedit-available-dark.svg"}}, {"id": "vscode-augment.next-edit.editor-action-submenu.disabled", "label": "Next Edit", "icon": {"light": "media/next-edit/nextedit-unavailable-light.svg", "dark": "media/next-edit/nextedit-unavailable-dark.svg"}}, {"id": "vscode-augment.next-edit.editor-action-submenu.loading", "label": "Next Edit", "icon": {"light": "media/next-edit/nextedit-loading-light.svg", "dark": "media/next-edit/nextedit-loading-dark.svg"}}, {"id": "vscode-augment.next-edit.panel-submenu", "label": "Next Edit Menu", "icon": "$(ellipsis)"}], "menus": {"view/title": [{"submenu": "vscode-augment.viewTitleMenuEntryPoint", "when": "view == augment-chat && vscode-augment.mainPanel.app == 'chat'", "group": "navigation@0"}, {"command": "vscode-augment.next-edit.update", "when": "view == augment-next-edit && !vscode-augment.nextEdit.global.updating && vscode-augment.nextEdit.global.canUpdate && !vscode-augment.nextEdit.global.updateCached", "group": "navigation@1"}, {"command": "_vscode-augment.next-edit.update.loading", "when": "view == augment-next-edit && vscode-augment.nextEdit.global.updating", "group": "navigation@1"}, {"command": "_vscode-augment.next-edit.update.disabled-no-changes", "when": "view == augment-next-edit && !vscode-augment.nextEdit.global.updating && !vscode-augment.nextEdit.global.canUpdate", "group": "navigation@1"}, {"command": "_vscode-augment.next-edit.update.disabled-cached", "when": "view == augment-next-edit && !vscode-augment.nextEdit.global.updating && vscode-augment.nextEdit.global.canUpdate && vscode-augment.nextEdit.global.updateCached", "group": "navigation@1"}, {"submenu": "vscode-augment.next-edit.panel-submenu", "when": "view == augment-next-edit", "group": "navigation@2"}], "vscode-augment.viewTitleMenuEntryPoint": [{"command": "vscode-augment.showSettingsPanel", "group": "menu@1"}, {"command": "vscode-augment.showDocs", "group": "menu@2"}, {"command": "vscode-augment.showAccountPage", "group": "menu@3"}, {"command": "vscode-augment.signOut", "group": "menu@4"}], "editor/context": [{"submenu": "vscode-augment.context-submenu", "group": "0_augment"}, {"command": "vscode-augment.next-edit.force", "group": "1_modification", "when": "vscode-augment.enableNextEdit"}], "vscode-augment.context-submenu": [{"command": "vscode-augment.focusAugmentPanel", "when": "editorHasSelection"}, {"command": "vscode-augment.chat.slash.explain", "when": "editorHasSelection"}, {"command": "vscode-augment.chat.slash.test", "when": "editorHasSelection"}, {"command": "vscode-augment.chat.slash.fix", "when": "editorHasSelection"}, {"command": "vscode-augment.chat.slash.document", "when": "editorHasSelection"}], "editor/lineNumber/context": [{"command": "_vscode-augment.next-edit.background.open", "when": "vscode-augment.enableNextEdit && editorLineNumber in vscode-augment.nextEdit.linesWithGutterIconActions && resourcePath in vscode-augment.nextEdit.filesWithGutterIconActions", "group": "navigation@0"}], "commandPalette": [{"command": "vscode-augment.internal-dv.o", "when": "vscode-augment.enableDebugFeatures"}, {"command": "vscode-augment.insertCompletion", "when": "!editor<PERSON><PERSON><PERSON><PERSON>"}, {"command": "vscode-augment.toggleAutomaticCompletionSetting"}, {"command": "vscode-augment.signIn", "when": "vscode-augment.useOAuth && !vscode-augment.isLoggedIn"}, {"command": "vscode-augment.signOut", "when": "vscode-augment.useOAuth && vscode-augment.isLoggedIn"}, {"command": "vscode-augment.internal-dv.i", "when": "(vscode-augment.internal-new-instructions.enabled || vscode-augment.enableDebugFeatures) && !editorReadonly && !terminalFocus"}, {"command": "vscode-augment.chat.slash.fix"}, {"command": "vscode-augment.startNewChat"}, {"command": "vscode-augment.focusAugmentPanel", "when": "vscode-augment.enableDebugFeatures || vscode-augment.isLoggedIn"}, {"command": "_vscode-augment.showSidebarChat", "when": "false"}, {"command": "_vscode-augment.showSidebarWorkspaceContext", "when": "false"}, {"command": "vscode-augment.clear-recent-editing-history", "when": "vscode-augment.enableNextEdit"}, {"command": "vscode-augment.generateCommitMessage", "when": "false"}, {"command": "vscode-augment.showSettingsPanel"}, {"command": "vscode-augment.manageAccountCommunity", "when": "augment.userTier == 'community'"}, {"command": "vscode-augment.manageAccountProfessional", "when": "augment.userTier == 'professional'"}, {"command": "vscode-augment.manageAccountEnterprise", "when": "augment.userTier == 'enterprise'"}, {"command": "vscode-augment.showRemoteAgentsPanel", "when": "vscode-augment.featureFlags.enableRemoteAgents"}, {"command": "vscode-augment.openSshConfig", "when": "vscode-augment.featureFlags.enableRemoteAgents"}, {"command": "vscode-augment.next-edit.open-panel", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.enablePanel"}, {"command": "vscode-augment.next-edit.force", "when": "vscode-augment.enableNextEdit"}, {"command": "vscode-augment.next-edit.background.accept", "when": "vscode-augment.enableNextEdit"}, {"command": "vscode-augment.next-edit.background.accept-all", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canAcceptAll"}, {"command": "vscode-augment.next-edit.background.reject", "when": "vscode-augment.enableNextEdit"}, {"command": "vscode-augment.next-edit.background.reject-all", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canRejectAll"}, {"command": "vscode-augment.next-edit.background.dismiss", "when": "vscode-augment.enableNextEdit"}, {"command": "vscode-augment.next-edit.background.next", "when": "vscode-augment.enableNextEdit"}, {"command": "vscode-augment.next-edit.background.next-forward", "when": "vscode-augment.enableNextEdit"}, {"command": "vscode-augment.next-edit.background.previous", "when": "vscode-augment.enableNextEdit"}, {"command": "vscode-augment.next-edit.toggle-bg", "when": "vscode-augment.enableNextEdit"}, {"command": "vscode-augment.next-edit.toggle-all-highlights", "when": "vscode-augment.enableNextEdit"}, {"command": "_vscode-augment.next-edit.background.accept-code-action", "when": "false"}, {"command": "vscode-augment.next-edit.toggle-panel-horizontal-split", "when": "view == augment-next-edit"}, {"command": "vscode-augment.next-edit.update", "when": "vscode-augment.enableNextEdit"}, {"command": "_vscode-augment.next-edit.update.loading", "when": "false"}, {"command": "_vscode-augment.next-edit.update.disabled-no-changes", "when": "false"}, {"command": "_vscode-augment.next-edit.update.disabled-cached", "when": "false"}, {"command": "vscode-augment.next-edit.learn-more", "when": "view == augment-next-edit"}, {"command": "_vscode-augment.next-edit.background.open", "when": "false"}, {"command": "_vscode-augment.next-edit.background.next-forward.disabled", "when": "false"}, {"command": "_vscode-augment.next-edit.background.previous.disabled", "when": "false"}], "scm/title": [{"command": "vscode-augment.generateCommitMessage", "args": ["${resourceUri}"], "group": "navigation", "when": "vscode-augment.enableGenerateCommitMessage && gitOpenRepositoryCount != 0"}], "editor/title": [{"submenu": "vscode-augment.next-edit.editor-action-submenu", "group": "navigation@43", "when": "vscode-augment.enableNextEdit && !terminalEditorActive && vscode-augment.nextEdit.canNext && !vscode-augment.nextEdit.loading"}, {"submenu": "vscode-augment.next-edit.editor-action-submenu.disabled", "group": "navigation@43", "when": "vscode-augment.enableNextEdit && !terminalEditorActive && !vscode-augment.nextEdit.canNext && !vscode-augment.nextEdit.loading"}, {"submenu": "vscode-augment.next-edit.editor-action-submenu.loading", "group": "navigation@43", "when": "vscode-augment.enableNextEdit && !terminalEditorActive && vscode-augment.nextEdit.loading"}], "vscode-augment.next-edit.panel-submenu": [{"command": "vscode-augment.next-edit.background.accept-all", "when": "vscode-augment.nextEdit.canAcceptAll", "title": "Accept All", "group": "2_more@1"}, {"command": "vscode-augment.next-edit.background.reject-all", "when": "vscode-augment.nextEdit.canRejectAll", "title": "Reject All", "group": "2_more@2"}, {"command": "vscode-augment.next-edit.learn-more", "title": "Learn about Next Edit...", "group": "2_more@3"}, {"command": "vscode-augment.next-edit.settings", "title": "Next Edit Settings...", "group": "2_more@4"}], "vscode-augment.next-edit.editor-action-submenu": [{"command": "vscode-augment.next-edit.background.previous", "title": "Go to Previous Suggestion", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canPrevious && (editorTextFocus || editorHoverFocused || augment-next-edit.active)", "group": "2_augment@1"}, {"command": "vscode-augment.next-edit.background.next-forward", "title": "Go to Next Suggestion", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canNext && (editorTextFocus || editorHoverFocused || augment-next-edit.active)", "group": "2_augment@2"}, {"command": "vscode-augment.next-edit.open-panel", "title": "Open Panel", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.enablePanel", "group": "2_augment@3"}, {"command": "vscode-augment.next-edit.force", "group": "2_augment@4", "when": "vscode-augment.enableNextEdit && (editorTextFocus || editorHoverFocused || augment-next-edit.active)"}, {"command": "vscode-augment.next-edit.enable-bg", "title": "Enable Background Suggestions", "when": "vscode-augment.enableNextEdit && !vscode-augment.enableNextEditBackgroundSuggestions", "group": "2_augment@5"}, {"command": "vscode-augment.next-edit.disable-bg", "title": "Disable Background Suggestions", "when": "vscode-augment.enableNextEdit && vscode-augment.enableNextEditBackgroundSuggestions", "group": "2_augment@5"}, {"command": "vscode-augment.next-edit.settings", "title": "Next Edit Settings...", "group": "2_augment@6", "when": "vscode-augment.enableNextEdit"}, {"command": "vscode-augment.next-edit.learn-more", "title": "Learn about Next Edit...", "group": "2_augment@7", "when": "vscode-augment.enableNextEdit"}], "vscode-augment.next-edit.editor-action-submenu.disabled": [{"command": "vscode-augment.next-edit.background.previous", "title": "Go to Previous Suggestion", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canPrevious && (editorTextFocus || editorHoverFocused || augment-next-edit.active)", "group": "2_augment@1"}, {"command": "vscode-augment.next-edit.background.next-forward", "title": "Go to Next Suggestion", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canNext && (editorTextFocus || editorHoverFocused || augment-next-edit.active)", "group": "2_augment@2"}, {"command": "vscode-augment.next-edit.open-panel", "title": "Open Panel", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.enablePanel", "group": "2_augment@3"}, {"command": "vscode-augment.next-edit.force", "group": "2_augment@4", "when": "vscode-augment.enableNextEdit && (editorTextFocus || editorHoverFocused || augment-next-edit.active)"}, {"command": "vscode-augment.next-edit.enable-bg", "title": "Enable Background Suggestions", "when": "vscode-augment.enableNextEdit && !vscode-augment.enableNextEditBackgroundSuggestions", "group": "2_augment@5"}, {"command": "vscode-augment.next-edit.disable-bg", "title": "Disable Background Suggestions", "when": "vscode-augment.enableNextEdit && vscode-augment.enableNextEditBackgroundSuggestions", "group": "2_augment@5"}, {"command": "vscode-augment.next-edit.settings", "title": "Next Edit Settings...", "group": "2_augment@6", "when": "vscode-augment.enableNextEdit"}, {"command": "vscode-augment.next-edit.learn-more", "title": "Learn about Next Edit...", "group": "2_augment@7", "when": "vscode-augment.enableNextEdit"}], "vscode-augment.next-edit.editor-action-submenu.loading": [{"command": "vscode-augment.next-edit.background.previous", "title": "Go to Previous Suggestion", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canPrevious && (editorTextFocus || editorHoverFocused || augment-next-edit.active)", "group": "2_augment@1"}, {"command": "vscode-augment.next-edit.background.next-forward", "title": "Go to Next Suggestion", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canNext && (editorTextFocus || editorHoverFocused || augment-next-edit.active)", "group": "2_augment@2"}, {"command": "vscode-augment.next-edit.open-panel", "title": "Open Panel", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.enablePanel", "group": "2_augment@3"}, {"command": "vscode-augment.next-edit.force", "group": "2_augment@4", "when": "vscode-augment.enableNextEdit && (editorTextFocus || editorHoverFocused || augment-next-edit.active)"}, {"command": "vscode-augment.next-edit.enable-bg", "title": "Enable Background Suggestions", "when": "vscode-augment.enableNextEdit && !vscode-augment.enableNextEditBackgroundSuggestions", "group": "2_augment@5"}, {"command": "vscode-augment.next-edit.disable-bg", "title": "Disable Background Suggestions", "when": "vscode-augment.enableNextEdit && vscode-augment.enableNextEditBackgroundSuggestions", "group": "2_augment@5"}, {"command": "vscode-augment.next-edit.settings", "title": "Next Edit Settings...", "group": "2_augment@6", "when": "vscode-augment.enableNextEdit"}, {"command": "vscode-augment.next-edit.learn-more", "title": "Learn about Next Edit...", "group": "2_augment@7", "when": "vscode-augment.enableNextEdit"}]}, "viewsContainers": {"activitybar": [{"icon": "media/activitybar.svg", "id": "augment-chat", "title": "Augment"}], "panel": [{"icon": "media/activitybar.svg", "id": "augment-panel", "title": "Augment Next Edit"}]}, "views": {"augment-chat": [{"id": "augment-chat", "name": "Augment", "type": "webview"}], "augment-panel": [{"id": "augment-next-edit", "name": "Augment Next Edit", "type": "webview", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.enablePanel"}]}}, "scripts": {"build": "npm run vscode:build-dev && npm run common-webviews:build", "build:prod": "npm run vscode:esbuild-prod && npm run common-webviews:build", "lint": "npm run vscode:lint", "lint:fix": "npm run vscode:lint:fix", "test": "npm run vscode:test && npm run test:e2e", "test:fix": "npm run lint:fix && npm run test", "test:debug": "LOG_LEVEL=verbose npm run test", "test:e2e": "wdio run ./wdio.conf.ts", "watch": "concurrently 'npm:*:watch'", "package-extension": "STABLE_VSCODE_RELEASE_VERSION=$(head -n1 version)-$(git rev-parse --abbrev-ref HEAD).$(git log --format='%ct.%h' -n1) bazel build //clients/vscode:package-extension-prerelease --config=local_output --compilation_mode=opt --stamp && cp ../../bazel-bin/clients/vscode/vscode-augment-prerelease.vsix .", "package-extension-pnpm": "rm -rf out ; rm -rf webviews/dist ; npm run build:prod ; npm run embed-version-info && npm run package-for-release && cp package.json.bak package.json && rm package.json.bak", "marketplace-data": "vsce show --json Augment.vscode-augment", "embed-version-info": "./embed-version-info.py", "vsce-package": "vsce package --no-dependencies --allow-star-activation --skip-license --out=\"${EXTENSION_FILENAME:-out/vscode-augment.vsix}\"", "package-for-release": "[ \"$RELEASE_CHANNEL\" = \"prerelease\" ] && npm run vsce-package -- --pre-release || npm run vsce-package", "vscode:build-dev": "pnpm run vscode:esbuild-sourcemaps", "vscode:watch": "pnpm run vscode:esbuild-sourcemaps --watch", "vscode:esbuild-prod": "npm run vscode:esbuild-base -- --minify", "vscode:esbuild-sourcemaps": "npm run vscode:esbuild-base -- --sourcemap", "vscode:esbuild-base": "esbuild ./src/extension.ts --bundle --outfile=out/extension.js --external:vscode --format=cjs --platform=node", "vscode:extension:dev:hmr": "pnpm run -r dev:vite-hmr-vscode", "vscode:extension:dev:watch": ". ./.augment-hmr-env && pnpm exec ibazel run //clients/vscode:build_dev_to_workspace_hmr --config='local_output' --compilation_mode=dbg --action_env=AUGMENT_HMR=${AUGMENT_HMR} --action_env=AUGMENT_JS_ENV=${AUGMENT_JS_ENV}", "vscode:extension:dev:watch-no-hmr": "AUGMENT_JS_ENV=development pnpm exec ibazel run //clients/vscode:build_dev_to_workspace --config='local_output' --compilation_mode=dbg --action_env=AUGMENT_JS_ENV=development", "vscode:hmr:write:port": "node scripts/generate-augment-hmr-env.js", "vscode:lint": "npm run vscode:eslint && npm run vscode:prettier", "vscode:lint:fix": "npm run vscode:eslint:fix && npm run vscode:prettier:fix", "vscode:eslint": "git ls-files -- . | xargs pre-commit run eslint --files", "vscode:eslint:fix": "git ls-files -- . | xargs pre-commit run eslint --hook-stage=manual --files", "vscode:jest": "jest --config ./jest.config.js", "vscode:prettier": "git ls-files -- . | xargs pre-commit run prettier --hook-stage=manual --files", "vscode:prettier:fix": "git ls-files -- . | xargs pre-commit run prettier --files", "vscode:test": "npm run build:prod && npm run vscode:lint && npm run vscode:jest", "common-webviews:build": "cd ../common/webviews && npm run build:vscode", "contributes-gen": "scripts/contributes/cli.ts"}, "devDependencies": {"@babel/core": "^7.26.10", "@babel/preset-env": "^7.26.9", "@jest/globals": "^29.7.0", "@trivago/prettier-plugin-sort-imports": "^4.3.0", "@types/diff": "^7.0.1", "@types/glob": "^7.2.0", "@types/jest": "^29.5.11", "@types/lodash": "^4.14.202", "@types/lodash.memoize": "^4.1.9", "@types/lodash.throttle": "^4.1.9", "@types/node": "18.15.0", "@types/node-forge": "^1.3.11", "@types/semver": "^7.5.8", "@types/uuid": "^9.0.8", "@types/vscode": "^1.82.0", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "@vscode/test-electron": "^2.3.9", "@wdio/cli": "^9.3.0", "@wdio/globals": "^8.27.0", "@wdio/local-runner": "^8.27.0", "@wdio/mocha-framework": "^8.27.0", "@wdio/spec-reporter": "^8.27.0", "@wdio/types": "^8.27.0", "babel-jest": "^29.7.0", "concurrently": "^8.2.2", "esbuild": "^0.14.54", "eslint": "^8.57.0", "eslint-plugin-jest": "^27.6.3", "eslint-plugin-mocha": "^10.5.0", "eslint-plugin-unused-imports": "^3.2.0", "fetch-mock": "^9.11.0", "fetch-mock-jest": "^1.5.1", "glob": "^8.1.0", "isomorphic-fetch": "^3.0.0", "jest": "^29.7.0", "jest-cli": "^29.7.0", "jest-environment-node": "^29.7.0", "jest-mock-vscode": "^3.0.4", "node-fetch": "^2.7.0", "nodemon": "^3.0.3", "npm": "^9.9.2", "replace-in-file": "^6.3.5", "ts-jest": "^29.1.2", "ts-node": "^10.9.2", "tsx": "^4.17.0", "typescript": "^5.5.3", "wdio-vscode-service": "^6.1.2"}, "dependencies": {"@anthropic-ai/sdk": "^0.27.2", "@anthropic-ai/vertex-sdk": "^0.4.1", "@bufbuild/protobuf": "^2.3.0", "@connectrpc/connect": "^2.0.2", "@vscode/vsce": "2.29.0", "denque": "^2.1.0", "diff": "^7.0.0", "encoding": "^0.1.13", "fuse.js": "^7.0.0", "highlight.js": "^11.9.0", "ignore": "^5.3.0", "jest-junit": "^16.0.0", "json5": "^2.2.3", "lodash": "^4.17.21", "lodash.memoize": "^4.1.2", "lodash.throttle": "^4.1.1", "lru-cache": "^11.0.0", "mac-ca": "^3.1.0", "monaco-editor": "^0.52.2", "node-diff3": "^3.1.2", "p-limit": "^3.1.0", "prettier-plugin-svelte": "^3.2.3", "prosemirror-model": "^1.23.0", "semver": "^7.6.3", "shlex": "^2.1.2", "simple-git": "^3.27.0", "typescript-eslint": "7.12.0", "uuid": "^9.0.1", "winston": "^3.11.0", "winston-transport": "^4.6.0", "zod": "^3.23.8"}, "__metadata": {"installedTimestamp": 1750199979567, "targetPlatform": "undefined", "size": 19334734}}, "codeAnalysis": {"functions": ["q7e", "j7e", "X7e", "nKe", "oKe", "dKe", "fKe", "mKe", "_Ke", "TKe", "p", "g", "A", "y", "b", "v", "E", "_", "w", "BKe", "RS", "KKe", "D0", "YKe", "jKe", "zKe", "JKe", "BS", "n", "eYe", "rYe", "nYe", "sYe", "VW", "FS", "Fo", "Bt", "No", "fc", "eB", "Yl", "zp", "ad", "Un", "Fa", "nl", "Rv", "v0", "nS", "_6", "rB", "Dv", "Jp", "I", "R", "W", "le", "fe", "<PERSON>", "Mt", "Cr", "si", "Vs", "Na", "<PERSON>i", "cd", "pc", "<PERSON>", "nB", "iWe", "aWe", "cWe", "kv", "lWe", "uWe", "x6", "$ne", "Pg", "iB", "dWe", "fWe", "pWe", "Bv", "ld", "Gne", "gWe", "mWe", "AWe", "te", "Q", "h", "yB", "zl", "Xr", "OWe", "UWe", "qWe", "E0", "VWe", "WWe", "HWe", "$We", "GWe", "Xp", "KWe", "YWe", "jWe", "zWe", "JWe", "Zp", "XWe", "ZWe", "e8e", "t8e", "r8e", "_0", "n8e", "i8e", "ud", "s8e", "o8e", "a8e", "c8e", "l8e", "tie", "rie", "u8e", "d8e", "R6", "lS", "bB", "f8e", "nie", "p8e", "eh", "D6", "x0", "Jl", "h8e", "iie", "sie", "uS", "g8e", "vB", "m8e", "aie", "Po", "xf", "B6", "CB", "w0", "lie", "Pa", "M6", "A8e", "y8e", "b8e", "F6", "v8e", "dS", "uie", "C8e", "E8e", "fS", "_8e", "x8e", "N6", "die", "w8e", "S8e", "I8e", "fie", "P6", "T8e", "L6", "pie", "hie", "gie", "EB", "R8e", "mie", "<PERSON><PERSON>", "D8e", "yie", "k8e", "Q6", "bie", "O6", "B8e", "U6", "Mr", "M8e", "F8e", "pS", "P8e", "Xl", "L8e", "_B", "q6", "<PERSON><PERSON>", "<PERSON><PERSON>", "il", "Ug", "V6", "_ie", "xB", "xie", "W6", "wie", "H6", "$6", "qg", "Vg", "<PERSON><PERSON>", "G6", "O8e", "U8e", "q8e", "Tie", "<PERSON><PERSON>", "V8e", "Die", "kie", "hc", "wf", "W8e", "H8e", "wB", "Lv", "<PERSON><PERSON>", "<PERSON><PERSON>", "$8e", "O", "Fie", "Qv", "hS", "G8e", "D", "<PERSON><PERSON>", "Pie", "SB", "<PERSON>t", "Lie", "IB", "K6", "TB", "K8e", "$", "<PERSON><PERSON>", "RB", "<PERSON><PERSON>", "Y6", "<PERSON><PERSON>", "th", "qie", "Vie", "j8e", "Wie", "z8e", "J8e", "rh", "j6", "z6", "DB", "Ov", "Lt", "kB", "X6", "S0", "X8e", "Z8e", "eHe", "$ie", "tHe", "<PERSON><PERSON>", "rHe", "nHe", "iHe", "nh", "La", "eW", "sHe", "tW", "oHe", "gS", "<PERSON><PERSON>", "<PERSON><PERSON>", "cHe", "lHe", "uHe", "dHe", "jie", "zie", "fHe", "rW", "<PERSON><PERSON>", "<PERSON><PERSON>", "BB", "Sf", "I0", "pHe", "tse", "hHe", "gHe", "mHe", "vHe", "CHe", "EHe", "_He", "xHe", "rse", "nse", "ise", "wHe", "SHe", "IHe", "sse", "THe", "RHe", "MHe", "Zl", "FHe", "NHe", "ose", "LHe", "QHe", "UHe", "iW", "qHe", "VHe", "WHe", "HHe", "$He", "GHe", "KHe", "YHe", "jHe", "zHe", "JHe", "XHe", "ZHe", "e$e", "i$e", "s$e", "o$e", "sW", "ase", "f$e", "p$e", "cse", "g$e", "MB", "A$e", "y$e", "b$e", "v$e", "C$e", "E$e", "_$e", "w$e", "S$e", "R$e", "D$e", "k$e", "lse", "use", "M$e", "FB", "P$e", "Q$e", "O$e", "U$e", "q$e", "V$e", "W$e", "H$e", "$$e", "K$e", "dse", "fse", "hse", "gse", "mse", "ht", "Qt", "<PERSON>r", "Hr", "an", "ol", "Qa", "al", "z$e", "PB", "LB", "J$e", "eGe", "tGe", "rGe", "nGe", "iGe", "sGe", "oGe", "aGe", "cGe", "lGe", "uGe", "dd", "gc", "As", "hGe", "mGe", "AGe", "yGe", "bGe", "cW", "vGe", "ih", "yse", "QB", "Hi", "rs", "CGe", "EGe", "_Ge", "xGe", "wGe", "SGe", "vse", "AS", "IGe", "OB", "sl", "TGe", "RGe", "DGe", "Ese", "sh", "Er", "_se", "eu", "xse", "MGe", "qn", "LGe", "UGe", "qGe", "VGe", "WGe", "HGe", "$Ge", "GGe", "KGe", "uW", "YGe", "dW", "oo", "mc", "XGe", "ZGe", "r9e", "<PERSON><PERSON>", "i9e", "s9e", "o9e", "a9e", "c9e", "l9e", "u9e", "qv", "d9e", "f9e", "p9e", "h9e", "Dse", "kse", "m9e", "A9e", "y9e", "E9e", "_9e", "x9e", "w9e", "S9e", "I9e", "R9e", "k9e", "B9e", "print", "M9e", "F9e", "N9e", "P9e", "L9e", "Q9e", "O9e", "Bse", "V9e", "W9e", "pW", "H9e", "Ac", "hW", "K9e", "Y9e", "gW", "J9e", "mW", "X9e", "Fse", "r7e", "AW", "yW", "s7e", "o7e", "a7e", "c7e", "l7e", "u7e", "g7e", "m7e", "A7e", "y7e", "b7e", "v7e", "x7e", "w7e", "Cce", "Ece", "c8", "bze", "vze", "<PERSON><PERSON>", "<PERSON><PERSON>", "_ze", "zS", "xze", "wze", "Sze", "X0", "W2", "pC", "i", "Tze", "_ce", "Dze", "Bze", "xce", "_8", "Fze", "<PERSON>ze", "Ice", "Tce", "Pze", "Lze", "<PERSON><PERSON>", "<PERSON><PERSON>", "w8", "Rce", "<PERSON><PERSON>", "wce", "Dce", "S8", "qze", "Vze", "kce", "Hze", "$ze", "Gze", "Kze", "Bce", "Mce", "Fce", "<PERSON><PERSON>", "jze", "zze", "Jze", "rJe", "nJe", "Nce", "oJe", "aJe", "cJe", "Lce", "Qce", "fJe", "<PERSON><PERSON>", "pJe", "hJe", "gJe", "c", "mJe", "D8", "o", "k8", "B8", "yJe", "vJe", "Vce", "EJe", "_Je", "xJe", "Hce", "F8", "wJe", "N8", "$ce", "SJe", "IJe", "RJe", "<PERSON>e", "kJe", "FJe", "<PERSON>e", "f", "Gce", "PJe", "LJe", "Kce", "QJe", "OJe", "UJe", "qJe", "VJe", "e", "K2", "WJe", "YJe", "L8", "ele", "tle", "rle", "yC", "JJe", "ZJe", "l", "u", "rXe", "cle", "nXe", "iXe", "sXe", "oXe", "ile", "aXe", "lle", "ule", "cXe", "dle", "fle", "lXe", "uXe", "dXe", "fXe", "ple", "pXe", "hXe", "sle", "d", "ole", "gXe", "mXe", "AXe", "yXe", "bXe", "hle", "vXe", "U8", "a", "gle", "mle", "K8", "code", "wXe", "SXe", "yle", "IXe", "TXe", "J2", "G8", "wle", "Y8", "MXe", "<PERSON><PERSON>", "FXe", "Ile", "PXe", "LXe", "QXe", "<PERSON><PERSON>", "kle", "<PERSON><PERSON>", "qXe", "VXe", "WXe", "HXe", "$Xe", "GXe", "eZe", "tZe", "<PERSON><PERSON>", "rZe", "Nf", "iZe", "vh", "gZe", "s", "<PERSON><PERSON>", "<PERSON><PERSON>", "mZe", "AZe", "yZe", "bZe", "CZe", "_Ze", "Z8", "xZe", "wZe", "<PERSON><PERSON>", "<PERSON><PERSON>", "iM", "rue", "hue", "mue", "Let", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Lf", "Htt", "rde", "YH", "GH", "jH", "nde", "<PERSON><PERSON>", "ide", "sde", "jtt", "ztt", "Ztt", "ert", "tde", "rrt", "lde", "ly", "d$", "p$", "BM", "Ode", "Wrt", "Hrt", "$rt", "Gde", "Kde", "<PERSON><PERSON>", "Krt", "qi", "zrt", "Jrt", "cfe", "snt", "ont", "ant", "hfe", "yfe", "Afe", "PI", "lnt", "NC", "t", "Rfe", "pm", "H$", "OI", "G$", "Ffe", "$$", "Snt", "Int", "Tnt", "Rnt", "Dnt", "Ofe", "GM", "Y$", "z$", "X$", "eG", "Unt", "nG", "qnt", "rpe", "Vnt", "spe", "ope", "cpe", "Hnt", "dpe", "fpe", "hpe", "<PERSON>", "lG", "$nt", "Epe", "xpe", "<PERSON>pe", "Spe", "$I", "kpe", "Ynt", "jnt", "Ze", "ss", "zM", "hG", "YI", "Vpe", "Wpe", "<PERSON><PERSON>", "yit", "$pe", "Gpe", "Kpe", "xh", "rF", "ga", "Uit", "Wn", "ohe", "ahe", "qit", "che", "lhe", "CG", "uhe", "wn", "vr", "De", "Lr", "ye", "ke", "Wt", "Zn", "bi", "ws", "Ba", "Wp", "bf", "Wr", "r6", "so", "el", "Hp", "$p", "Qw", "tl", "Ow", "Uw", "on", "er", "$l", "Gl", "m0", "Kt", "yv", "qw", "Rk", "bv", "kg", "vv", "n6", "Vw", "qs", "Ww", "i6", "Gp", "At", "Br", "vf", "A0", "Cf", "Hw", "Dk", "s6", "kk", "o6", "a6", "c6", "l6", "Ev", "u6", "_v", "d6", "f6", "p6", "Bk", "rl", "Mk", "Fk", "$w", "h6", "g6", "Nk", "od", "Pk", "Gw", "Kw", "Lk", "Ma", "xv", "m6", "Yw", "y0", "Bg", "wv", "Sv", "Kl", "Mg", "Kp", "Qk", "Ok", "Yp", "Uk", "qk", "Vk", "Wk", "jw", "Iv", "A6", "Hk", "$k", "zw", "Gk", "y6", "Jw", "Kk", "Yk", "jk", "b6", "v6", "ii", "Bn", "Tv", "zk", "Ef", "Xw", "C6", "E6", "Fg", "Jk", "Ss", "Zw", "jp", "Xk", "eS", "Mo", "_f", "Zk", "tS", "b0", "Ci", "rS", "tB", "<PERSON><PERSON>", "wG", "fF", "Xit", "BG", "Zit", "est", "MG", "Ohe", "rst", "nst", "ist", "FG", "qhe", "pF", "Vhe", "RG", "sst", "ost", "DG", "Phe", "ast", "cst", "ust", "dst", "fst", "pst", "hst", "gst", "mst", "<PERSON>he", "hF", "<PERSON>he", "bst", "vst", "$he", "Cst", "xst", "wst", "Ist", "kst", "Mst", "Nst", "Lst", "JC", "Hst", "$st", "Gst", "Kst", "eot", "not", "oot", "yot", "bot", "<PERSON><PERSON>", "<PERSON><PERSON>", "Rot", "Fot", "<PERSON>ot", "qot", "XC", "jot", "zot", "Xot", "eat", "rat", "iat", "oat", "ZC", "mat", "eE", "xat", "wat", "UF", "Rat", "<PERSON>t", "<PERSON>", "Oat", "Uat", "ict", "sct", "lct", "uct", "dct", "Act", "Cct", "Sct", "Fct", "Pct", "ult", "dlt", "Slt", "Tlt", "Rlt", "Plt", "Olt", "Wlt", "Klt", "Jlt", "Eut", "t0e", "wut", "Put", "Lut", "tN", "m0e", "<PERSON><PERSON>", "A0e", "XF", "y0e", "Out", "<PERSON><PERSON>", "qut", "Vut", "<PERSON><PERSON>", "b0e", "v0e", "C0e", "Hu<PERSON>", "Gut", "E0e", "<PERSON><PERSON>", "<PERSON><PERSON>", "jut", "_0e", "zut", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "edt", "tdt", "rdt", "ndt", "x0e", "odt", "w0e", "adt", "ldt", "udt", "ddt", "fdt", "pdt", "nN", "received", "$9", "U0e", "W0e", "Ndt", "H0e", "Ldt", "oft", "aft", "fft", "lye", "dN", "fE", "uye", "aye", "cye", "pft", "X9", "hft", "dye", "gft", "mft", "lN", "Aft", "yft", "Z9", "fye", "bft", "bye", "Nft", "vye", "Pft", "C1", "Lft", "Qft", "Oft", "<PERSON><PERSON>", "qft", "Vft", "Wft", "Hft", "$ft", "Gft", "pE", "Kft", "Yft", "jft", "Eye", "zft", "Jft", "e7", "v1", "Xft", "_ye", "ept", "tpt", "rpt", "npt", "hN", "ipt", "spt", "opt", "apt", "cpt", "xye", "upt", "dpt", "fpt", "ppt", "fN", "wye", "gpt", "t7", "Sye", "mpt", "Apt", "ypt", "bpt", "vpt", "<PERSON><PERSON>", "Ept", "Spt", "a7", "<PERSON><PERSON>", "Upt", "qpt", "Vpt", "O<PERSON>", "gE", "c7", "AN", "tht", "<PERSON><PERSON>", "rht", "nht", "iht", "sht", "oht", "mE", "<PERSON><PERSON>", "aht", "<PERSON><PERSON>", "<PERSON>ht", "<PERSON><PERSON>", "Rht", "Dht", "kht", "Bht", "Mht", "ebe", "Fht", "tbe", "Kht", "Yht", "jht", "zht", "Jht", "Xht", "Zht", "egt", "tgt", "ube", "rgt", "ngt", "dbe", "agt", "mbe", "cgt", "ugt", "wbe", "or", "Sbe", "<PERSON><PERSON>", "_be", "k7", "Ugt", "lmt", "ymt", "bmt", "Dmt", "<PERSON><PERSON>", "qmt", "Wmt", "Hmt", "Zmt", "r", "aAt", "cAt", "$1", "lAt", "rK", "_ve", "xve", "nK", "iK", "fAt", "Pve", "Qve", "bAt", "Ove", "Vve", "MAt", "Xve", "nCe", "Fh", "uCe", "dCe", "CK", "fCe", "lCe", "jAt", "pCe", "hCe", "zAt", "bK", "gCe", "vK", "mCe", "JAt", "ACe", "XAt", "yCe", "ZAt", "<PERSON>e", "HCe", "O0t", "rEe", "iEe", "sEe", "VK", "oEe", "GK", "LE", "sP", "hyt", "nP", "AEe", "gyt", "pEe", "z1", "<PERSON><PERSON>", "fP", "NEe", "PEe", "Rbt", "Dbt", "GEe", "ZK", "YEe", "jEe", "VEe", "kbt", "eY", "zEe", "Bbt", "JEe", "Mbt", "Fbt", "Obt", "km", "Ubt", "qbt", "Vbt", "l_e", "Gbt", "Kbt", "nvt", "T_e", "R_e", "D_e", "ivt", "k_e", "avt", "cvt", "pvt", "VE", "mvt", "Avt", "yvt", "N_e", "Qvt", "Ovt", "Uvt", "qvt", "pY", "Vvt", "Wvt", "Hvt", "$vt", "H_e", "$_e", "G_e", "K_e", "Gvt", "Kvt", "Yvt", "zvt", "fCt", "pCt", "txe", "rxe", "nxe", "vxe", "RCt", "$Ct", "GCt", "KC<PERSON>", "YCt", "jCt", "pT", "UEt", "hT", "IY", "VEt", "$P", "GP", "kY", "XE", "owe", "awe", "FY", "KP", "Re", "Gn", "mT", "YP", "jEt", "hwe", "XP", "zEt", "rp", "s_t", "vwe", "eL", "a_t", "_we", "Se", "In", "f_t", "p_t", "<PERSON><PERSON>", "Fwe", "Nwe", "g_t", "m_t", "Om", "A_t", "y_t", "b_t", "v_t", "C_t", "E_t", "iL", "__t", "x_t", "w_t", "S_t", "I_t", "T_t", "R_t", "D_t", "k_t", "B_t", "$y", "M_t", "F_t", "N_t", "P_t", "L_t", "Q_t", "Gy", "O_t", "U_t", "q_t", "V_t", "W_t", "H_t", "$_t", "G_t", "K_t", "Y_t", "j_t", "z_t", "J_t", "X_t", "Z_t", "ext", "txt", "rxt", "nxt", "ixt", "sxt", "oxt", "axt", "cxt", "GY", "lxt", "Pwe", "uxt", "Lwe", "dxt", "fxt", "pxt", "hxt", "gxt", "mxt", "Axt", "yxt", "bxt", "vxt", "Cxt", "Ext", "_xt", "xxt", "wxt", "Sxt", "Ixt", "Txt", "Rxt", "Dxt", "kxt", "Bxt", "bT", "Qwe", "Mxt", "Fxt", "Nxt", "Pxt", "Lxt", "r_", "Qxt", "Oxt", "Uxt", "qxt", "Vxt", "Wxt", "Hxt", "$xt", "Gxt", "Yxt", "jxt", "zxt", "Jxt", "Uwe", "sL", "q", "Jwe", "oL", "swt", "ap", "owt", "Gwe", "<PERSON><PERSON>", "<PERSON>we", "jwe", "OID", "eSe", "tSe", "rSe", "uwt", "qm", "o_", "_u", "hSe", "tj", "ywt", "dSe", "ET", "rj", "wwt", "Swt", "Iwt", "Twt", "ISe", "jwt", "zwt", "Jwt", "Xwt", "Zwt", "eSt", "DSe", "Vh", "wT", "aSt", "cSt", "lSt", "qSe", "vj", "Cj", "QSe", "<PERSON><PERSON>", "AL", "uSt", "dSt", "fSt", "OSe", "VSe", "pSt", "WSe", "HSe", "_j", "$m", "hSt", "<PERSON><PERSON>", "$Se", "Vt", "c_", "l_", "<PERSON><PERSON>", "ci", "jSe", "gSt", "mSt", "ASt", "ySt", "bSt", "vSt", "<PERSON><PERSON>", "CSt", "Tj", "iIe", "lp", "d_", "bL", "dIe", "ESt", "fIe", "yIe", "TSt", "kSt", "PSt", "OSt", "VSt", "WSt", "jSt", "ZSt", "rIt", "sIt", "oIt", "lIt", "vIt", "xIt", "IIt", "TIt", "DIt", "BIt", "MIt", "NIt", "o1t", "f1t", "m1t", "_1t", "IL", "xTt", "STt", "ITt", "Nj", "pTe", "MTt", "FTt", "BTe", "GTt", "found", "jm", "<PERSON>j", "jj", "FTe", "jTt", "NTe", "PTe", "tb", "zTt", "JTt", "zj", "tRt", "aRt", "cRt", "dRt", "fRt", "pRt", "PL", "k", "jo", "kRt", "gRe", "VL", "BRt", "oz", "MRt", "Su", "ARe", "FRt", "NRt", "PRt", "QRt", "ORt", "ERe", "_Re", "URt", "qRt", "xRe", "VRt", "wRe", "WRt", "KRt", "TRe", "ib", "YRt", "jRt", "LT", "hz", "zRt", "JRt", "XRt", "ZRt", "eDt", "tDt", "rDt", "nDt", "iDt", "sDt", "oDt", "aDt", "cDt", "lDt", "FRe", "Jm", "uDt", "dDt", "Zm", "pDt", "hDt", "gDt", "QRe", "bDt", "ORe", "vDt", "CDt", "EDt", "_Dt", "wDt", "IDt", "<PERSON><PERSON>", "PDt", "$Re", "Tn", "GRe", "Ez", "LDt", "QDt", "<PERSON>e", "ODt", "zL", "KRe", "xz", "UDt", "qDt", "YRe", "VDt", "WDt", "HDt", "Sz", "jRe", "_z", "$Dt", "zRe", "dp", "KDt", "YDt", "tDe", "JDt", "XDt", "lkt", "VT", "us", "ukt", "dkt", "fkt", "pkt", "Dz", "hkt", "gkt", "mkt", "eDe", "Akt", "nDe", "iDe", "ykt", "bkt", "qT", "vkt", "Ckt", "Skt", "mDe", "ADe", "CDe", "Nkt", "bDe", "vDe", "Qkt", "xDe", "Okt", "Ukt", "qkt", "Vkt", "Nz", "jkt", "kDe", "eBt", "tBt", "rBt", "nBt", "iBt", "cBt", "lBt", "GT", "Qz", "wBt", "WDe", "DBt", "GDe", "NBt", "UBt", "$h", "qBt", "jDe", "KT", "VBt", "eke", "$Bt", "GBt", "tke", "KBt", "YBt", "jBt", "Hz", "$z", "zBt", "Gz", "Wz", "ske", "Kz", "JBt", "XBt", "nA", "EQ", "i2t", "s2t", "o2t", "a2t", "c2t", "d2t", "f2t", "zo", "p2t", "h2t", "ds", "Ks", "g2t", "Xz", "Wd", "x2t", "db", "tMt", "rMt", "nMt", "iMt", "kQ", "pMt", "nR", "no", "fJ", "QMt", "EFt", "_Ft", "hO", "xFt", "wFt", "v2e", "c5t", "l5t", "u5t", "d5t", "f5t", "p5t", "<PERSON>u", "g5t", "m5t", "A5t", "see", "C5t", "E5t", "_5t", "x5t", "w5t", "S5t", "I5t", "k5t", "B5t", "M5t", "F5t", "N5t", "P5t", "L5t", "Q5t", "O5t", "V5t", "W5t", "H5t", "$5t", "G5t", "K5t", "Y5t", "dee", "dte", "lWt", "mg", "_e", "<PERSON>", "Kb", "uWt", "dWt", "fte", "lte", "Aq", "Pi", "Yb", "hqe", "pWt", "mqe", "bq", "DWt", "kWt", "BWt", "MWt", "FWt", "NWt", "FA", "PWt", "LWt", "QWt", "OWt", "UWt", "qWt", "VWt", "N5e", "WWt", "HWt", "Pte", "$Wt", "GWt", "P5e", "KWt", "YWt", "jWt", "zWt", "JWt", "XWt", "ZWt", "e8t", "t8t", "r8t", "n8t", "i8t", "s8t", "o8t", "L5e", "a8t", "c8t", "l8t", "u8t", "d8t", "f8t", "x5e", "p8t", "h8t", "g8t", "m8t", "A8t", "y8t", "b8t", "<PERSON>", "v8t", "C8t", "Q5e", "E8t", "x8t", "UD", "w8t", "S8t", "I8t", "T8t", "R8t", "D8t", "k8t", "U5e", "B8t", "S5", "w5", "Jte", "M8t", "F8t", "Xte", "N8t", "P8t", "H5e", "$5e", "G5e", "L8t", "Q8t", "O8t", "U8t", "V8t", "W8t", "J8t", "c4e", "nHt", "iHt", "sHt", "oHt", "cHt", "lHt", "uHt", "dHt", "tre", "fHt", "f4e", "pHt", "hHt", "gHt", "mHt", "AHt", "yHt", "rre", "bHt", "p4e", "vHt", "h4e", "CHt", "EHt", "av", "b4e", "xHt", "wHt", "SHt", "IHt", "THt", "RHt", "DHt", "kHt", "v4e", "BHt", "MHt", "FHt", "C4e", "NHt", "PHt", "LHt", "QHt", "OHt", "UHt", "qHt", "nre", "ire", "U5", "E4e", "VHt", "WHt", "HHt", "$Ht", "lre", "W5", "jD", "GHt", "w4e", "S4e", "KHt", "YHt", "I4e", "x4e", "jHt", "zHt", "JHt", "XHt", "ZHt", "r$t", "n$t", "i$t", "fre", "pre", "D4e", "s$t", "o$t", "B4e", "hre", "h$t", "g$t", "m$t", "A$t", "y$t", "b$t", "E$t", "_$t", "I$t", "T$t", "R$t", "yre", "D$t", "k$t", "B$t", "M$t", "bre", "F$t", "W4e", "N$t", "P$t", "Q$t", "$$t", "G$t", "K$t", "Y$t", "j$t", "z$t", "iGt", "sGt", "oGt", "aGt", "cGt", "lGt", "uGt", "<PERSON><PERSON>", "bGt", "vGt", "CGt", "EGt", "_Gt", "xGt", "xre", "LGt", "QGt", "OGt", "_re", "UGt", "qGt", "hVe", "gVe", "mVe", "HGt", "AVe", "$Gt", "ts", "C6e", "Lse", "qe", "tr", "$B", "Use", "Vv", "Wv", "qse", "bW", "Vse", "k7e", "B7e", "vW", "if", "Kse", "F7e", "<PERSON><PERSON>", "N7e", "P7e", "L7e", "Q7e", "jse", "O7e", "U7e", "<PERSON>e", "wW", "MKe", "ao", "pd", "koe", "<PERSON><PERSON>", "<PERSON>", "TW", "RW", "IS", "zB", "Foe", "FKe", "Noe", "Yg", "<PERSON>", "XB", "OKe", "UKe", "DW", "qKe", "IW", "VKe", "WKe", "HKe", "kW", "$Ke", "BW", "ZB", "FW", "r2", "GKe", "Yv", "uYe", "jg", "yae", "u2", "<PERSON><PERSON>", "zg", "dYe", "<PERSON>", "bae", "vae", "pYe", "Jv", "gYe", "AYe", "He", "Oa", "Is", "Xv", "di", "<PERSON><PERSON>", "jae", "cn", "zae", "arguments", "return", "vYe", "b2", "Je", "C2", "ece", "gr", "rce", "QYe", "nce", "OYe", "UYe", "qYe", "VYe", "Zv", "zW", "ice", "sce", "mze", "bce", "o8", "yze", "Et", "Ee", "dC", "hde", "Ade", "um", "_d", "wI", "ZH", "oy", "CM", "urt", "RC", "TI", "e$", "Cde", "drt", "frt", "t$", "r$", "n$", "DC", "<PERSON><PERSON>", "i$", "DI", "s$", "EM", "kI", "o$", "a$", "BI", "_de", "kC", "xde", "wde", "Sde", "BC", "prt", "Ide", "_M", "xM", "c$", "l$", "wM", "Tde", "Rde", "Dde", "_st", "by", "NG", "PG", "nge", "ige", "vIe", "CIe", "<PERSON><PERSON>", "EIe", "f_", "ks", "ls", "xl", "qd", "Lc", "Ym", "SSt", "ISt", "DT", "kT", "X", "UQ", "ji", "UBe", "<PERSON><PERSON>", "yJ", "VBe", "jh", "zMt", "GBe", "lR", "GQ", "Xa", "JMt", "B_", "Uc", "zh", "sA", "<PERSON><PERSON>", "M_", "wJ", "KQ", "pb", "Ca", "ZBe", "tFt", "e2e", "F_", "t2e", "Vr", "oA", "Co", "Jh", "hb", "gb", "Vc", "IJ", "jQ", "r2e", "Dn", "Eo", "n2e", "i2e", "zQ", "s2e", "uR", "JQ", "TJ", "o2e", "sFt", "a2e", "js", "l2e", "u2e", "DJ", "pR", "XQ", "ZQ", "d2e", "eO", "tO", "rO", "nO", "f2e", "p2e", "O_", "ps", "Ap", "y2e", "cFt", "jd", "D2e", "<PERSON>a", "Ab", "Bu", "yb", "k2e", "V_", "F2e", "P2e", "OJ", "L2e", "WJ", "Q2e", "O2e", "U2e", "BFt", "yp", "bO", "MFt", "W2e", "H2e", "$2e", "FFt", "NFt", "PFt", "LFt", "vO", "G2e", "K2e", "Y2e", "$J", "UFe", "qFe", "Eb", "vt", "_a", "MLt", "BO", "FLt", "$_", "tg", "PLt", "LLt", "bp", "jFe", "QLt", "zFe", "OLt", "kl", "pX", "JFe", "XFe", "$Fe", "gX", "$Lt", "JLt", "ZFe", "XLt", "MO", "ZLt", "eQt", "tQt", "GFe", "iQt", "oQt", "wX", "eNe", "lQt", "uQt", "dQt", "fQt", "ea", "QO", "rNe", "TR", "RR", "pQt", "fA", "hQt", "fNe", "aNe", "TX", "cNe", "gQt", "lNe", "RX", "yQt", "hNe", "gNe", "mNe", "CQt", "hA", "_Qt", "bNe", "kX", "vNe", "CNe", "RQt", "MR", "ENe", "xNe", "_Ne", "DQt", "qO", "qNe", "kQt", "BQt", "MQt", "FQt", "NQt", "VNe", "NR", "FX", "NX", "VQt", "GQt", "KQt", "SNe", "YQt", "LX", "eOt", "tOt", "rOt", "iOt", "sOt", "oOt", "cOt", "lOt", "uOt", "dOt", "fOt", "pOt", "mOt", "AOt", "bOt", "vOt", "xOt", "wOt", "SOt", "TOt", "DOt", "kOt", "BOt", "MOt", "LOt", "QOt", "qOt", "VOt", "$Ot", "GOt", "TNe", "Ep", "wb", "$c", "J_", "XOt", "ZOt", "eUt", "RNe", "tUt", "rUt", "XNe", "St", "$O", "AA", "kNe", "X_", "WX", "Fs", "YO", "HX", "nUt", "iUt", "sUt", "oUt", "aUt", "BNe", "cUt", "lUt", "uUt", "dUt", "Z_", "fUt", "ZNe", "pUt", "hUt", "NUt", "PUt", "QUt", "MNe", "OX", "OUt", "KO", "QR", "FNe", "NNe", "UUt", "qUt", "FR", "cPe", "VUt", "WUt", "PNe", "LNe", "HUt", "QNe", "$Ut", "GUt", "ONe", "KUt", "YUt", "UNe", "ng", "jUt", "qX", "zUt", "GX", "OR", "YX", "fPe", "pPe", "zO", "JO", "gPe", "hPe", "o3t", "CPe", "zX", "called", "rx", "SPe", "dU", "nc", "TPe", "RPe", "oZ", "c3t", "l3t", "u3t", "nx", "ig", "DPe", "pU", "hU", "NPe", "PPe", "d3t", "gU", "LPe", "VR", "f3t", "OPe", "kPe", "p3t", "<PERSON><PERSON>", "mU", "AU", "h3t", "FPe", "bU", "VPe", "m3t", "A3t", "WPe", "HPe", "$Pe", "y3t", "b3t", "aZ", "bA", "MU", "lZ", "C3t", "XPe", "E3t", "_3t", "ZPe", "eLe", "tLe", "uZ", "S3t", "rLe", "nLe", "iLe", "fLe", "vA", "Ax", "hLe", "GU", "hZ", "gLe", "gZ", "R3t", "mZ", "mLe", "AZ", "ALe", "D3t", "yLe", "bLe", "Nl", "vLe", "KU", "ELe", "kb", "YU", "jU", "_Le", "jR", "xLe", "wLe", "k3t", "XU", "B3t", "TLe", "bZ", "vZ", "kLe", "BLe", "H3t", "$3t", "G3t", "CZ", "K3t", "Y3t", "j3t", "z3t", "ZU", "ag", "J3t", "X3t", "Z3t", "eqt", "tqt", "RLe", "yx", "ef", "MLe", "rqt", "FLe", "nqt", "iqt", "NLe", "t3", "LLe", "QLe", "sqt", "JR", "OLe", "wp", "XR", "ZR", "eD", "tD", "xZ", "ULe", "aqt", "cg", "DZ", "qLe", "kZ", "VLe", "r3", "Ps", "WLe", "HLe", "$Le", "cqt", "GLe", "Sp", "KLe", "to", "dqt", "zLe", "fqt", "pqt", "MZ", "FZ", "YLe", "gqt", "mqt", "BZ", "ra", "rD", "OZ", "UZ", "JLe", "NZ", "Aqt", "PZ", "i3", "XLe", "qZ", "VZ", "ZLe", "eQe", "tQe", "rQe", "bqt", "iQe", "o3", "WZ", "a3", "sQe", "vqt", "Cqt", "oQe", "Eqt", "Ip", "xqt", "Mb", "IQe", "HZ", "wqt", "Sqt", "$Z", "Cx", "Ex", "TQe", "Iqt", "NQe", "Dqt", "PQe", "kqt", "LQe", "UQe", "qQe", "Bqt", "_x", "VQe", "Nqt", "u3", "nD", "HQe", "Pqt", "Lqt", "YZ", "d3", "Qqt", "Oqt", "Uqt", "qqt", "Vqt", "$Qe", "jZ", "GQe", "Wqt", "Hqt", "f3", "xx", "YQe", "$qt", "Gqt", "Kqt", "Yqt", "jqt", "zZ", "g3", "zqt", "jQe", "Jqt", "Xqt", "Zqt", "e5t", "t5t", "zQe", "JZ", "JQe", "XZ", "r5t", "XQe", "ZQe", "n5t", "eOe", "ZZ", "tOe", "eee", "i5t", "rOe", "s5t", "oOe", "aOe", "t4t", "N3", "_Oe", "nUe", "iUe", "sUe", "oUe", "Lb", "aUe", "pD", "<PERSON><PERSON>", "Fee", "kn", "cUe", "V3", "Rp", "lUe", "uUe", "yi", "cD", "lD", "dUe", "bee", "vee", "Dp", "<PERSON><PERSON>", "<PERSON><PERSON>", "fUe", "gUe", "<PERSON><PERSON>", "Kc", "o4t", "Oee", "ms", "<PERSON><PERSON>", "jc", "l4t", "bUe", "vUe", "u4t", "d4t", "SUe", "Yc", "sc", "IUe", "TUe", "RUe", "p4t", "kUe", "h4t", "g4t", "m4t", "A4t", "y4t", "b4t", "v4t", "C4t", "pee", "_4t", "x4t", "w4t", "S4t", "I4t", "T4t", "R4t", "D4t", "k4t", "B4t", "VUe", "M4t", "wOe", "F4t", "$ee", "Pb", "P4t", "SOe", "O4t", "U4t", "IOe", "gee", "W4t", "$4t", "G4t", "Y4t", "z4t", "J4t", "Z4t", "tVt", "nVt", "sVt", "oVt", "<PERSON><PERSON>", "cVt", "e3e", "uVt", "n3e", "dVt", "G3", "fVt", "pVt", "a3e", "hVt", "gVt", "<PERSON><PERSON>", "kOe", "f3e", "bVt", "OOe", "EVt", "xVt", "ete", "wVt", "WOe", "Ol", "yee", "TVt", "DVt", "Q3", "BVt", "MVt", "FVt", "QVt", "UVt", "qVt", "M3e", "YOe", "N3e", "Q3e", "GVt", "KVt", "YVt", "jVt", "XVt", "e6t", "V3e", "t6t", "n6t", "s6t", "o6t", "c6t", "u6t", "f6t", "h6t", "g6t", "Y3e", "A6t", "y6t", "b6t", "v6t", "C6t", "_6t", "w6t", "S6t", "Y3", "I6t", "R6t", "X3e", "D6t", "D3", "B6t", "M6t", "F6t", "Q6t", "O6t", "U6t", "q6t", "V6t", "W6t", "H6t", "G6t", "XOe", "K6t", "Y6t", "j6t", "z6t", "ZOe", "X6t", "Z6t", "eWt", "tWt", "rWt", "iWt", "sWt", "iqe", "oWt", "rte", "aWt", "fg", "cWt", "Sqe", "<PERSON>u", "xD", "Vx", "Wx", "Xi", "Bqe", "Mp", "jqe", "t5", "zqe", "Jqe", "TA", "Yx", "r5", "CWt", "Zqe", "EWt", "xWt", "wWt", "GGt", "U", "KGt", "YGt", "jGt", "bVe", "zGt", "JGt", "XGt", "CVe", "ZGt", "e9t", "t9t", "<PERSON><PERSON>", "n9t", "d4", "na", "Up", "RVe", "TVe", "<PERSON>e", "ko", "y4", "Tg", "DVe", "b4", "kVe", "Mre", "Fre", "R4", "NVe", "D4", "PVe", "d9t", "LVe", "QVe", "ck", "l0", "u0", "f9t", "WVe", "p9t", "vw", "KVe", "h9t", "g9t", "m9t", "A9t", "y9t", "HVe", "Cw", "$Ve", "uk", "Nre", "YVe", "JVe", "XVe", "q4", "C9t", "r6e", "Lre", "w9t", "S9t", "Qre", "T9t", "H4", "R9t", "s6e", "d0", "L9t", "h6e", "A6e", "y6e", "b6e", "o7t", "B6e", "_V", "mv", "f7t", "p7t", "h7t", "g7t", "m7t", "SV", "L6e", "FV", "_ne", "Fw", "g0", "C7t", "E7t", "qV", "U6e", "q6e", "D7t", "k7t", "K6e", "qne", "Ik", "Tk", "j6e", "Hl", "rWe", "F7t", "ce", "J6e", "nWe"], "strings": ["use strict", "object", "function", "default", "__esModule", "return this", "[object Null]", "[object Undefined]", "[object Symbol]", "symbol", "number", "string", "Expected a function", "max<PERSON><PERSON>", "trailing", "leading", "crypto", "Stringified UUID is invalid", "uuid.v1(): Can't create more than 10M uuids/sec", "Invalid UUID", "Namespace must be array-like (16 iterable integer values, 0-255)", "6ba7b810-9dad-11d1-80b4-00c04fd430c8", "6ba7b811-9dad-11d1-80b4-00c04fd430c8", "utf8", "sha1", "00000000-0000-0000-0000-000000000000", "Bad alphabet", "Number \"", "\" contains of non-alphabetic digits (", "01234567", "0123456789", "0123456789abcdef", "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ!#$%&'()*+-./:<=>?@[]^_`{|}~", "**********************************************************", "0123456789abcdefghijklmnopqrstuvwxyz", "The provided Alphabet has duplicate characters resulting in unreliable results", "4.17.21", "Unsupported core-js use. Try https://npms.io/search?q=ponyfill.", "Invalid `variable` option passed into `_.template`", "__lodash_hash_undefined__", "__lodash_placeholder__", "bind", "<PERSON><PERSON><PERSON>", "curry", "curryRight", "flip", "partial", "partialRight", "rearg", "[object Arguments]", "[object Array]", "[object AsyncFunction]", "[object Boolean]", "[object Date]", "[object DOMException]", "[object Error]", "[object Function]", "[object GeneratorFunction]", "[object Map]", "[object Number]", "[object Object]", "[object Promise]", "[object Proxy]", "[object RegExp]", "[object Set]", "[object String]", "[object WeakMap]", "[object WeakSet]", "[object A<PERSON>y<PERSON><PERSON>er]", "[object DataView]", "[object Float32Array]", "[object Float64Array]", "[object Int8Array]", "[object Int16Array]", "[object Int32Array]", "[object Uint8Array]", "[object Uint8ClampedArray]", "[object Uint16Array]", "[object Uint32Array]", "])(?:(?!\\1)[^\\\\]|\\\\.)*?\\1)\\]/,n6=/^\\w*$/,Vw=/[^.[\\]]+|\\[(?:(-?\\d+(?:\\.\\d+)?)|([\"", ",Kw=", "+_v+", ",Lk=", "+Pk+", ",Ma=", "+Bk+", ",xv=", ",m6=", "+rl+", ",Yw=", "+Mk+", ",y0=", "+_v+Pk+xv+rl+Mk+Nk+", ",Bg=", ",wv=", "+Ma+", "+Bg+", ",Sv=", ",Kl=", ",Mg=", ",Kp=", "+Nk+", ",Qk=", ",Ok=", "+Yw+", "+y0+", ",Yp=", "+Kp+", ",Uk=", "+Gw+", ",qk=", ",Vk=wv+", ",Wk=", "+od+", ",jw=", "+Qk+", "+[Sv,Kl,Mg].join(", "+Wk+Vk+", ",Iv=", ",A6=", ",Hk=Wk+Vk+jw,$k=", "+[m6,Kl,Mg].join(", "+Hk,zw=", "+[Sv+Ma+", ",<PERSON>,Kl,Mg,Kw].join(", ",Gk=RegExp(Gw,", "),y6=RegExp(Ma,", "),Jw=RegExp(Bg+", "+zw+Hk,", "),Kk=RegExp([Kp+", "+Uk+", "+[Lk,Kp,", "].join(", ",Yp+", "+qk+", "+[Lk,Kp+Ok,", ",Kp+", "+Ok+", "+Uk,Kp+", "+qk,A6,Iv,xv,$k].join(", "),Yk=RegExp(", "+Qk+_v+Bk+od+", "),jk=/[a-z][A-Z]|[A-Z]{2}[a-z]|[0-9][a-zA-Z]|[a-zA-Z][0-9]|[^a-zA-Z0-9 ]/,b6=[", ",\\u00C1:", ",\\u00C2:", ",\\u00C3:", ",\\u00C4:", ",\\u00C5:", ",\\u00E0:", ",\\u00E1:", ",\\u00E2:", ",\\u00E3:", ",\\u00E4:", ",\\u00E5:", ",\\u00C7:", ",\\u00E7:", ",\\u00D0:", ",\\u00F0:", ",\\u00C8:", ",\\u00C9:", ",\\u00CA:", ",\\u00CB:", ",\\u00E8:", ",\\u00E9:", ",\\u00EA:", ",\\u00EB:", ",\\u00CC:", ",\\u00CD:", ",\\u00CE:", ",\\u00CF:", ",\\u00EC:", ",\\u00ED:", ",\\u00EE:", ",\\u00EF:", ",\\u00D1:", ",\\u00F1:", ",\\u00D2:", ",\\u00D3:", ",\\u00D4:", ",\\u00D5:", ",\\u00D6:", ",\\u00D8:", ",\\u00F2:", ",\\u00F3:", ",\\u00F4:", ",\\u00F5:", ",\\u00F6:", ",\\u00F8:", ",\\u00D9:", ",\\u00DA:", ",\\u00DB:", ",\\u00DC:", ",\\u00F9:", ",\\u00FA:", ",\\u00FB:", ",\\u00FC:", ",\\u00DD:", ",\\u00FD:", ",\\u00FF:", ",\\u00C6:", ",\\u00E6:", ",\\u00DE:", ",\\u00FE:", ",\\u00DF:", ",\\u0100:", ",\\u0102:", ",\\u0104:", ",\\u0101:", ",\\u0103:", ",\\u0105:", ",\\u0106:", ",\\u0108:", ",\\u010A:", ",\\u010C:", ",\\u0107:", ",\\u0109:", ",\\u010B:", ",\\u010D:", ",\\u010E:", ",\\u0110:", ",\\u010F:", ",\\u0111:", ",\\u0112:", ",\\u0114:", ",\\u0116:", ",\\u0118:", ",\\u011A:", ",\\u0113:", ",\\u0115:", ",\\u0117:", ",\\u0119:", ",\\u011B:", ",\\u011C:", ",\\u011E:", ",\\u0120:", ",\\u0122:", ",\\u011D:", ",\\u011F:", ",\\u0121:", ",\\u0123:", ",\\u0124:", ",\\u0126:", ",\\u0125:", ",\\u0127:", ",\\u0128:", ",\\u012A:", ",\\u012C:", ",\\u012E:", ",\\u0130:", ",\\u0129:", ",\\u012B:", ",\\u012D:", ",\\u012F:", ",\\u0131:", ",\\u0134:", ",\\u0135:", ",\\u0136:", ",\\u0137:", ",\\u0138:", ",\\u0139:", ",\\u013B:", ",\\u013D:", ",\\u013F:", ",\\u0141:", ",\\u013A:", ",\\u013C:", ",\\u013E:", ",\\u0140:", ",\\u0142:", ",\\u0143:", ",\\u0145:", ",\\u0147:", ",\\u014A:", ",\\u0144:", ",\\u0146:", ",\\u0148:", ",\\u014B:", ",\\u014C:", ",\\u014E:", ",\\u0150:", ",\\u014D:", ",\\u014F:", ",\\u0151:", ",\\u0154:", ",\\u0156:", ",\\u0158:", ",\\u0155:", ",\\u0157:", ",\\u0159:", ",\\u015A:", ",\\u015C:", ",\\u015E:", ",\\u0160:", ",\\u015B:", ",\\u015D:", ",\\u015F:", ",\\u0161:", ",\\u0162:", ",\\u0164:", ",\\u0166:", ",\\u0163:", ",\\u0165:", ",\\u0167:", ",\\u0168:", ",\\u016A:", ",\\u016C:", ",\\u016E:", ",\\u0170:", ",\\u0172:", ",\\u0169:", ",\\u016B:", ",\\u016D:", ",\\u016F:", ",\\u0171:", ",\\u0173:", ",\\u0174:", ",\\u0175:", ",\\u0176:", ",\\u0177:", ",\\u0178:", ",\\u0179:", ",\\u017B:", ",\\u017D:", ",\\u017A:", ",\\u017C:", ",\\u017E:", ",\\u0132:", ",\\u0133:", ",\\u0152:", ",\\u0153:", ",\\u0149:", "n\",\\u017F:\"s\"},zk={\"&\":\"&amp;\",\"<\":\"&lt;\",\">\":\"&gt;\",", "\":\"&#39;\"},Ef={\"&amp;\":\"&\",\"&lt;\":\"<\",\"&gt;\":\">\",\"&quot;\":", "\"},Xw={\"\\\\\":\"\\\\\",\"", ",je=w6((m.escape||Ev).source+", "+ve.source+", "+(ve===kg?Hw:Ev).source+", "+(m.evaluate||Ev).source+", "),ht=", "+(Jn.call(m,", ")?(m.sourceURL+", ").replace(/\\s/g,", "+ ++v6+", "):Te).replace(Gl,", ").replace(m0,", "),Te=", "+(Qt||", ")+`) {\n`+(Qt?", ":`obj || (obj = {});\n`)+", "+(z?", ")}function L9e(h,m,C){if(h=qn(h),h&&(C||m===e))return h.replace(Ww,", ");if(!h||!(m=il(m)))return h;var S=ld(h),D=Ng(S,ld(m));return Vg(S,D).join(", ")}function Q9e(h,m){var C=H,S=q;if(Hi(m)){var D=", "in m?m.separator:D;C=", "in m?Er(m.length):C,S=", ")+m.toUpperCase()}),fW=Fie(", "),p7e=IB(function(h,m){return h/m},1),h7e=Y6(", "],function(h){Q[h].placeholder=Q}),No([", ")}),S},Xr.prototype[h+", "]=function(C){return this.reverse()[h](C).reverse()}}),No([", "],function(h,m){var C=", "+(m?", ");Xr.prototype[h]=function(){return this[C](1).value()[0]}}),No([", "+(m==", "],function(h){var m=sB[h],C=/^(?:push|sort|unshift)$/.test(h)?", ";Jn.call(Nv,S)||(Nv[S]=[]),Nv[S].push({name:m,func:C})}}),Nv[SB(e,A).name]=[{name:", "&&typeof define.amd==", ";Object.defineProperty(_n,", "||t==='", "}t++}}function vze(e,t){if(t==='", "')return e;if(e==='", "')return t;if(typeof e==", ")return t instanceof j0||e[e.length-1]!=='", "?void 0:typeof t!=\"string\"?`${e.slice(0,-1)}${t}\"`:t[0]===", "'?e.slice(0,-1)+t.slice(1):void 0;if(typeof t==", "&&t[0]==='", "else", "then", "return", ");return this._endBlockNode(eI)}try(t,r,n){if(!r&&!n)throw new Error(", "catch", "finally", " + ${e} + ", "nullable", "type", "keyword", "schema", "schemaPath", "errSchemaPath", "topSchemaRef", ");return{schema:n,schemaPath:i,topSchemaRef:o,errSchemaPath:s}}throw new Error(", "data", "dataProp", "trackErrors", "ajv/dist/runtime/validation_error", ",this.opts.code.process&&(u=this.opts.code.process(u,e));let p=new Function(", ",s=j8[i];s&&(e=s.serialize(e,t));let o=e,a=e.nss;return o.path=", "suffix", "same-document", "relative", "absolute", "URI is not a ", " reference.", "Host's domain name can not be converted to ASCII: ", "URI can not be parsed.", "require(\"ajv/dist/runtime/uri\").default", "KeywordCxt", "stringify", "Name", "CodeGen", "new RegExp", "removeAdditional", "useDefaults", "coerceTypes", "validate", "serialize", "parse", "wrapper", "root", "pattern", "formats", "validate$data", "func", "Error", "`validateFormats: false` can be used instead.", "\"nullable\" keyword is supported by default.", "Deprecated jsPropertySyntax can be used instead.", "Deprecated ignoreKeywordsWithRef can be used instead.", "Pass empty schema with $id that should be ignored to ajv.addSchema.", "Use option `code: {process: (code, schemaEnv: object) => string}`", "Use option `code: {source: true}`", "It is default now, see option `strict`.", "\"uniqueItems\" keyword is always validated.", "Disable strict mode or pass `true` to `ajv.addFormat` (or `formats` option).", "Map is used as cache, schema object as key.", "It is default now.", "\"minLength\"/\"maxLength\" account for unicode characters by default.", "NOT SUPPORTED", "DEPRECATED", "warn", "$async", "no schema with key or ref \"${t}\"", "options.loadSchema should be a function", "AnySchema ${u} is loaded but ${d} cannot be resolved", "schema ${o} must be string", "boolean", "$schema must be a string", "meta-schema not available", "schema is invalid: ", "undefined", "ajv.removeSchema: invalid parameter", "these parameters are deprecated, see docs for addKeyword", "addKeywords: keyword must be string or non-empty array", "invalid addKeywords parameters", "No errors", "${n}${i.instancePath} ${i.message}", "schema must be object", "schema must be object or boolean", "schema with key or id \"${t}\" already exists", "ajv implementation error", "error", "${r}: option ${i}. ${e[s]}", "keywords option as map is deprecated, pass array", "logger must implement log, warn and error methods", "Keyword ${n} is already defined", "Keyword ${n} has invalid name", "code", "$data keyword must have \"code\" or \"validate\" function", "keyword with \"post\" flag cannot have \"type\"", "rule ${r} is not defined", "https://raw.githubusercontent.com/ajv-validator/ajv/master/lib/refs/data.json#", "NOT SUPPORTED: keyword \"id\", use \"$id\" for schema ID", "$ref", "${g}.validate", "valid", "${r.scopeValue(\"wrapper\",{ref:t})}.validate", "async schema referenced by sync schema", "await ${(0,jle.callValidateCode)(e,t,l)}", "!(${A} instanceof ${s.ValidationError})", "${g}.errors", "${CC.default.vErrors} === null ? ${A} : ${CC.default.vErrors}.concat(${A})", "${CC.default.vErrors}.length", "props", "${g}.evaluated.props", "items", "${g}.evaluated.items", "$schema", "$defs", "$vocabulary", "$comment", "definitions", "must be ${oM[e].okStr} ${t}", "{comparison: ${oM[e].okStr}, limit: ${t}}", "${r} ${oM[t].fail} ${n} || isNaN(${r})", "must be multiple of ${e}", "{multipleOf: ${e}}", "multipleOf", "Math.abs(Math.round(${o}) - ${o}) > 1e-${s}", "${o} !== parseInt(${o})", "(${n} === 0 || (${o} = ${r}/${n}, ${a}))", "require(\"ajv/dist/runtime/ucs2length\").default", "max<PERSON><PERSON><PERSON>", "more", "fewer", "must NOT have ${r} than ${t} characters", "{limit: ${e}}", "<PERSON><PERSON><PERSON><PERSON>", "${r}.length", "${(0,LZe.useFunc)(e.gen,QZe.default)}(${r})", "${o} ${s} ${n}", "must match pattern \"${e}\"", "{pattern: ${e}}", "(new RegExp(${i}, ${o}))", "!${a}.test(${t})", "maxProperties", "must NOT have ${r} than ${t} properties", "minProperties", "Object.keys(${r}).length ${i} ${n}", "must have required property '${e}'", "{missingProperty: ${e}}", "required", "array", "required property \"${A}\" is not defined at \"${y}\" (strictRequired)", "missing", "prop", "maxItems", "must NOT have ${r} than ${t} items", "minItems", "${r}.length ${i} ${n}", "require(\"ajv/dist/runtime/equal\").default", "must NOT have duplicate items (items ## ${t} and ${e} are identical)", "{i: ${e}, j: ${t}}", "uniqueItems", "${o} === false", "${g} > 1", "item", "indices", ";${g}--;", "${r}[${g}]", "continue", "typeof ${y} == \"string\"", "${y} += \"_\"", "typeof ${v}[${y}] == \"number\"", "${v}[${y}]", "${v}[${y}] = ${g}", "outer", "${A} = ${g}; ${A}--;", "${y}(${r}[${g}], ${r}[${A}])", "must be equal to constant", "{allowedValue: ${e}}", "const", "!${(0,tet.useFunc)(t,ret.default)}(${r}, ${i})", "${s} !== ${r}", "must be equal to one of the allowed values", "{allowedValues: ${e}}", "enum", "enum must have non-empty array", "vSchema", "${l()}(${r}, ${p})", "${l()}(${r}, ${p}[${g}])", "${r} === ${A}", "must NOT have more than ${e} items", "additionalItems", "\"additionalItems\" is ignored when \"items\" is not an array of schemas", "${i}.length", "${a} <= ${t.length}", "${s}.length", "${l} > ${f}", "\"${o}\" is ${g}-tuple, but minItems or maxItems/${t} are not specified or different at path \"${p}\"", "prefixItems", "must contain at least ${e} valid item(s)", "must contain at least ${e} and no more than ${t} valid item(s)", "{minContains: ${e}}", "{minContains: ${e}, maxContains: ${t}}", "contains", "\"minContains\" == 0 without \"maxContains\": \"contains\" keyword ignored", "\"minContains\" > \"maxContains\" is always invalid", "${u} >= ${o}", "${A} && ${u} <= ${a}", "${i}.length > 0", "_valid", "count", "${A}++", "${A} >= ${o}", "${A} > ${a}", "property", "properties", "must have ${n} ${r} when property ${e} is present", "{property: ${e},\n    missingProperty: ${n},\n    depsCount: ${t},\n    deps: ${r}}", "dependencies", "__proto__", "${c} && (${(0,EI.checkMissingProp)(e,a,s)})", "property name must be valid", "{propertyName: ${e.propertyName}}", "propertyNames", "must NOT have additional properties", "{additionalProperty: ${e.additionalProperty}}", "additionalProperties", "${s} === ${qet.default.errors}", "${y} === ${v}", "${(0,dM.usePattern)(e,v)}.test(${y})", "delete ${i}[${y}]", "failing", "patternProperties", "property ${y} matches pattern ${A} (use allowMatchingProperties)", "${(0,Rue.usePattern)(e,A)}.test(${y})", "${d}[${y}]", "must NOT be valid", "anyOf", "must match a schema in anyOf", "must match exactly one schema in oneOf", "{passingSchemas: ${e.passing}}", "oneOf", "passing", "${c} && ${o}", "[${a}, ${d}]", "allOf", "must match \"${e.ifClause}\" schema", "{failingKeyword: ${e.ifClause}}", "\"if\" without \"then\" and \"else\" is ignored", "if<PERSON>lause", "${u}", "\"${e}\" without \"if\" is ignored", "must match format \"${e}\"", "{format: ${e}}", "format", "fDef", "${g}[${o}]", "fType", "typeof ${A} == \"object\" && !(${A} instanceof RegExp)", "${A}.type || \"string\"", "${A}.validate", "\"string\"", "${o} && !${b}", "(${A}.async ? await ${b}(${n}) : ${b}(${n}))", "${b}(${n})", "(typeof ${b} == \"function\" ? ${_} : ${b}.test(${n}))", "${b} && ${b} !== true && ${y} === ${t} && !${w}", "unknown format \"${s}\" ignored in schema at path \"${l}\"", "${c.code.formats}${(0,bs.getProperty)(s)}", "${N}.validate", "async format in sync schema", "await ${b}(${n})", "${b}.test(${n})", "title", "description", "deprecated", "readOnly", "writeOnly", "examples", "contentMediaType", "contentEncoding", "contentSchema", "mapping", "tag \"${t}\" must be string", "value of tag \"${t}\" must be in oneOf", "{error: ${e}, tag: ${r}, tagValue: ${t}}", "discriminator", "discriminator: requires discriminator option", "discriminator: requires propertyName", "discriminator: mapping is not supported", "discriminator: requires oneOf keyword", "${r}${(0,_C.getProperty)(a)}", "typeof ${l} == \"string\"", "${l} === ${g}", "discriminator: oneOf subschemas (or referenced schemas) must have \"properties/${a}\"", "discriminator: \"${a}\" must be required", "discriminator: \"properties/${a}\" must have \"const\" or \"enum\"", "discriminator: \"${a}\" values must be unique strings", "http://json-schema.org/draft-07/schema#", "Core schema meta-schema", "integer", "#/definitions/nonNegativeInteger", "null", "uri-reference", "#/definitions/nonNegativeIntegerDefault0", "regex", "#/definitions/schemaArray", "#/definitions/stringArray", "#/definitions/simpleTypes", "/properties", "http://json-schema.org/draft-07/schema", "http://json-schema.org/schema", "ValidationError", "Missing<PERSON>ef<PERSON><PERSON><PERSON>", "date-time", "iso-time", "iso-date-time", "\"()*+,;=]|%[0-9a-f]{2})*)(?::\\d*)?(?:\\/(?:[a-z0-9\\-._~!$&", "()*+,;=:@]|%[0-9a-f]{2})*)*|\\/(?:(?:[a-z0-9\\-._~!$&'", "\"()*+,;=:@]|%[0-9a-f]{2})*)*)?|(?:[a-z0-9\\-._~!$&", "()*+,;=:@]|%[0-9a-f]{2})+(?:\\/(?:[a-z0-9\\-._~!$&'", "\"()*+,;=:@/?]|%[0-9a-f]{2})*)?(?:#(?:[a-z0-9\\-._~!$&", "()*+,;=:@/?]|%[0-9a-f]{2})*)?$/i,", ":/^(?:(?:[^\\x00-\\x20", "{|}~-]+(?:\\.[a-z0-9!#$%&'*+/=?^_", "json-pointer", "json-pointer-uri-fragment", ",params:({keyword:e,schemaCode:t})=>(0,Ed._)", ");e.fail$data((0,Ed.or)((0,Ed._)", ",(0,Ed._)", "${i}", "${f}", "compare", ");let g=t.scopeValue(\"formats\",{key:f,ref:p,code:o.code.formats?(0,Ed._)", ":void 0});e.fail$data(d(g))}function d(f){return(0,Ed._)", "${e}", "ajv-formats/dist/formats", "&&(r+=", "'>]*`,$t.inside);$t.attr=Ic($t.attr,", ",Efe(", "));$t.pseudo=Ic($t.pseudo,", "));$t.simple=Ic($t.simple,", ",$t.pseudo);$t.simple=Ic($t.simple,", ",$t.attr);$t.ident=Ic($t.ident,", ",$t.cssid);$t.str_escape=Ic($t.str_escape,", ",$t.escape);var QI=function(e){for(var t=e.replace(/^\\s+|\\s+$/g,", ",i.push(UM(o,!0)),i.push(UM(a));else throw new SyntaxError(", ");for(;a=$t.simple.exec(t);)t=t.substring(a[0].length),i.push(UM(a));if(t[0]===", "){n.push(LI.noop(P$(i)));break}}else c=", ";if(!LI[c])throw new SyntaxError(", "?fo[", "]:fo.type(e);if(e[1])return e[1][0]===", "?fo.attr(", ",fm(e[1].substring(1)),!1):fo.attr(", ";return this.scheme!==void 0&&(e+=this.scheme+", "),this.isAbsolute()&&(e+=", ",(this.username||this.password)&&(e+=this.username||", ",this.password&&(e+=", "+this.password),e+=", "),this.host&&(e+=this.host)),this.port!==void 0&&(e+=", "+this.port),this.path!==void 0&&(e+=this.path),this.query!==void 0&&(e+=", "+this.query),this.fragment!==void 0&&(e+=", "+a;var c=o.lastIndexOf(", ");return c===-1?a:o.substring(0,c+1)+a}function s(o){if(!o)return o;for(var a=", ";o.length>0;){if(o===", "||o===", "){o=", ";break}var c=o.substring(0,2),l=o.substring(0,3),u=o.substring(0,4);if(l===", ")o=o.substring(3);else if(c===", ")o=o.substring(2);else if(l===", "+o.substring(3);else if(c===", "&&o.length===2)o=", ";else if(u===", "||l===", "&&o.length===3)o=", "+o.substring(4),a=a.replace(/\\/?[^\\/]*$/,", ";Cpe.exports={Event:FC(),UIEvent:f$(),MouseEvent:h$(),CustomEvent:vpe()}});var _pe=x(VC=>{", ";Object.defineProperty(VC,", "+t.charAt(1)).toLowerCase()}VC.hyphenate=Epe});var jM=x((her,Tpe)=>{", ").toLowerCase()}function Ipe(e){this._element=e}var wpe=", ";for(var r in e.property)t&&(t+=", "),t+=r+", "+e.property[r],e.priority[r]&&(t+=", "+e.priority[r]),t+=", ")},set:function(e){this._element.setAttribute(", "}},getPropertyPriority:{value:function(e){return e=e.toLowerCase(),this._parsed.priority[e]||", "}},setProperty:{value:function(e,t,r){if(e=e.toLowerCase(),t==null&&(t=", "),r==null&&(r=", "),t!==WC&&(t=", "+t),t=t.trim(),t===", "){this.removeProperty(e);return}if(!(r!==", "&&r!==WC&&!/^important$/i.test(r))){var n=this._parsed;if(t===WC){if(!n.property[e])return;r!==", "?n.priority[e]=", ":delete n.priority[e]}else{if(t.indexOf(", ")!==-1)return;var i=Spe(e+", "?n.priority[s]=", "},set:function(e){var t=this.href,r=new po(t);r.isAbsolute()&&(e=e.replace(/:+$/,", "+e.port:", "},set:function(e){var t=this.href,r=new po(t);r.isAbsolute()&&r.isAuthorityBased()&&(e=", "+e,e=e.replace(/[^0-9].*$/,", "),e=e.replace(/^0+/,", "),e.length===0&&(e=", "},set:function(e){var t=this.href,r=new po(t);r.isAbsolute()&&r.isHierarchical()&&(e.charAt(0)!==", "&&(e=", "+e.query:", "},set:function(e){var t=this.href,r=new po(t);r.isAbsolute()&&r.isHierarchical()&&(e.charAt(0)===", "+e.fragment},set:function(e){var t=this.href,r=new po(t);e.charAt(0)===", "},set:function(e){var t=this.href,r=new po(t);r.isAbsolute()&&(e=e.replace(/[\\x00-\\x1F\\x7F-\\uFFFF ", "gopher", "http", "https", "constructor", "href", "use-credentials", "anonymous", "no-referrer", "no-referrer-when-downgrade", "same-origin", "origin", "strict-origin", "origin-when-cross-origin", "strict-origin-when-cross-origin", "unsafe-url", "HTMLElement", "style", "MouseEvent", "click", "auto", "true", "false", "enter", "done", "next", "previous", "search", "send", "none", "sentences", "words", "characters", "long", "abort", "canplay", "canplaythrough", "change", "contextmenu", "cuechange", "dblclick", "drag", "dragend", "dragenter", "dragleave", "dragover", "dragstart", "drop", "durationchange", "emptied", "ended", "input", "invalid", "keydown", "keypress", "keyup", "loadeddata", "loadedmetadata", "loadstart", "mousedown", "mousemove", "mouseout", "mouseover", "mouseup", "mousewheel", "pause", "play", "playing", "progress", "ratechange", "readystatechange", "reset", "seeked", "seeking", "select", "show", "stalled", "submit", "suspend", "timeupdate", "volumechange", "waiting", "blur", "focus", "load", "scroll", "HTMLUnknownElement", "HTMLAnchorElement", "area", "HTMLAreaElement", "HTMLBRElement", "base", "HTMLBaseElement", "body", "HTMLBodyElement", "afterprint", "beforeprint", "beforeunload", "hashchange", "message", "offline", "online", "pagehide", "pageshow", "popstate", "resize", "storage", "unload", "button", "HTMLButtonElement", "menu", "post", "dialog", "application/x-www-form-urlencoded", "multipart/form-data", "text/plain", "HTMLDListElement", "HTMLDataElement", "datalist", "HTMLDataListElement", "details", "HTMLDetailsElement", "HTMLDivElement", "embed", "HTMLEmbedElement", "fieldset", "HTMLFieldSetElement", "form", "HTMLFormElement", "accept-charset", "enctype", "HTMLHRElement", "head", "HTMLHeadElement", "HTMLHeadingElement", "html", "HTMLHtmlElement", "iframe", "HTMLIFrameElement", "eager", "lazy", "HTMLImageElement", "unsigned long", "HTMLInputElement", "checkbox", "radio", "value", "checked", "text", "hidden", "email", "password", "datetime", "date", "month", "week", "time", "datetime-local", "range", "color", "file", "image", "verbatim", "latin", "latin-name", "latin-prose", "full-width-latin", "kana", "kana-name", "katakana", "numeric", "keygen", "HTMLKeygenElement", "HTMLLIElement", "label", "HTMLLabelElement", "legend", "HTMLLegendElement", "link", "HTMLLinkElement", "HTMLMapElement", "HTMLMenuElement", "context", "popup", "toolbar", "meta", "HTMLMetaElement", "http-equiv", "meter", "HTMLMeterElement", "HTMLModElement", "HTMLOListElement", "HTMLObjectElement", "optgroup", "HTMLOptGroupElement", "option", "HTMLOptionElement", "selected", "output", "HTMLOutputElement", "HTMLParagraphElement", "param", "HTMLParamElement", "listing", "HTMLPreElement", "HTMLProgressElement", "blockquote", "HTMLQuoteElement", "script", "HTMLScriptElement", "HTMLSelectElement", "span", "HTMLSpanElement", "HTMLStyleElement", "caption", "HTMLTableCaptionElement", "HTMLTableCellElement", "rowgroup", "colgroup", "char", "charoff", "HTMLTableColElement", "limited unsigned long with fallback", "table", "HTMLTableElement", "template", "HTMLTemplateElement", "HTMLTableRowElement", "td,th", "thead", "tfoot", "tbody", "HTMLTableSectionElement", "textarea", "HTMLTextAreaElement", "HTMLTimeElement", "HTMLTitleElement", "HTMLUListElement", "HTMLMediaElement", "metadata", "muted", "HTMLAudioElement", "audio", "HTMLVideoElement", "video", "HTMLTableDataCellElement", "HTMLTableHeaderCellElement", "frameset", "HTMLFrameSetElement", "frame", "HTMLFrameElement", "canvas", "HTMLCanvasElement", "HTMLDialogElement", "menuitem", "HTMLMenuItemElement", "command", "source", "HTMLSourceElement", "track", "HTMLTrackElement", "subtitles", "captions", "descriptions", "chapters", "font", "HTMLFontElement", "HTMLDirectoryElement", "abbr", "address", "article", "aside", "cite", "content", "figcaption", "figure", "footer", "header", "hgroup", "main", "mark", "noscript", "ruby", "samp", "section", "small", "strong", "summary", "acronym", "basefont", "center", "nobr", "noembed", "noframes", "plaintext", "strike", "SVGElement", "SVGSVGElement", "rect", "altGlyph", "altGlyphDef", "altGlyphItem", "animate", "animateColor", "animateMotion", "animateTransform", "circle", "clipPath", "color-profile", "cursor", "defs", "desc", "ellipse", "feBlend", "feColorMatrix", "feComponentTransfer", "feComposite", "feConvolveMatrix", "feDiffuseLighting", "feDisplacementMap", "feDistantLight", "feFlood", "feFuncA", "feFuncB", "feFuncG", "feFuncR", "feG<PERSON><PERSON><PERSON>lur", "feImage", "feMerge", "feMergeNode", "feMorphology", "feOffset", "fePointLight", "feSpecularLighting", "feSpotLight", "feTile", "feTurbulence", "filter", "font-face", "font-face-format", "font-face-name", "font-face-src", "font-face-uri", "foreignObject", "glyph", "glyphRef", "hkern", "line", "linearGradient", "marker", "mask", "missing-glyph", "mpath", "path", "polygon", "polyline", "radialGradient", "stop", "switch", "textPath", "tref", "tspan", "view", "vkern", "about:blank", "loading", "text/html", "application/xml", "Event", "CustomEvent", "UIEvent", "event", "mouseevent", "mutationevent", "uievent", "xmlns", "#document", "BackCompat", "CSS1Compat", "application/xhtml+xml", "root argument is required", "root not a node", "UTF-8", "HTML", "vLink", "aLink", "bgColor", "interactive", "DOMContentLoaded", "complete", "base[href]", "_tagName", "Reilly and Associates\\/\\/DTD HTML 2\\.0\\/\\/|^-\\/\\/O", "|\"[^\"&\\r\\u0000]*\"|[^\\t\\n\\r\\f \"&", "annotation-xml", "encoding", "<PERSON>url", "definitionURL", "\\uFFFF", "should never happen", "http://www.w3.org/1999/xhtml", "mglyph", "malignmark", "\\uFFFD", "DOCTYPE", "[CDATA[", "PUBLIC", "SYSTEM", "bgsound", "thorough", "applet", "marquee", "math", "sarcasm", "face", "size", "PCDATA state", "RCDATA state", "RAWTEXT state", "PLAINTEXT state", "Character", "Comment", "StartTag", "EndTag", "image/svg+xml", "Mozilla", "Netscape", "Gecko", "20100101", "ADDRESS", "ARTICLE", "ASIDE", "AUDIO", "BLOCKQUOTE", "BODY", "CANVAS", "CENTER", "FIELDSET", "FIGCAPTION", "FIGURE", "FOOTER", "FORM", "FRAMESET", "HEADER", "HGROUP", "ISINDEX", "MAIN", "MENU", "NOFRAMES", "NOSCRIPT", "OUTPUT", "SECTION", "TABLE", "TBODY", "TFOOT", "THEAD", "AREA", "BASE", "COMMAND", "EMBED", "INPUT", "KEYGEN", "LINK", "META", "PARAM", "SOURCE", "TRACK", "IFRAME", "SCRIPT", "VIDEO", "setext", "\n    ", "start", "indented", "CODE", "\n\n    ", "fenced", "class", "{3,}", "inlined", "\\\\$1", "/g,'\\\\", "+r+n+", "}};ma.referenceLink={filter:function(e,t){return t.linkStyle===", "&&e.nodeName===", "&&e.getAttribute(", ")},replacement:function(e,t,r){var n=t.getAttribute(", "),i=pF(t.getAttribute(", "));i&&(i=' ", "');var s,o;switch(r.linkReferenceStyle){case", "+n+i;break;case", "+n+i;break;default:var a=this.references.length+1;s=", "+n+i}return this.references.push(o),s},references:[],append:function(e){var t=", "],replacement:function(e,t,r){return e.trim()?r.emDelimiter+e+r.emDelimiter:", "}};ma.strong={filter:[", "],replacement:function(e,t,r){return e.trim()?r.strongDelimiter+e+r.strongDelimiter:", "}};ma.code={filter:function(e){var t=e.previousSibling||e.nextSibling,r=e.parentNode.nodeName===", "&&!t;return e.nodeName===", "&&!r},replacement:function(e){if(!e)return", ";e=e.replace(/\\r?\\n|\\r/g,", ");for(var t=/^`|^ .*?[^ ].* $|`$/.test(e)?", "\",n=e.match(/", "`filter` needs to be a string, array, or function", "<x-turndown id=\"turndown-root\">", "</x-turndown>", "turndown-root", "left", "right", "\\\\\\\\", "\\\\+ ", "\\\\$1 ", "/g,\"\\\\", "],[/^~~~/g,", "],[/\\[/g,", "],[/\\]/g,", "],[/^>/g,", "],[/_/g,", "],[/^(\\d+)\\. /g,", "]];function hF(e){if(!(this instanceof hF))return new hF(e);var t={rules:ma,headingStyle:", ",hr:", ",bulletListMarker:", ",codeBlockStyle:", ",fence:", ":\"\"},keepReplacement:function(r,n){return n.isBlock?", "+n.outerHTML+", ":n.outerHTML},defaultReplacement:function(r,n){return n.isBlock?", " or ", ".\");let t=e.port!=null?e.port:e.protocol===\"https:\"?443:80,r=e.origin!=null?e.origin:", ",n=e.path!=null?e.path:", ";return r[r.length-1]===\"/\"&&(r=r.slice(0,r.length-1)),n&&n[0]!==\"/\"&&(n=", "),new URL(", ")}if(!XF(e.origin||e.protocol))throw new go(\"Invalid URL protocol: the URL must start with ", ".toWellFormed():Dut.toUSVString(e)}function odt(e){return sdt?", ".isWellFormed():x0e(e)===", ":${i}", ");i.push(r[s])}else if(r[s]===null)i.push(\"\");else{if(typeof r[s]==\"object\")throw new Yi(", ");i.push(", ")}r=i}else if(typeof r==\"string\"){if(!k0e(r))throw new Yi(", ")}else r===null?r=\"\":r=", ":r+=", ",r+=", "].concat(Pe.ALPHANUM);Pe.URL_CHAR=Pe.STRICT_URL_CHAR.concat([", "]);for(let e=128;e<=255;e++)Pe.URL_CHAR.push(e);Pe.HEX=Pe.NUM.concat([", "]);Pe.STRICT_TOKEN=[", "data:", "failure", "text/plain;charset=US-ASCII", "${i}/${s}", "base64", ")/g,", "),s='", "+s,s+=", ":case", ":return", ";case", "}return e.subtype.endsWith(", ":e.subtype.endsWith(", ";var{types:Hf,inspect:vft}=require(", "),{markAsUncloneable:Cft}=require(", ",r=`${e.argument} could not be converted to${t}: ${e.types.join(", ");throw n.code=", ",n}}else if(e?.[Symbol.toStringTag]!==t.prototype[Symbol.toStringTag]){let n=new TypeError(", "} required, but${e?", "} ${e} found.`,header:r})};Fe.illegalConstructor=function(){throw Fe.errors.exception({header:", ",message:", "})};Fe.util.Type=function(e){switch(typeof e){case", ":return e===null?", "?s=0:s=Math.pow(-2,53)+1):r===", ":return`Symbol(${e.description})`;case", ":return vft(e);case", ":return`", "`;default:return`${e}`}};Fe.sequenceConverter=function(e){return(t,r,n,i)=>{if(Fe.util.Type(t)!==", "?i():t?.[Symbol.iterator]?.(),o=[],a=0;if(s===void 0||typeof s.next!=", ")throw Fe.errors.exception({header:n,message:`${i} (", "||i===", ")return s;if(i!==", ".`});let d=t[a],f=Object.hasOwn(o,", ";if(typeof e==", "]=function(e,t,r){return Fe.util.ConvertToInt(e,64,", ",void 0,t,r)};Fe.converters[", "]=function(e,t,r){return Fe.util.ConvertToInt(e,32,", "]=function(e,t,r,n){return Fe.util.ConvertToInt(e,16,", ",n,t,r)};Fe.converters.ArrayBuffer=function(e,t,r,n){if(Fe.util.Type(e)!==", "||!Hf.is<PERSON><PERSON><PERSON><PERSON>(e))throw Fe.errors.conversionFailed({prefix:t,argument:`${r} (", ")`,types:[", "]});if(n?.allowShared===!1&&Hf.isSharedArrayBuffer(e))throw Fe.errors.exception({header:", "});if(e.resizable||e.growable)throw Fe.errors.exception({header:", "});return e};Fe.converters.TypedArray=function(e,t,r,n,i){if(Fe.util.Type(e)!==", "});if(e.buffer.resizable||e.buffer.growable)throw Fe.errors.exception({header:", "});return e};Fe.converters.DataView=function(e,t,r,n){if(Fe.util.Type(e)!==", "]})};Fe.converters[", "]=Fe.sequenceConverter(Fe.converters.ByteString);Fe.converters[", "]=Fe.sequenceConverter(Fe.converters[", "]);Fe.converters[", ";var{Transform:_ft}=require(", "),gye=require(", "),{isUint8Array:Fft}=require(", "),{webidl:b1}=$o(),yye=[],pN;try{pN=require(", ");let e=[", ").toString(", "}function Qft(e){return e instanceof Error||e?.constructor?.name===", "||e?.constructor?.name===", "||e[0]===", "||e[e.length-1]===", "||e.includes(`\n`)||e.includes(", ")||e.includes(", "))===!1}function qft(e,t){let{headersList:r}=t,n=(r.get(", ",!0)??", ").split(", "),i=", ";if(n.length>0)for(let s=n.length;s!==0;s--){let o=n[s-1].trim();if(wft.has(o)){i=o;break}}i!==", "&&(e.referrerPolicy=i)}function Vft(){return", "}function Wft(){return", "}function Hft(){return", "}function $ft(e){let t=null;t=e.mode,e.headersList.set(", ",t,!0)}function Gft(e){let t=e.origin;if(!(t===", "||t===void 0)){if(e.responseTainting===", "||e.mode===", ")e.headersList.append(", ",t,!0);else if(e.method!==", "&&e.method!==", "){switch(e.referrerPolicy){case", ":t=null;break;case", ":e.origin&&t7(e.origin)&&!t7(C1(e))&&(t=null);break;case", ":hN(e,C1(e))||(t=null);break;default:}e.headersList.append(", "){let a=mye();if(!a||a.origin===", ")return", ":return i??e7(r,!0);case", ":return n;case", ":return s?i:", ":return s?n:i;case", ":{let a=C1(e);return hN(n,a)?n:v1(n)&&!v1(a)?", ":i}case", ":default:return o?", ":i}}function e7(e,t){return xy(e instanceof URL),e=new URL(e),e.protocol===", "||e.protocol===", ":(e.username=", ",e.password=", ",e.hash=", ",t&&(e.pathname=", ",e.search=", "),e)}function v1(e){if(!(e instanceof URL))return!1;if(e.href===", "||e.href===", ")return!0;return t(e.origin);function t(r){if(r==null||r===", ")return!1;let n=new URL(r);return!!(n.protocol===", "||n.protocol===", "||/^127(?:\\.[0-9]+){0,2}\\.[0-9]+$|^\\[(?:0*:)*?:?0*1\\]$/.test(n.hostname)||n.hostname===", "||n.hostname.includes(", ")||n.hostname.endsWith(", "))}}function Xft(e,t){if(pN===void 0)return!0;let r=_ye(t);if(r===", ");if(c[c.length-1]===", "&&(c[c.length-2]===", ":t}function ept(e){let t=e[0].algo;if(t[3]===", ")return t;for(let r=1;r<e.length;++r){let n=e[r];if(n.algo[3]===", "){t=", ";break}else{if(t[3]===", ")continue;n.algo[3]===", "&&(t=", "&&t[r]===", "||e[r]===", "}function opt(e){return e.controller.state===", "||e.controller.state===", ");return xy(typeof t==", ":d=l;break;case", ":d=u;break;case", "&&typeof e.tee==", "}function ppt(e){try{e.close(),e.byobRequest?.respond(0)}catch(t){if(!t.message.includes(", ")&&!t.message.includes(", ");t.push(i),r+=i.length}}function gpt(e){xy(", "in e);let t=e.protocol;return t===", "||t===", "}function t7(e){return typeof e==", "&&e[5]===", "&&e[0]===", "&&e[1]===", "&&e[2]===", "&&e[3]===", "&&e[4]===", "}function Sye(e){xy(", "}function mpt(e,t){let r=e;if(!r.startsWith(", "))return", ";let n={position:5};if(t&&_y(c=>c===", "||c===", ",r,n),r.char<PERSON>t(n.position)!==61)return", ";n.position++,t&&_y(c=>c===", ",r,n),r.char<PERSON>t(n.position)!==45)return", ":{rangeStartValue:s,rangeEndValue:a}}function Apt(e,t,r){let n=", ";return n+=fN(`${e}`),n+=", ",n+=fN(`${t}`),n+=", ",this.push.bind(this)),this._inflateStream.on(", ",()=>this.push(null)),this._inflateStream.on(", ",e);if(i===null)return", ";for(let s of i){let o=Rft(s);o===", "||o.essence===", "||(n=o,n.essence!==r?(t=null,n.parameters.has(", ")&&(t=n.parameters.get(", ")),r=n.essence):!n.parameters.has(", ")&&t!==null&&n.parameters.set(", ",t))}return n??", "}function vpt(e){let t=e,r={position:0},n=[],i=", ";for(;r.position<t.length;){if(i+=_y(s=>s!=='", " on ", ": parameter 2 is not of type ", "'),<PERSON>ye=Buffer.from(", "),Lpt=Buffer.from(", "&&t.essence===", ");let r=t.parameters.get(", ");if(r===void 0)return", ";let n=Buffer.from(`--${r}`,", ";s.position+=2;let a=Vpt(e,s);if(a===", ";f=e.subarray(s.position,g-4),s.position+=f.length,d===", "&&(f=Buffer.from(f.toString(),", "))}if(e[s.position]!==13||e[s.position+1]!==10)return", ";s.position+=2;let p;l!==null?(u??=", ",Opt(u)||(u=", "),p=new Npt([f],l,{type:u})):p=Dpt(Buffer.from(f)),mN(Pye(c)),mN(typeof p==", ";switch(t.position++,gE(a=>a===32||a===9,e,t),Rpt(o)){case", ":{if(r=n=null,!AN(e,Ppt,t)||(t.position+=17,r=Oye(e,t),r===null))return", "}break}case", ":{let a=gE(c=>c!==10&&c!==13,e,t);a=c7(a,!1,!0,c=>c===9||c===32),i=Lye(a);break}case", ").replace(/%22/g,'", " on path ", "status", " option ", " can be set only with ", " is not a valid HTTP method.`);let E=b.toUpperCase();if(_yt.has(E))throw new TypeError(`", ")),e.domain&&(ivt(e.domain),t.push(", ")),e.path&&(D_e(e.path),t.push(", ")),e.expires&&e.expires.toString()!==\"Invalid Date\"&&t.push(", "),e.sameSite&&t.push(", ",n+=i+\"Constructed: \"+e.constructed+", "),r+=ZP.util.encode64(e.body,t.maxline||64)+", ",r+=\"-----END \"+e.type+", ";for(var s=0,o=-1,i=0;i<t.length;++i,++s)if(s>65&&o!==-1){var a=t[o];a===\",\"?(++o,t=t.substr(0,o)+", "+t.substr(o)):t=t.substr(0,o)+", ";s+=\"Encryption: \"+i+", ",s+=\"Comment: \"+r+", "+v.digest().toHex()+", ",t[Hj]&&(t[Hj]=", ";for(let s=1;s<r;s++)i+=", "${s}", "${t}", ");if(!Number.isInteger(r))throw new TypeError(", ");if(r<1)throw new RangeError(", ")}return r===void 0?1/0:r}function tb(e){return e===1?\"1 item\":", ";throw typeof r!=\"function\"&&(n+=", "... ${tb(k)} not stringified", "}return y!==\"\"&&(E+=", "),g.pop(),", ",q=_)}if(N>o){let L=N-o;E+=", "${tb(L)} not stringified", ",q=_}return y!==\"\"&&q.length>1&&(E=", "... ${tb(Y)} not stringified", "}g.push(p);let w=\"\";y!==\"\"&&(b+=y,_=", ",w=\" \");let T=\"\";for(let N of A){let H=c(N,p[N],g,A,y,b);H!==void 0&&(E+=", ",T=_)}return y!==\"\"&&T.length>1&&(E=", "... ${tb(ce)} not stringified", "}return H+=", ",g.pop(),", "}let v=Object.keys(p),E=v.length;if(E===0)return\"{}\";if(s<g.length+1)return'\"[Object]\"';y+=A;let _=", ",T=_)}if(E>o){let H=E-o;w+=", "${tb(H)} not stringified", ",T=_}return T!==\"\"&&(w=", "... ${tb(N)} not stringified", "}return g.pop(),", ",v=\",\")}if(b>o){let _=b-o;A+=", "${tb(_)} not stringified", ":e[XTe]=", "+e[r-1]:r===2?", "}else return", ";else{let s=PRt(e,\".\")?\"property\":\"argument\";i=", "}return i+=", ",\"- Upgrade docs: https://github.com/winstonjs/winston/blob/master/UPGRADE-3.0.md\"].join(", "):console.warn(t[__]),r&&r();return}console._stdout?console._stdout.write(", ";return this.zippedArchive&&!this.tailable?", ",u=va.join(this.dirname,l);Gh.exists(u,d=>{if(!d)return c(null);l=", ",Gh.rename(u,va.join(this.dirname,l),c)})}.bind(this,o));w2t(i,()=>{Gh.rename(va.join(this.dirname,", "),va.join(this.dirname,", ",headers:s,auth:n&&n.username&&n.password?", "(no error message)", ",t&&t.stack||\"  No stack trace\"].join(", ").join(", ");dr(\"MAINVERSION\",", ");dr(\"MAINVERSIONLOOSE\",", ");dr(\"PRERELEASEIDENTIFIER\",", ");dr(\"PRERELEASEIDENTIFIERLOOSE\",", ");dr(\"PRERELEASE\",", ");dr(\"PRERELEASELOOSE\",", ");dr(\"BUILDIDENTIFIER\",", ");dr(\"BUILD\",", ");dr(\"FULLPLAIN\",", ");dr(\"FULL\",", ");dr(\"LOOSEPLAIN\",", ");dr(\"LOOSE\",", ");dr(\"GTLT\",\"((?:<|>)?=?)\");dr(\"XRANGEIDENTIFIERLOOSE\",", ");dr(\"XRANGEIDENTIFIER\",", ");dr(\"XRANGEPLAIN\",", ");dr(\"XRANGEPLAINLOOSE\",", ");dr(\"XRANGE\",", ");dr(\"XRANGELOOSE\",", ");dr(\"COERCEPLAIN\",", ");dr(\"COERCE\",", ");dr(\"COERCEFULL\",ot[at.COERCEPLAIN]+", ",!0);eg.tildeTrimReplace=\"$1~\";dr(\"TILDE\",", ");dr(\"TILDELOOSE\",", ");dr(\"LONECARET\",\"(?:\\\\^)\");dr(\"CARETTRIM\",", ",!0);eg.caretTrimReplace=\"$1^\";dr(\"CARET\",", ");dr(\"CARETLOOSE\",", ");dr(\"COMPARATORLOOSE\",", ");dr(\"COMPARATOR\",", ");dr(\"COMPARATORTRIM\",", ",!0);eg.comparatorTrimReplace=\"$1$2$3\";dr(\"HYPHENRANGE\",", ");dr(\"HYPHENRANGELOOSE\",", "${typeof t}", ");if(t.length>rMe)throw new TypeError(", ",this.prerelease.length&&(this.version+=", ")}return this.raw=this.format(),this.build.length&&(this.raw+=", ":\"\",a=t.includePrerelease&&r[6]?", ":\"\";return YNt(", ":tc(o)?c=", ":a?(Mi(\"replaceTilde pr\",a),c=", "):c=", ":tc(a)?s===\"0\"?l=", ":c?(Mi(\"replaceCaret pr\",c),s===\"0\"?o===\"0\"?l=", "):(Mi(\"no pr\"),s===\"0\"?o===\"0\"?l=", "):u?n=", ":d&&(n=", ":tc(s)?r=", ":o?r=", ",tc(l)?c=\"\":tc(u)?c=", ":tc(d)?c=", ":f?c=", ":e?c=", "):a.push(", ",this.quotes=", "\"`,this.escapes=\"\\\\\",this.escapedQuotes=", "+this.escapes.replace(/(.)/g,", ";if(!n&&this.escapedQuotes.includes(r)){let s=new RegExp(i+", ");return t.replace(s,", ")}if(n){let s={", "),t=!1,r=!1;continue}s=(s||", ")+a;continue}if(this.quotes.includes(a)){t=a,i===o-1&&(a===", "\"&&!this.ansiCQuotes||a===", "'&&!this.localeQuotes||(r=a)),s=s||", ",r!==!1&&(s=s.slice(0,-1));continue}if(t===!1&&a===", "&&(i=o),this.whitespace.includes(a)){s!==void 0&&(yield s),s=void 0;continue}s=(s||", ")+a}}};UR.split=function(e){return Array.from(new jX(e))};UR.quote=function(e){return e===", ":/[^\\w@%\\-+=:,./]/.test(e)?(", "\"+e.replace(/(", "'\"$1\"'", "args should be an array", "enabled", "#0000CC", "#0000FF", "#0033CC", "#0033FF", "#0066CC", "#0066FF", "#0099CC", "#0099FF", "#00CC00", "#00CC33", "#00CC66", "#00CC99", "#00CCCC", "#00CCFF", "#3300CC", "#3300FF", "#3333CC", "#3333FF", "#3366CC", "#3366FF", "#3399CC", "#3399FF", "#33CC00", "#33CC33", "#33CC66", "#33CC99", "#33CCCC", "#33CCFF", "#6600CC", "#6600FF", "#6633CC", "#6633FF", "#66CC00", "#66CC33", "#9900CC", "#9900FF", "#9933CC", "#9933FF", "#99CC00", "#99CC33", "#CC0000", "#CC0033", "#CC0066", "#CC0099", "#CC00CC", "#CC00FF", "#CC3300", "#CC3333", "#CC3366", "#CC3399", "#CC33CC", "#CC33FF", "#CC6600", "#CC6633", "#CC9900", "#CC9933", "#CCCC00", "#CCCC33", "#FF0000", "#FF0033", "#FF0066", "#FF0099", "#FF00CC", "#FF00FF", "#FF3300", "#FF3333", "#FF3366", "#FF3399", "#FF33CC", "#FF33FF", "#FF6600", "#FF6633", "#FF9900", "#FF9933", "#FFCC00", "#FFCC33", "renderer", "color: ", "color: inherit", "debug", "[UnexpectedJSONParseError]: ", "FORCE_COLOR", "color=16m", "color=full", "color=truecolor", "color=256", "TF_BUILD", "AGENT_NAME", "dumb", "win32", "GITHUB_ACTIONS", "GITEA_ACTIONS", "TRAVIS", "CIRCLECI", "APPVEYOR", "GITLAB_CI", "BUILDKITE", "DRONE", "codeship", "TEAMCITY_VERSION", "truecolor", "xterm-kitty", "TERM_PROGRAM", "iTerm.app", "Apple_Terminal", "COLORTERM", "process", "no-color", "no-colors", "color=false", "color=never", "colors", "color=true", "color=always", "util", "\\x1B[3", "8;5;", "  ${i};1m${t} \\x1B[0m", "\\x1B[0m", "@kwsites/file-exists", "checking %s", "[OK] path represents a file", "[OK] path represents a directory", "[FAIL] path represents something other than a file or directory", "ENOENT", "[FAIL] path is not accessible: %o", "[FATAL] %o", "pending", "resolved", "rejected", "\\u200C", "\\u200D", "\\xA0", "\\uFEFF", "\\u2028", "\\u2029", "comment", "multiLineComment", "singleLineComment", "multiLineCommentAsterisk", "punctuator", "alse", "sign", "decimalPointLeading", "zero", "decimalInteger", "nfinity", "identifierName", "identifierNameEscape", "identifier", "decimalPoint", "decimalExponent", "hexadecimal", "decimalFraction", "decimalExponentSign", "decimalExponentInteger", "hexadecimalInteger", "identifierNameStartEscape", "afterPropertyName", "beforePropertyV<PERSON>ue", "beforePropertyName", "beforeArrayValue", "afterArrayValue", "afterProperty<PERSON><PERSON>ue", "JSON5: invalid end of input at ${SA}:${of}", "JSON5: invalid character '${mqe(e)}' at ${SA}:${of}", "JSON5: invalid identifier character at ${SA}:${of}", "JSON5: '${mqe(e)}' in strings is not valid ECMAScript; consider escaping", "\\\\u2028", "\\\\u2029", "          ", "\\\\x00", "Converting circular structure to JSON5", "child_process", "electron-machine-id", "Symbol.", " is not an object!", "2.4.0", "Accessors not supported!", " is not a function!", "Can't call method on  ", "toStringTag", "keys", "Arguments", "Undefined", "<PERSON><PERSON>", "Object", "constructor,hasOwnProperty,isPrototypeOf,propertyIsEnumerable,toLocaleString,toString,valueOf", "prototype", "iterator", "@@iterator", "values", " Iterator", "entries", "Array", "__core-js_shared__", "onreadystatechange", "Can't convert object to primitive value", "Symbol(", "ia32", "PROCESSOR_ARCHITEW6432", "mixed", "native", "sha256", "darwin", "IOPlatformUUID", "/gi,", ").toLowerCase();case", ":return E.toString().split(", ")[1].replace(/\\r+|\\n+|\\s+/gi,", ":return E.toString().replace(/\\r+|\\n+|\\s+/gi,", ").toLowerCase();default:throw new Error(", "+T.stack));var q=c(N.toString());return _(E?q:a(q))})})}Object.defineProperty(n,", ",mixed:", "},v={darwin:", ",win32:b[o()]+", ",linux:", ",freebsd:", ")throw TypeError(g+", ").propertyIsEnumerable(0)?Object:function(o){return s(o)==", "?o.split(", "):Object(o)}},function(r,n,i){var s=i(10),o=i(1)(", ";var s=i(49),o=i(17),a=i(18),c={};i(5)(c,i(1)(", "),function(){return this}),r.exports=function(l,u,d){l.prototype=s(c,{next:o(1,d)}),a(l,u+", ")}},function(r,n,i){var s=i(1)(", "),l=function(){},u=", ",d=function(){var f,p=i(16)(", "),g=a.length,A=", ";for(p.style.display=", ",i(25).append<PERSON><PERSON><PERSON>(p),p.src=", ",f=p.contentWindow.document,f.open(),f.write(", "||he,<PERSON><PERSON>fail=typeof xe==", ";var s=i(61)(!0);i(27)(String,", "),l=[", ")},function(r,n){r.exports=require(", ")}])})});var C5e=x((H2r,SWt)=>{SWt.exports={name:", ",version:", ",description:", ",license:", ",author:", ",homepage:", ",main:", ",type:", ",bin:{systeminformation:", "},types:", ",scripts:{test:", ",testDeno:", "},files:[", "],keywords:[", "],repository:{type:", ",url:", "},funding:{type:", "},os:[", "],engines:{node:", "}}});var Qs=x(_t=>{", ";var MA=require(", "),pf=require(", "),IWt=require(", "),qte=require(", ").spawn,TWt=require(", ").exec,ew=require(", ").execSync,RWt=require(", "),nv=process.platform,Vte=nv===", "||nv===", ",w5e=nv===", ",y5=nv===", ",S5e=nv===", ",I5e=nv===", ",T5e=nv===", ",Nte=0,rv=", ",df=", ",BA=null,ff=null,Wte=process.env.WINDIR||", ",no,Zx=", ",FD=[],Hte=!1,m5=", ",E5e=", ",Lte=", ",_5e=", ",A5=", ",Qte=", ",ND={windowsHide:!0,maxBuffer:1024*2e4,encoding:", ",env:Object.assign({},process.env,{LANG:", "})},b5={maxBuffer:1024*2e4,encoding:", ",stdio:[", "]};function DWt(e){let t=parseInt(e,10);return isNaN(t)&&(t=0),t}function kWt(e){let t=!1,r=", ";for(let i of e)i>=", "&&i<=", ",t=t.toLowerCase(),n=n||!1,i=i||!1;let s=", ";return e.some(o=>{let a=o.toLowerCase().replace(/\\t/g,", ");if(n&&(a=a.trim()),a.startsWith(t)&&(!i||a.match(t+r)||a.match(t+", ",r=0;return e.split(", ").forEach(n=>{n>=", "&&n<=", "?r===1&&r++:(r===0&&r++,r===1&&(t+=n))}),t}function QWt(e,t){t=t||", ")>-1||s[1].toLowerCase().indexOf(", "+r).substr(-2)+", "+n).substr(-2)}}function OWt(e,t){let r={date:", ",time:", "};t=t||{};let n=(t.dateFormat||", ").toLowerCase(),i=t.pmDesignator||", ",s=e.split(", ");if(s[0]){if(s[0].indexOf(", ")>=0){let o=s[0].split(", ");o.length===3&&(o[0].length===4?r.date=o[0]+", "+o[1]).substr(-2)+", "+o[2]).substr(-2):o[2].length===2?(n.indexOf(", ")>-1||n.indexOf(", ")>-1,r.date=", "+o[2]+", "+o[0]).substr(-2)):(e.toLowerCase().indexOf(", ")>-1||e.toLowerCase().indexOf(", ")>-1)&&n.indexOf(", ")!==0?r.date=o[2]+", "+o[0]).substr(-2)+", "+o[1]).substr(-2):r.date=o[2]+", "+o[0]).substr(-2))}if(s[0].indexOf(", ");o.length===3&&(n.indexOf(", ")>-1?r.date=o[2]+", ");o.length===3&&(r.date=o[0]+", "+o[2]).substr(-2))}}if(s[1]){s.shift();let o=s.join(", "):(!/\\s/.test(e[c])&&r&&(s=c-1,i<s&&o.push({from:i,to:s,cap:e.substring(i,s)}),i=s+1,n++),r=e[c]===", "&&!rv&&(rv=Wte+", ",!pf.existsSync(rv)))try{let e=ew(", ",ND).toString().split(`\\r\n`);e&&e.length?rv=e[0]:rv=", "}catch{rv=", "}return rv}function WWt(e){return new Promise(t=>{process.nextTick(()=>{try{P5e(N5e()+", "+e).then(r=>{t(r,", ")})}catch(r){t(", ",r)}})})}function HWt(){return y5?`", "}function Pte(e){let t=", ",r,n=", "],{stdio:", ",windowsHide:!0,maxBuffer:1024*2e4,encoding:", "})}),no&&no.pid&&(Hte=!0,no.stdout.on(", ",function(e){Zx=Zx+e.toString(", "),e.indexOf(A5)>=0&&(Pte(Zx),Zx=", ")}),no.stderr.on(", ",function(){Pte(Zx+_5e)}),no.on(", ",function(){no&&no.kill()})))}function GWt(){try{no&&(no.stdin.write(", "+Lte+t+Qte+", "+MA.EOL+e+MA.EOL+", "+A5+<PERSON><PERSON>)}catch{r(", ")}})})}else{let t=", ";return new Promise(r=>{process.nextTick(()=>{try{let n=qte(m5,[", ",windowsHide:!0,maxBuffer:2048e4,encoding:", "})});if(n&&!n.pid&&n.on(", ",function(){r(t)}),n&&n.pid){n.stdout.on(", ",function(i){t=t+i.toString(", ")}),n.stderr.on(", ",function(){n.kill(),r(t)}),n.on(", ",function(){n.kill(),r(t)});try{n.stdin.write(E5e+e+MA.EOL),n.stdin.write(", "+MA.EOL),n.stdin.end()}catch{n.kill(),r(t)}}else r(t)}catch{r(t)}})})}}function KWt(e,t,r){let n=", ";return r=r||{},new Promise(i=>{process.nextTick(()=>{try{let s=qte(e,t,r);s&&!s.pid&&s.on(", ",function(){i(n)}),s&&s.pid?(s.stdout.on(", ",function(o){n+=o.toString()}),s.on(", ",function(){s.kill(),i(n)}),s.on(", ",function(){s.kill(),i(n)})):i(n)}catch{i(n)}})})}function YWt(){if(y5){if(!df)try{let r=ew(", ",ND).toString().split(`\\r\n`)[0].split(", ");df=r.length>1?r[1].replace(", ").trim():", "}catch{df=", "}return df}if(Vte||w5e||S5e||I5e||T5e){if(!df)try{let r=ew(", ",b5).toString().split(`\\r\n`)[0].split(", ");df=r.length>1?r[1].trim():", ",df||(df=", ")}catch{df=", "}return df}}function jWt(){if(BA!==null)return BA;if(BA=!1,y5)try{let e=ew(", ",ND).toString().split(`\\r\n`);e&&e.length?BA=e[0].indexOf(", ")>=0:BA=!1}catch{BA=!1}if(Vte||w5e||S5e||I5e||T5e)try{BA=ew(", ",b5).toString().split(`\\r\n`).length>0}catch{RWt.noop()}return BA}function zWt(e){let t=[", "];if(ff!==null)e=ff;else if(e===void 0)try{e=pf.readFileSync(", ",{encoding:", "}).toString().split(`\n`),ff=e}catch{return!1}let r=FA(e,", "),n=FA(e,", ");return r&&t.indexOf(r)>-1||n&&n.indexOf(", ")>-1}function JWt(){let e=[];try{e=pf.readFileSync(", "}).toString().split(`\n`)}catch{return!1}let t=FA(e,", ");return t&&t.indexOf(", ")>-1}function XWt(e,t,r){r||(r=t,t=ND);let n=", "+df+", ";TWt(n,t,function(i,s){r(i,s)})}function ZWt(){let e=pf.existsSync(", "),t=pf.existsSync(", "),r=pf.existsSync(", ";let r=[];return e.forEach(n=>{n.startsWith(t)&&r.push(n)}),r.length}function n8t(e,t){typeof t>", "&&(t=!1);let r=e||", ",i=F5e(r.length,2e3);for(let s=0;s<=i;s++)r[s]===void 0||r[s]===", "||r[s]===", "||r[s]===`\n`||r[s]===", "\"||r[s]===\"`\"||r[s]===", "'||r[s].length>1||t&&r[s]===", "||t&&r[s]===", "||t&&r[s]==", "||(n=n+r[s]);return n}function i8t(){let e=", ",t=!0,r=", ");t=t&&r===a,s=Math.random()*i*.9999999999,o=r.substr(0,s)+", "+r.substr(s,2e3),a=o.replace(/{/g,", "),t=t&&r===a,s=Math.random()*i*.9999999999,o=r.substr(0,s)+", "+r.substr(s,2e3),a=o.replace(/\\*/g,", "+r.substr(s,2e3),a=o.replace(/\\$/g,", ",f=u.toLowerCase();t=t&&f[0]===d&&f[0]&&!f[1]}}return!t}function s8t(e){return(", ":{type:", ",revision:", ",memory:256,manufacturer:", ",processor:", ",memory:512,manufacturer:", "}},r=[", "],n=[", "],i={", ",10:", ",11:", ",12:", ",13:", ",14:", ",15:", ",16:", ",17:", ",18:", ",19:", "},s=FA(e,", ",!0),o=FA(e,", ",!0),a=FA(e,", "+FA(e,", "}).toString().split(`\n`),ff=e}catch{return!1}let t=L5e(e);return t.type===", "||t.type===", ":t.type===", ";if(Vte)try{e=ew(", ",b5).toString()}catch{e=", "}return e}function f8t(e){let t=[", "],r=", ",n=e.indexOf(r),i=e.length;for(;e[n]!==", "&&n<i;)n++;let s=0,o=!1,a=!1,c=!1,l=[{tagStart:", ",tagEnd:", ",tagContent:", ",key:", ",data:null}],u=", ",d=e[n];for(;n<i;)u=d,n+1<i&&(d=e[n+1]),u===", "?(a=!1,d===", "?c=!0:l[s].tagStart?(l[s].tagContent=", ",l[s].data||(l[s].data=l[s].tagStart===", "?[]:{}),s++,l.push({tagStart:", ",key:null,data:null}),o=!0,a=!1):o||(o=!0)):u===", "?(l[s].tagStart===", "&&(o=!1,c=!0,l[s].tagStart=", ",l[s].tagEnd=", ",l[s].data=!0),l[s].tagStart===", ",l[s].data=!1),l[s].tagStart===", ",l[s].data=[]),a&&(a=!1),o&&(o=!1,a=!0,l[s].tagStart===", "&&(l[s].data=[]),l[s].tagStart===", "&&(l[s].data={})),c&&(c=!1,l[s].tagEnd&&t.indexOf(l[s].tagEnd.substr(1))>=0&&(l[s].tagEnd===", "||l[s].tagEnd===", "?(s>1&&l[s-2].tagStart===", "&&l[s-2].data.push(l[s-1].data),s>1&&l[s-2].tagStart===", "&&(l[s-2].data[l[s-1].key]=l[s-1].data),s--,l.pop(),l[s].tagContent=", ",l[s].tagStart=", "):(l[s].tagEnd===", "&&l[s].tagContent?l[s].key=l[s].tagContent:(l[s].tagEnd===", "&&l[s].tagContent&&(l[s].data=parseFloat(l[s].tagContent)||0),l[s].tagEnd===", "&&l[s].tagContent&&(l[s].data=parseInt(l[s].tagContent)||0),l[s].tagEnd===", "&&l[s].tagContent&&(l[s].data=l[s].tagContent||", "),l[s].tagEnd===", "&&(l[s].data=l[s].tagContent||!1),l[s].tagEnd===", "&&(l[s].data=l[s].tagContent||[]),s>0&&l[s-1].tagStart===", "&&l[s-1].data.push(l[s].data),s>0&&l[s-1].tagStart===", "&&(l[s-1].data[l[s].key]=l[s].data)),l[s].tagContent=", ")),l[s].tagEnd=", ")>=0){let i=t[n].split(", ");if(i[0]=i[0].trim(),i[0].startsWith('", ")||(i[0]=", "'+i[0]+'", "),i[1]=i[1].trim(),i[1].indexOf(", "')===-1&&i[1].endsWith(", ")){let s=i[1].substring(0,i[1].length-1);x5e(s)||(i[1]=`", ";`)}if(i[1].indexOf('", "virtual|hypervisor", "vmware|qemu|kvm|xen", "Nested Virtualization|/virtual/", "root/wmi", "ReleaseDate", "yyyy-MM-dd", "EFI v", "Detected boot environment", "%windir%\\\\Panther\\\\setupact.log", "');s.versions.java=g.length===3?g[1].trim():", "}a()}):a()}):Tt(", ",function(l,u){if(!l){let f=(u.toString().split(`\n`)[0]||", ").split('", "HKLM:\\\\SOFTWARE\\\\Microsoft\\\\NET Framework Setup\\\\NDP", "^(?!S)\\\\p{L}", ")>=0).length,y=g.filter(b=>b.indexOf(", "Governor: ", "Upgrade: Socket", "cpu MHz", "-----", "$mon", "$label", "$value", "HKEY_LOCAL_MACHINE\\\\HARDWARE\\\\DESCRIPTION\\\\System\\\\CentralProcessor\\\\0", "Pages active|Pages inactive", "Size:|Type|Speed|Manufacturer|Form Factor|Locator|Memory Device|Serial Number|Voltage|Part Number", "Bus Address: ", " | awk ", "HKLM:\\\\SYSTEM\\\\ControlSet001\\\\Control\\\\Class\\\\{4d36e968-e325-11ce-bfc1-08002be10318}\\\\*", "name", ").replace(/FSTYPE=/g,", "fsType", ").replace(/TYPE=/g,", ").replace(/SIZE=/g,", ").replace(/MOUNTPOINT=/g,", "mountpoint", ").replace(/UUID=/g,", "uuid", ").replace(/ROTA=/g,", "rota", ").replace(/RO=/g,", ").replace(/RM=/g,", ").replace(/TRAN=/g,", "tran", ").replace(/SERIAL=/g,", "serial", ").replace(/LABEL=/g,", ").replace(/MODEL=/g,", "model", ").replace(/OWNER=/g,", "owner", ").replace(/GROUP=/g,", "group", "; E={$_.GetRelated(", ").GetRelated(", "'+u+'", "s/ +/;/g", "s/^;//", "; diskutil info /dev/'+v+", "+g;let A=g.split(`\n`),y=we.getValue(A,", ",!0).trim(),b=we.getValue(A,", ",!0).trim(),v=we.getValue(A,", ",!0).trim();if(b){let E=0;if(b.indexOf(", ")>=0&&(E=parseInt(b.match(/\\(([^)]+)\\)/)[1].replace(/\\./g,", ").replace(/,/g,", "))),E||(E=parseInt(b)),E){let _=we.getValue(A,", ",!0).trim().toLowerCase();i.push({device:v,type:", ",name:we.<PERSON><PERSON><PERSON><PERSON>(A,", ",!0).trim(),vendor:t(we.getValue(A,", ",!0).trim(),serialNum:we.getValue(A,", ",!0).trim(),interfaceType:(", "+y).trim(),smartStatus:_===", ":_||", ",temperature:null,BSDName:v}),s=s+`printf ", "+v+'|\"; diskutil info /dev/'+v+\" | grep SMART;\"}}})}catch{we.noop()}try{let p=d.join(", ").replaceAll(", ",\"Model:\").split(", ");p.shift(),p.for<PERSON>ach(function(g){let A=g.split(", "\n`+b+'|", "\"),Cg&&(i=\"route -n get default 2>/dev/null | grep interface: | awk ", ";($A||GA||KA)&&(n=", ").replace(/", "netstat -natvln | head -n2; netstat -natvln | grep \"tcp4\\\\|tcp6\\\\|udp4\\\\|udp6\"", "ps -axo pid,command", "_Address", "UNKNOWN", "netstat -nao", "HERGESTELLT", "ESTABLISHED", "LISTEN", "SCHLIESSEN_WARTEN", "CLOSE_WAIT", "WARTEND", "TIME_WAIT", "SYN_GESENDET", "SYN_SENT", "LISTENING", "SYN_RECEIVED", "SYN_RECV", "FIN_WAIT_1", "FIN_WAIT1", "FIN_WAIT_2", "FIN_WAIT2", "ip route get 1", " via ", "route -n get default", "gateway", "netstat -rn | awk '/default/ {print $2}'", "netstat -r", "0.0.0.0 0.0.0.0", "NextHop", "linux", "android", "iw dev 2>/dev/null", "\nInterface ", "ifindex", "addr", "channel", "GENERAL.DEVICE", "GENERAL.TYPE", "GENERAL.HWADDR", "wifi", "nmcli -t -f general,wifi-properties,capabilities,ip4,ip6 device show ${e} 2> /dev/null", "GENERAL.CONNECTION", "GENERAL.VENDOR", "GENERAL.PRODUCT", "nmcli -t --show-secrets connection show ${e} 2>/dev/null", "802-11-wireless.seen-bssids", "connection.uuid", "connection.type", "connection.autoconnect", "802-11-wireless-security.key-mgmt", "wpa_cli -i ${e} status 2>&1", "freq", "ssid", "key_mgmt", "bssid", "ACTIVE:", "CHAN", "FREQ", "SECURITY", "WPA-FLAGS", "RSN-FLAGS", "SIGNAL", "SSID", "BSSID", "MODE", "export LC_ALL=C; iwlist ${e} scan 2>&1; unset LC_ALL", "        Cell ", "resource busy", "Address:", "mode", "frequency", "Quality", "signal level=", "essid", " WPA ", "WPA2 ", "WPA2", "group cipher", "pairwise cipher", "tkip", "TKIP/", "TKIP", "ccmp", "CCMP/", "CCMP", "proprietary", "PROP/", "PROP", "authentication suites", "802.1x", "802.1x/", "PSK/", "spairport_security_mode_wep", "spairport_security_mode_wpa2_personal", "spairport_security_mode_wpa2_enterprise", "WPA2 EAP", "pairport_security_mode_wpa3_transition", "WPA2/WPA3", "pairport_security_mode_wpa3", "WPA3", "export LC_ALL=C; iwconfig 2>/dev/null; unset LC_ALL", "no wireless", "system_profiler SPAirPortDataType -json 2>/dev/null", "netsh wlan show networks mode=Bssid", "SSID ", " BSSID", "intel", "Intel", "realtek", "Realtek", "qualcom", "Qualcom", "broadcom", "Broadcom", "cavium", "Cavium", "cisco", "Cisco", "marvel", "Marvel", "z<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "melanox", "Melanox", "d-link", "D-Link", "tp-link", "TP-Link", "asus", "<PERSON><PERSON>", "linksys", "Linksys", "802.11", "######", "SPNetworkDataType", "SPAirPortDataType", "  | {", "  | }", ").split(`\n`));let d=c.find(b=>b._name===", "),f=l[0].spairport_current_network_information,p=parseInt((", "+f.spairport_network_channel).split(", ")[0])||0,g=f.spairport_signal_noise||null,A=[],y=f.spairport_security_mode||", ";y===", "?A.push(", "):y===", "):y.<PERSON><PERSON><PERSON>(", ")?A.push(", ")&&A.push(", "),r.push({id:d._name||", ",iface:d.interface||", ",model:d.hardware||", ",ssid:f._name||", ",bssid:f.spairport_network_bssid||", ",channel:p,frequency:p?jD(p):null,type:f.spairport_network_phymode||", ")>=0?c[0].split(", ")[1].trim():", ",u=c[1].indexOf(", ")>=0?c[1].split(", ",d=c[2].indexOf(", ")>=0?c[2].split(", ",f=Qe.getValue(c,", ",!0),p=Qe.getValue(c,", ",!0)||Qe.getValue(c,", ",!0),g=Qe.getValue(c,", ",!0),A=lre(g),y=Qe.getValue(c,", ",!0)||null,b=Qe.getValue(c,", ",!0)||null,v=Qe.getValue(c,", ",!0)||null,E=Qe.getValue(c,", ",!0),l=Qe.getValue(a,", ",!0),u=Qe.getValue(a,", ",!0);r.push({id:", ",iface:c,model:u,vendor:", ",mac:l})}e&&e(r),t(r)});else if(cre){let n=", ",f=c[3].indexOf(", ")>=0?c[3].split(", "):[];f.shift();let p=f.join(", ";var G5=require(", "),e$t=require(", "),t$t=require(", "),zD=require(", ").exec,ure=require(", ").execSync,mt=Qs(),jA=process.platform,Lp=jA===", "||jA===", ",lv=jA===", ",dre=jA===", ",JD=jA===", ",XD=jA===", ",ZD=jA===", ",$5=jA===", "};function r$t(e){let t=e,r=e.replace(/ +/g,", ");return r.length===5&&(t=r[4]+", ".indexOf(r[1].toUpperCase())/3+1)).slice(-2)+", "+r[2]).slice(-2)+", "),n=r.length-1,i=n>0?parseInt(r[n-1]):0,s=r[n].split(", "+u.toISOString().substring(11,19);try{u=new Date(t.getTime()-l),d=u.toISOString().substring(0,10)+", "),new Promise(r=>{process.nextTick(()=>{if(typeof e!=", ")return t&&t([]),r([]);if(e){let n=", ").replace(/,+/g,", "),n===", "&&(n=", "),mt.isPrototypePolluted()&&n!==", ");let o=n.split(", "),a=[],c=[];if(Lp||JD||XD||ZD||lv){if((Lp||JD||XD||ZD)&&n===", ")try{let u=ure(", ",mt.execOptsLinux).toString().split(`\n`);o=[];for(let d of u){let f=d.split(", ")[0];f&&d.indexOf(", ")===-1&&o.push(f.trim())}n=o.join(", ")}catch{try{n=", ";let d=ure(", ",mt.execOptsLinux).toString().split(`\n`);for(let f of d){let p=f.split(", ");p.length===2&&(n+=(n!==", ")+p[1].trim())}o=n.split(", ")}catch{try{let f=ure(", ",mt.execOptsLinux).toString().split(`\n`).join(", ");if(n=", ",f){let p=f.split(", ");for(let g of p){let A=g.trim();A&&(n+=(n!==", ")+A)}o=n.split(", ")}}catch{n=", ",o=[]}}}lv&&n===", "&&(t&&t(a),r(a));let l=lv?[", "];n!==", "&&o.length>0?mt.execSafe(", ",l).then(u=>{if(u){let d=u.replace(/ +/g,", "+f.toLowerCase()+", ")!==-1||<PERSON><PERSON>to<PERSON>ase().indexOf(", "+f.toLowerCase())!==-1});let g=[];for(let A of p){let y=A.trim().split(", ")[2];y&&g.push(parseInt(y,10))}a.push({name:f,running:p.length>0,startmode:", ",pids:g,cpu:parseFloat(p.reduce(function(A,y){return A+parseFloat(y.trim().split(", ")[0])},0).toFixed(2)),mem:parseFloat(p.reduce(function(A,y){return A+parseFloat(y.trim().split(", ")[1])},0).toFixed(2))})}),Lp){let f='cat /proc/stat | grep ", "';for(let p in a)for(let g in a[p].pids)f+=", "+a[p].pids[g]+", "],mt.execSafe(", ",l).then(d=>{if(d){let f=d.replace(/ +/g,", ",cpu:0,mem:0})}),t&&t(a),r(a)}else o.forEach(function(f){a.push({name:f,running:!1,startmode:", ",cpu:0,mem:0})}),t&&t(a),r(a)})}):(t&&t(a),r(a))}if(dre)try{let l=", ";o[0]!==", "&&(l+=' -Filter ", ",o.forEach(u=>{l+=`Name=", "cpu ", "CreationDate", "yyyy-MM-dd HH:mm:ss", ",function(n,i){if(n)e&&e(r),t(r);else{let s=i.toString().split(`\n`);r=B4e(s,1),r.length===0?ek(", ",function(n,i){if(!n){let s=i.toString().split(`\n`);r=hre(s)}e&&e(r),t(r)}),p$t&&ek(", ",function(n,i){if(!n){let s=i.toString().split(`\n`);r=hre(s)}e&&e(r),t(r)}),c$t&&ek(", ";`;n+=\"Get-CimInstance Win32_LoggedOnUser | select antecedent,dependent | fl ; echo ", ";\",n+=`$process = (Get-CimInstance Win32_Process -Filter \"name = ", "),n[o]=n[o].replace(\": Yes,\",", "),n[o]=n[o].replace(\": Yes\",", "),n[o]=n[o].replace(\":No,\",", "),n[o]=n[o].replace(\": No,\",", "),n[o]=n[o].replace(\": No\",", ").concat(JSON.stringify(t),", ".concat(JSON.stringify(e),", "word", "}static getMarkdownParsingHelpText(){return", "}static formatTaskListViewResponse(t){return", ";return r.trim()&&(l+=", "),await this.performUpload(n)}catch(i){throw this.#e.error(", "${o}", ";break;case Be.invalid_literal:r=", ";break;case Be.unrecognized_keys:r=", ";break;case Be.invalid_union:r=\"Invalid input\";break;case Be.invalid_union_discriminator:r=", ";break;case Be.invalid_enum_value:r=", "${e.received}", "${e.validation.includes}", ",typeof e.validation.position==\"number\"&&(r=", ")):\"startsWith\"in e.validation?r=", "${e.validation.startsWith}", ":\"endsWith\"in e.validation?r=", "${e.validation.endsWith}", ":pn.assertNever(e.validation):e.validation!==\"regex\"?r=", ":r=\"Invalid\";break;case Be.too_small:e.type===\"array\"?r=", "exactly", "at least", "more than", ":e.type===\"string\"?r=", "over", ":e.type===\"number\"?r=", "exactly equal to ", "greater than or equal to ", "greater than ", ":e.type===\"date\"?r=", ":r=\"Invalid input\";break;case Be.too_big:e.type===\"array\"?r=", "at most", "less than", "under", "less than or equal to", ":e.type===\"bigint\"?r=", "smaller than or equal to", "smaller than", "^(\\\\p{Extended_Pictographic}|\\\\p{Emoji_Component})+$", "^${tce}$", "([01]\\\\d|2[0-3]):[0-5]\\\\d:[0-5]\\\\d", "${t}\\\\.\\\\d{${e.precision}}", "${t}(\\\\.\\\\d+)?", "^${rce(e)}$", "${tce}T${rce(e)}", "([+-]\\\\d{2}:?\\\\d{2})", "${t}(${r.join(\"|\")})", "^${t}$", "length", "emoji", "nanoid", "cuid", "cuid2", "ulid", "trim", "includes", "toLowerCase", "toUpperCase", "startsWith", "endsWith", "duration", "cidr", "base64url", "float", "finite", "bigint", "strip", "passthrough", "strict", "Internal ZodObject error: invalid unknownKeys value.", "unrecognized_keys", "dirty", "A discriminator value for key \\`${t}\\` could not be extracted from all schema options", "Discriminator property ${String(t)} has duplicate value ${String(a)}", "You must pass an array of schemas to z.tuple([ ... ])", "aborted", "preprocess", "refinement", "Async refinement encountered during synchronous parse operation. Use .parseAsync instead.", "transform", "Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.", "zod_brand", "custom", "ZodString", "ZodNumber", "ZodNaN", "ZodBigInt", "ZodBoolean", "ZodDate", "ZodSymbol", "ZodUndefined", "ZodNull", "ZodAny", "ZodUnknown", "<PERSON><PERSON><PERSON><PERSON>", "ZodVoid", "ZodArray", "ZodObject", "ZodUnion", "ZodDiscriminatedUnion", "ZodIntersection", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ZodRecord", "ZodMap", "ZodSet", "ZodFunction", "ZodLazy", "ZodLiteral", "ZodEnum", "ZodEffects", "ZodNativeEnum", "ZodOptional", "Zod<PERSON>ullable", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ZodCatch", "ZodPromise", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Zod<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Input not instance of ${e.name}", "2024-11-05", "2024-10-07", "ConnectionClosed", "RequestTimeout", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "InvalidRequest", "MethodNotFound", "InvalidParams", "InternalError", "notifications/cancelled", "initialize", "notifications/initialized", "ping", "notifications/progress", "resources/list", "resources/templates/list", "resources/read", "notifications/resources/list_changed", "resources/subscribe", "resources/unsubscribe", "notifications/resources/updated", "prompts/list", "prompts/get", "resource", "user", "assistant", "notifications/prompts/list_changed", "tools/list", "tools/call", "notifications/tools/list_changed", "info", "notice", "warning", "critical", "alert", "emergency", "logging/setLevel", "notifications/message", "sampling/createMessage", "thisServer", "allServers", "endTurn", "stopSequence", "maxTokens", "ref/resource", "ref/prompt", "completion/complete", "file://", "roots/list", "notifications/roots/list_changed", "MCP error ${t}: ${r}", "method", "Connection closed", "Uncaught error in notification handler: ${n}", "Method not found", "Failed to send an error response: ${i}", "Internal error", "Failed to send response: ${i}", "Received a progress notification for an unknown token: ${JSON.stringify(t)}", "Received a response for an unknown message ID: ${JSON.stringify(t)}", "result", "Not connected", "Failed to send cancellation: ${f}", "Request timed out", "Server does not support ${t} (required for ${r})", "Server sent invalid initialize result: ${r}", "Server's protocol version is not supported: ${r.protocolVersion}", "Server does not support logging (required for ${t})", "Server does not support prompts (required for ${t})", "Server does not support resources (required for ${t})", "Server does not support resource subscriptions (required for ${t})", "Server does not support tools (required for ${t})", "Client does not support roots list changed notifications (required for ${t})", "Client does not support sampling capability (required for ${t})", "Client does not support roots capability (required for ${t})", "APPDATA", "HOMEDRIVE", "HOMEPATH", "LOCALAPPDATA", "PATH", "PROCESSOR_ARCHITECTURE", "SYSTEMDRIVE", "SYSTEMROOT", "TEMP", "USERNAME", "USERPROFILE", "HOME", "LOGNAME", "SHELL", "TERM", "USER", "pipe", "inherit", "AbortError", "spawn", "close", "drain", "read-file", "save-file", "edit-file", "clarify", "onboarding-sub-agent", "launch-process", "kill-process", "read-process", "write-process", "list-processes", "wait-process", "open-browser", "str-replace-editor", "remember", "diagnostics", "setup-script", "read-terminal", "git-commit-retrieval", "spawn-sub-agent", "remoteToolHost", "localToolHost", "sidecarToolHost", "mcpHost", "ContentText", "ContentImage", "Unsafe", "Safe", "Check", "Unknown", "WebSearch", "GitHubApi", "Linear", "<PERSON><PERSON>", "Confluence", "Notion", "Supabase", "Glean", "Unknown<PERSON><PERSON>us", "Available", "UserConfigRequired", "No content provided", "JsonSchemaValidationError", "Invalid schema for tool ${t}: Schema must be an object", "Using compatible validator for schema ${t} with $schema: ${i}", "Schema for ${t} is valid according to JSON Schema specification", "Invalid schema for tool ${t}: ${a}", "Error validating schema for ${t}: ${n instanceof Error?n.message:String(n)}", "McpHost", "augment-mcp-client", "1.0.0", "Client is closing", "Client is undefined", "cmd.exe", "/bin/sh", "Connecting to MCP server: \"${t.name}\"", "Connected to MCP server: \"${t.name}\"", "${t.name} has ${a?.tools.length} tools}", "Failed to connect to MCP server \"${this._config.name}\"", "  Command: ${this._config.command}", "  Args: ${(this._config.args||[]).join(\" \")}", "  Error: ${i instanceof Error?i.message:String(i)}", "  Stderr: ${s}", "pkill -P ${r}", "MCP client is closing", "Cancelled by user.", "MCP tool call failed: ${f}", "Tool execution failed: ${f}", "Unexpected result format: content is not an array", "No result", "Timeout is too large: ${t.timeoutMs}, max is ${e.maxTimeoutMs}.", "${t}_${this._serverName}", "_${this._serverName}", "Validating MCP server tool ${t.name} and schema ${JSON.stringify(i)}", "${i instanceof Error?i.message:String(i)}", "git rev-parse --show-toplevel", "git config --get remote.origin.url", "git rev-parse --abbrev-ref HEAD", "git rev-parse --abbrev-ref origin/HEAD", "git config --get user.email", "Local Git Repository Information:", "- Repository Root: ${n}", "- Remote URL: ${s}", "- Current Branch: ${o}", "- Default Branch: ${l}", "- Git User Email: ${c}", "REPOSITORY SCOPE:", "RemoteToolHost", "Fetching remote tool definitions from backend", "Applying transformation for ${l.remoteToolId}", "Failed to list remote tools", "Using expired cache due to error fetching remote tools", "Tool not found: ${n}", "Tool ${n} not found.", "Failed to run remote tool ${n}: ${xI[u.status]}", "Failed to run remote tool ${n} - ${o instanceof Error?o.message:o}", "ExecutionUnknownStatus", "ExecutionSuccess", "NotFound", "InvalidInput", "ExecutionError", "NotAvailable", "AuthenticationError", "... additional lines truncated ...", "<...>", "A description of the information you need.", "information_request", "Failed to retrieve codebase information: ${o instanceof Error?o.message:String(o)}", "The paths of the files to remove.", "file_paths", "Cannot read file: ${c}", "Failed to delete file ${c}: ${A instanceof Error?A.message:String(A)}", "Failed to remove file(s): ${c instanceof Error?c.message:String(c)}", "File(s) removed: ${s.slice(0,o).join(\", \")}", "<response clipped><NOTE>To save on context only part of this file has been shown to you.</NOTE>", "Missing required parameter `path`", "Invalid parameter `path`. It must be a string.", "Invalid parameter `path`. It must not be empty.", "${String(o+r).padStart(6)}\t${s}", "Here's the result of running \\`cat -n\\` on ${t}:\n${e}\n", "Total lines in file: ${n}\n", "ToolFileUtils", "No filename found in path: ${e}", "Searching for files with name: ${i}", "**/${i}", "No files found with name: ${i}", "Error finding similar paths for ${e}: ${s instanceof Error?s.message:String(s)}", "Automatically correcting path from ${e} to ${i}", "Note: Path was automatically corrected from '${e}' to '${i}'.", "File not found: ${e}. Similar files found:\n${n}", "File not found: ${e}. Did you mean one of these?\n${n}", "File not found: ${e}. No similar files found", "File not found: ${e}", "Reading file: ${e}", "Successfully read file: ${e} (${r.contents.length} bytes)", "Successfully read corrected file: ${n.correctedPath} (${i.contents.length} bytes)", "SaveFileTool", "The path of the file to save.", "The content of the file.", "Whether to add a newline at the end of the file (default: true).", "file_content", "Should be exactly this string: '${this.instructions}'", "instructions_reminder", "Tool called with path: ${String(t.path)}", "Cannot resolve path: ${s}", "Resolved path: ${String(l)}", "File already exists and content is the same: {${l.absPath}}", "File already exists: ${l.absPath}", "File saved.  Created empty file {${s}}", "File saved.  Saved file {${s}}", "Failed to save file: ${t.path}: ${o}", "${String(c+s+1).padStart(6)}\t${a}", "space", "^ {1,${t.size}}", "Missing required parameter `str_replace_entries` for `str_replace` command.", "Invalid parameter `str_replace_entries` for `str_replace` command. It must be an array of objects.", "Empty required parameter `str_replace_entries` for `str_replace` command.", "Missing required parameter `old_str` for `str_replace` command.", "Invalid parameter `old_str` for `str_replace` command. It must be a string.", "Missing required parameter `new_str` for `str_replace` command.", "Invalid parameter `new_str` for `str_replace` command. It must be a string.", "Missing required parameter `insert_line_entries` for `insert` command.", "Invalid parameter `insert_line_entries` for `insert` command. It must be an array of objects.", "Empty required parameter `insert_line_entries` for `insert` command.", "Missing required parameter `insert_line` for `insert` command.", "Missing required parameter `new_str` for `insert` command.", "Invalid parameter `new_str` for `insert` command. It must be a string.", "Invalid parameter `insert_line` for `insert` command. It must be a non-negative integer.", "old_str", "new_str", "old_str_1", "new_str_1", "old_str_start_line_number", "old_str_end_line_number", "old_str_start_line_number_1", "old_str_end_line_number_1", "old_str_", "new_str_${i}", "old_str_${i}", "old_str_start_line_number_${i}", "old_str_end_line_number_${i}", "insert_line", "insert_line_", "insert_line_${i}", "Command not provided, deduced 'str_replace' from presence of str_replace_entries", "str_replace", "Command not provided, deduced 'insert' from presence of insert_line_entries", "insert", "Command not provided, deduced 'str_replace' from presence of old_str parameters", "Command not provided, deduced 'insert' from presence of insert_line parameters", "Command not provided, deduced 'view' from presence of view_range", "StrReplaceEditorToolDefinitionFlat", "Extracted ${r.length} str_replace entries from flat schema", "Validated ${n.length} str_replace entries from flat schema", "Extracted ${r.length} insert entries from flat schema", "Validated ${n.length} insert entries from flat schema", "The commands to run. Allowed options are: 'str_replace', 'insert'.", "Required parameter of `str_replace` command containing the string in `path` to replace.", "StrReplaceEditorToolDefinitionNested", "Validated ${r.length} str_replace entries", "Validated ${r.length} insert entries", "The string to insert. Can be an empty string.", "The string in `path` to replace.", "The string to replace `old_str` with. Can be an empty string to delete content.", "assert", "ExceedsMaxDiff", "ExceedsMaxDiffRatio", "FirstSymbolOfOldStrNotInOriginal", "LastSymbolOfOldStrNotInOriginal", "SymbolInOldNotInOriginalOrNew", "AmbiguousReplacement", "Type of path to view. Allowed options are: 'file', 'directory'.", "directory", "ViewTool", "Parameter 'path' must be a string", "Parameter 'type' must be a string", "Parameter 'type' must be either 'file' or 'directory'", "Parameter 'view_range' must be an array of two numbers", "Parameter 'view_range' must contain only numbers", "Parameter 'search_query_regex' must be a string", "Parameter 'case_sensitive' must be a boolean", "Parameter 'context_lines_before' must be an integer", "Parameter 'context_lines_before' must be a non-negative integer", "Parameter 'context_lines_after' must be an integer", "Parameter 'context_lines_after' must be a non-negative integer", "Tool called with inputs: ${JSON.stringify(t)}", "Input validation failed: ${i.message}", "Tool called with path: ${s.path} and view_range: ${JSON.stringify(s.viewRange)}", "Error in tool call: ${i instanceof Error?i.message:String(i)}", "Unknown error: ${String(i)}", "Path does not exist: ${t.path}", "Directory not found: ${t.path}", "Attempted path auto-correction, result: ${JSON.stringify(r)}", "File not found: ${t.path}", "Corrected path to: ${r.<PERSON>}", "${r.correctionNote}\n\n${i.text}", "Path is not a file or directory: ${t.path}", "Failed to read file even though we got pathInfo: ${t.path}", "Cannot read file: ${t.path}", "File ${t.path} has ${s} lines", "Note:\n${p.message}\n\n", "Invalid view_range: ${String(f)}", "Listing directory: ${t} (depth: 2, showHidden: false)", "Failed to list directory: ${t} - ${r.errorMessage}", "Directory ${t} has ${r.entries.length} entries", "Here's the files and directories up to 2 levels deep in ${t}, excluding hidden items:\n", "(empty directory)\n", "${t}/${i}", "${s}\n", "Performing regex search in ${t.path} with pattern: ${t.searchQueryRegex}", "Invalid view_range for regex search, searching entire file: ${String(f)}", "Invalid regex pattern: ${t.searchQueryRegex} - ${f instanceof Error?f.message:String(f)}", " within lines ${s+1}-${o+1}", "No matches found for regex pattern: ${t.searchQueryRegex}${f} in ${t.path}", "Regex search results for pattern: ${t.searchQueryRegex} in ${t.path}", "Search limited to lines ${s+1}-${o+1}", "Found ${l.length} matching lines:\n", "${_}${v}\t${b}", "\nTotal matches: ${l.length}", "Total lines in file: ${r.length}", "Invalid view range provided. Showing entire file (lines 1-${t}).", "Start line ${i} is less than 1. Adjusted to 1.\n", "Start line ${i} exceeds file length (${t}). Adjusted to 1.\n", "End line ${s} exceeds file length (${t}). Adjusted to ${t}. ", "End line ${s} is less than start line ${i}. Adjusted to ${t}. ", "View range expanded to meet minimum size of ${r} lines. ", "End line adjusted to last line of file (${t}).", "New range: [${i}, ${o}].", "StrReplaceEditorToolDefinitionFlatWithReminder", "ALWAYS BREAK DOWN EDITS INTO SMALLER CHUNKS OF AT MOST ${this.maxLines} LINES EACH.", "instruction_reminder", "StrReplaceEditorTool", "Initialized with params: lineNumberErrorTolerance=${r}, waitForAutoFormatSec=${n}", "Creating checkpoint for file: ${t.absPath}", "Adding checkpoint with conversationId: ${a}, requestId: ${o}", "Tool called with command: ${String(s)}, path: ${String(t.path)}", "Handling 'view' command for ${c} with range: ${JSON.stringify(t.view_range)}", "Unknown command: ${String(s)}", "Error in tool call: ${s instanceof Error?s.message:String(s)}", "Unknown error: ${String(s)}", "Preparing tool response for ${a} command on ${t}", "Tool call was cancelled.", "Content changed, creating checkpoint and waiting for auto-formatting", "File was auto-formatted after edit", "No content changes detected", "Results summary: all errors=${d}, all success=${f}, total results=${c.length}", "No message available", "Result for ${a} for entry with index [${y.index}]:\n${b}\n", "Tool call was cancelled. Reverting the change", "Returning error response for ${t}", "Returning success response for ${t}", "Cannot revert. Last edit path or sourceToolCallRequestId is undefined", "No verbatim match found for old_str. Trying fuzzy matching...", "oldStr", "newStr", "Fuzzy match was successful.", "Fuzzy match failed. Reason: ${c}", "No verbatim match found for old_str. Trying tab indentation fix...", "No replacement was performed, oldStr did not appear verbatim in ${t}.", "regionContent", "Multiple occurrences of oldStr \\`${n}\\` found. Please provide line numbers to disambiguate.", "No match found close to the provided line numbers (${ce}, ${k}).", "Internal error: Could not find the exact position of the match.", "Replacement successful.", "${i}\nEdited section after IDE auto-formatting was applied:\n${s}", "Handling str_replace command for ${t}", "Handling insert command for ${t}", "Augment-WebFetch/1.0", "removeStyleAndScriptTags", "The URL to fetch.", "User-Agent", "Failed to fetch URL: ${s}: ${o instanceof Error?o.message:String(o)}", "unspecified", "userGuidelines", "augmentGuidelines", "rules", "manuallyCreated", "selectedDirectory", "selectedFile", "classify_and_distill", "orientation", "memoriesRequestId", "exceptionThrown", "toolOutputIsError", "injectionNoCodeWrapper", "rememberToolModelNameMissing", "compressionStarted", "compressionTargetMissing", "compressionPromptMissing", "compressionNumRecentMemoriesToKeepMissing", "compressionRecentMemoriesSubpromptMissing", "compressionMemoriesQueueSize", "compressionPromptStats", "compressionRequestId", "compressedMemoriesStats", "compressionFailed", "setMemoriesStart", "setMemoriesUpperBoundSizeMissing", "nonEmptyLines", "noMemoriesFile", "updateBufferFailed", "noChangesMade", "injectionStarted", "injectionCurrentMemoriesStats", "injectionPromptMissing", "injectionPromptStats", "injectionRequestId", "injectionUpdatedMemoriesStats", "injectionFailed", "lastUserExchangeRequestId", "noMemoryData", "agenticTurnHasRememberToolCall", "emptyMemory", "removeUserExchangeMemoryFailed", "opened-agent-conversation", "revert-checkpoint", "agent-interruption", "sent-user-message", "remember-tool-call", "opened-memories-file", "initial-orientation", "classify-and-distill", "flush-memories", "vs-code-terminal-shell-integration-not-available", "vs-code-terminal-reading-approximate-output", "vs-code-terminal-timed-out-waiting-for-noop-command", "vs-code-terminal-failed-to-use-shell-integration", "vs-code-terminal-last-command-is-same-as-current", "vs-code-terminal-polling-determined-process-is-done", "vs-code-terminal-failed-to-read-output", "vs-code-terminal-buggy-output", "vs-code-terminal-buggy-execution-events", "vs-code-terminal-unsupported-vscode-shell", "vs-code-terminal-failed-to-find-git-bash", "vs-code-terminal-failed-to-find-powershell", "vs-code-terminal-no-supported-shells-found", "vs-code-terminal-settings-changed", "vs-code-terminal-wait-timeout", "vs-code-terminal-error-loading-settings", "vs-code-terminal-error-checking-for-shell-updates", "vs-code-terminal-error-cleaning-up-temp-dir", "vs-code-terminal-error-initializing-shells", "vs-code-terminal-error-checking-shell-capability", "vs-code-terminal-error-creating-zsh-environment", "vs-code-terminal-missed-start-event", "vs-code-terminal-read-stream-timeout-when-process-is-complete", "chat-history-truncated", "enhanced-prompt", "memories-move", "rules-imported", "onboarding", "automaticAfterIndexing", "concurrencyLevelMissing", "initialOrientationDisabled", "noRootFolderFound", "retryWithLowerConcurrencyLevel", "localizationPromptMissing", "detectLanguagesPromptMissing", "orientationCompressionPromptMissing", "orientationMaxLanguagesMissing", "orientationBuildTestQueryMissing", "orientationModelNameMissing", "topLanguagesNumFiles", "topLanguagesNumCodeFiles", "topLanguagesRequestId", "topLanguagesModelResponseStats", "topLanguagesNumDetectedLanguages", "topLanguagesNumFinalLanguages", "localizationStarted", "localizationEnded", "localizationPromptStats_0", "localizationPromptStats_1", "localizationPromptStats_2", "localizationPromptStats_3", "localizationPromptStats_4", "localizationPromptStats_5", "localizationRequestId_0", "localizationRequestId_1", "localizationRequestId_2", "localizationRequestId_3", "localizationRequestId_4", "localizationRequestId_5", "localizationResponseStats_0", "localizationResponseStats_1", "localizationResponseStats_2", "localizationResponseStats_3", "localizationResponseStats_4", "localizationResponseStats_5", "localizationParsingFailed_0", "localizationParsingFailed_1", "localizationParsingFailed_2", "localizationParsingFailed_3", "localizationParsingFailed_4", "localizationParsingFailed_5", "localizationNumLocations_0", "localizationNumLocations_1", "localizationNumLocations_2", "localizationNumLocations_3", "localizationNumLocations_4", "localizationNumLocations_5", "failedToListRootFolder", "agenticStarted", "agenticEnded", "agenticNumTurns_0", "agenticNumTurns_1", "agenticNumTurns_2", "agenticNumTurns_3", "agenticNumTurns_4", "agenticNumTurns_5", "agenticModelResponseStats_0", "agenticModelResponseStats_1", "agenticModelResponseStats_2", "agenticModelResponseStats_3", "agenticModelResponseStats_4", "agenticModelResponseStats_5", "agenticFailedToComplete_0", "agenticFailedToComplete_1", "agenticFailedToComplete_2", "agenticFailedToComplete_3", "agenticFailedToComplete_4", "agenticFailedToComplete_5", "agenticModelResponseStats", "compressionModelResponseStats", "compressionParsingFailed", "rememberStarted", "rememberEnded", "failedToReadGuidelines", "failedToWriteGuidelines", "noPendingUserMessage", "startSendSilentExchange", "sendSilentExchangeRequestId", "sendSilentExchangeResponseStats", "noRequestId", "conversationChanged", "explanationStats", "contentStats", "invalidResponse", "worthRemembering", "noLastUserExchangeRequestId", "remote-agent-setup", "ssh-interaction", "notification-bell", "diff-panel", "setup-page-opened", "github-api-failure", "remote-agent-created", "changes-applied", "created-pr", "unknown", "completed", "failed", "clicked", "remoteSessionStarted", "activated", "deactivated", "notified", "accepted", "The concise (1 sentence) memory to remember.", "memory", "Failed to save memory.", "- ${t}", "{currentMemories}", "{newMemory}", "Injection failed", "Model call failed", "Failed to save memories: upper bound size missing", "Failed to save memories: no memories file available", "No changes made to memories", "Memories saved successfully.", "Failed to update buffer", "{recentMemoriesSubprompt}", "- ${c}", "{recentMemories}", "{memories}", "{compressionTarget}", "Task ID is required and must be a non-empty string.", "State must be a string if provided.", "Invalid state: ${s}. Must be one of: NOT_STARTED, IN_PROGRESS, CANCELLED, COMPLETE.", "Name must be a string if provided.", "Description must be a string if provided.", "At least one property (state, name, description) must be provided to update.", "Name is required and must be a non-empty string.", "Description is required and must be a non-empty string.", "Parent task ID must be a non-empty string if provided.", "After task ID must be a non-empty string if provided.", "Invalid state: ${String(t)}. Must be one of: NOT_STARTED, IN_PROGRESS, CANCELLED, COMPLETE.", "ViewTaskListTool", "No root task found.", "Task with UUID ${i} not found.", "Error in ViewTaskListTool:", "Failed to view task list: ${i instanceof Error?i.message:String(i)}", "UpdateTasksTool", "The UUID of the task to update.", "NOT_STARTED", "IN_PROGRESS", "CANCELLED", "COMPLETE", "New task state. Use NOT_STARTED for [ ], IN_PROGRESS for [/], CANCELLED for [-], COMPLETE for [x].", "New task name.", "New task description.", "task_id", "New task name for single task update.", "New task description for single task update.", "Either task_id or tasks array is required.", "Error in UpdateTasksTool:", "Failed to update task(s): ${i instanceof Error?i.message:String(i)}", "Task with UUID ${t} not found.", "Task with UUID ${n} not found.", "Failed to retrieve updated task tree.", "Task with UUID ${r} not found.", "ReorganizeTaskListTool", "markdown", "No markdown provided.", "Task with UUID ${s} not found.", "Failed to parse markdown: ${d}\n\n${f}", "Failed to update task list: ${i instanceof Error?i.message:String(i)}", "AddTasksTool", "The name of the new task.", "The description of the new task.", "UUID of the parent task if this should be a subtask.", "UUID of the task after which this task should be inserted.", "Initial state of the task. Defaults to NOT_STARTED.", "The name of the new task for single task creation.", "The description of the new task for single task creation.", "UUID of the parent task if this should be a subtask for single task creation.", "UUID of the task after which this task should be inserted for single task creation.", "Initial state of the task for single task creation. Defaults to NOT_STARTED.", "Either single task properties (name, description) or tasks array is required.", "Error in AddTasksTool:", "Failed to add task(s): ${i instanceof Error?i.message:String(i)}", "No root task found and no parent task specified.", "Parent task with UUID ${r} not found.", "View a specific range of lines from untruncated content", "The reference ID of the truncated content (found in the truncation footer)", "The starting line number (1-based, inclusive)", "The ending line number (1-based, inclusive)", "reference_id", "start_line", "end_line", "Error: start_line must be a positive integer", "Error: end_line must be greater than or equal to start_line", "Error: Content with reference ID '${s}' not found", "Error: Failed to retrieve content for reference ID '${s}'", "${o+g}: ${p}", "}\n\n${d.join(", "Error: ${s instanceof Error?s.message:String(s)}", "Search for a term within untruncated content", "The term to search for within the content", "Number of context lines to include before and after matches (default: 2)", "search_term", "Error: Failed to search content for reference ID '${s}'", "${d>0?", "}\n\n${u}", "tool-output", "user-message", "conversation-history", "untruncated", "UntruncatedContentManager", "No matches found for \"${r}\"", "${b}${y+1}: ${s[y]}", "Failed to parse metadata:", "${e.ASSET_PREFIX}/content/${t}.txt", "${e.ASSET_PREFIX}/metadata/${t}.json", "The Mermaid diagram definition code to render", "Optional title for the diagram", "Mermaid Diagram", "diagram_definition", "RenderMermaidTool", "Tool called with diagram_definition: ${String(t.diagram_definition)} and title: ${String(t.title)}", "diagram_definition must be a string", "diagram_definition cannot be empty", "mermaid_diagram", "Error: ${i.message}", "GrepSearchTool", "Absolute path to the directory to search in.", "Optional glob pattern for files to include", "Optional glob pattern for files to exclude", "directory_absolute_path", "query", "Parameter 'directory_absolute_path' must be a string", "Parameter 'directory_absolute_path' must be an absolute path", "Parameter 'query' must be a string", "Parameter 'disable_ignore_files' must be a boolean", "Parameter 'files_include_glob_pattern' must be a string", "Parameter 'files_exclude_glob_pattern' must be a string", "Tool called with query: ${String(t.query)}", "--json", "--no-config", "--no-ignore", "--hidden", "!${s.files_exclude_glob_pattern}", "--before-context", "--after-context", "Running ripgrep with args: ${o.join(\" \")}", "No matches found.", "Failed to execute grep search: ${a}", "\n\n[Search timed out after ${o} seconds. Results may be incomplete.]", "rgProcess.stdout.on data: ${p}", "\n\n[Output truncated at ${y} characters limit.]", "ripgrep process exited with code ${p}: ${d}", "Failed to start ripgrep process: ${p.message}", "\n\n[Search was aborted. Results may be incomplete.]", "begin", "=== Search results start in file: ${c} ===\n", "=== Search results end in file: ${c} ===\n", "match", "...\n", "${l.toString().padStart(6)}\t${c.text.trimEnd()}\n", "Failed to parse ripgrep output line: ${o}", "events", "augment/clients/sidecar/events/restart-hosts", "augment/clients/sidecar/chat-mode", "ToolsModel", "Loaded saved chat mode: ${String(r)}", "Failed to load saved chat mode: ${String(t)}", "Saved chat mode: ${String(t)}", "Failed to save chat mode: ${String(r)}", "Cannot find the host for tool '${t}'.", "Tools Mode: ${this._chatMode} (${this.hosts.length} hosts)", " + ${a}", " - ${a}", "Host: ${t.getName()} (${r.length} tools: ${s.length} enabled, ${o.length} disabled})\n${s}\n${o}", "empty", "agent-get-edit-list-request", "agent-get-edit-list-response", "agent-get-edit-changes-by-request-id-request", "agent-get-edit-changes-by-request-id-response", "agent-set-current-conversation", "agent-migrate-conversation-id", "revert-to-timestamp", "chat-agent-edit-accept-all", "report-agent-session-event", "report-agent-request-event", "chat-review-agent-file", "get-agent-edit-contents-by-request-id", "get-agent-edit-contents-by-request-id-response", "check-has-ever-used-agent", "check-has-ever-used-agent-response", "set-has-ever-used-agent", "check-has-ever-used-remote-agent", "check-has-ever-used-remote-agent-response", "set-has-ever-used-remote-agent", "hasEverUsedAgent", "hasEverUsedRemoteAgent", "AgentWebviewMessageHandler", "get-hydrated-task-request", "get-hydrated-task-response", "set-current-root-task-uuid", "create-task-request", "create-task-response", "update-task-request", "update-task-response", "update-hydrated-task-request", "update-hydrated-task-response", "close-all-tool-processes", "get-tool-identifier-request", "get-tool-identifier-response", "ToolsWebviewMessageHandler", "Received closeAllToolProcesses message", "WebviewMessageBroker", "No webview message handler found for '${t.type}' in the sidecar broker.", "content exceeds maximum size of ${t}", "console", "vscode", "${t}${n}: (array) ${i.length} (array length) ${JSON.stringify(i).length} (char length)", "${t}  [${a}]: ${Zy(o,t+\"  \")}", "${t}  ${i.length-s} more items...", "${t}${n}: (object) ${Object.keys(i).length} (object size) ${JSON.stringify(i).length} (char length)", "${t}${n}: ${EIe(i)}", "${e.length} (string length)", "Value of ${e}.${t} is not Array", "Value of ${e}.${t} has unexpected type. Expected number, received ${n}", "Value of ${e}.${t} has unexpected type. Expected string, received ${n}", "Value of ${e}.${t} has unexpected type. Expected boolean, received ${i}", "Surrogate pair was split in field ${n}", "Lone surrogate found mid-string in field ${n}", "BackChatResult", "unknown_blob_names", "checkpoint_not_found", "BackWorkspaceFileChunk", "char_start", "char_end", "blob_name", "AugmentExtensionSidecar", "chat-stream", "Please configure Augment API URL", "Augment API URL is invalid:", "Augment API URL must start with 'http://' or 'https://'", "POST", "Content-Type", "application/json", "x-request-id", "x-request-session-id", "${c??this.sessionId}", "Bearer ${d}", "API request ${t} to ${f.toString()} failed: ${He(T,!0)}", "Request cancelled", "API request ${t} to ${f.toString()} failed: ${await v.clone().text()}", "API request ${t} to ${f.toString()} response ${v.status}: ${v.statusText}", "object size is ${i?Zy(i):0} ", "JSON parse failed for ${ie}: ${He(ce)}", "Augment", "verbose", "logged", "'${r.prefix}'", "YYYY-MM-DD HH:mm:ss.SSS", "${e.timestamp} [${e.level}] '${e.prefix}': ${e.message}", "${this.name} changed:\n${this.diff(this.value,t).map(r=>", "${this.name} changed from <unset> to ${this._formatValue(t)}", "${this._formatValue(t)} to ${this._formatValue(r)}", "memoriesParams", "${n.concat(o).join(\" > \")}: ${this._formatValue(c)} to ${this._formatValue(a)}", "<unset>", "Attempt to add undefined disposable to DisposableCollection", "visible-hover", "backend_edit_tool", "FeatureFlagManager", "feature flags", "FeatureFlagManager has been disposed", "augment", "AugmentConfigListener", "Feature flag override for ${i} is not a valid feature flag.", "Feature flag override for ${i} is does not match default type ${typeof fb[i]}.", "background", "global", "right-click", "hover-click", "editor-action-click", "keybinding", "keyboard", "active-editor-changed", "editor-selection-changed", "editor-visible-ranges-changed", "hovered-outside-suggestion", "document-changed", "next-edit-panel-item-focus-click", "next-edit-panel-item-click", "code-action", "gutter-click", "code-lens", "tutorial", "validation-expected", "validation-unexpected", "debug-session", "notebook-document", "unsupported-uri", "missing-path-name", "not-active-editor", "no-content-changes", "fresh-suggestions", "fresh", "stale", "Unimplemented", "Success", "NotActive", "Failed", "Conversion failure: ${t}. Response = ${r}", "AugmentExtension", "${r}", "${this.sessionId}", "x-api-version", "Bearer ${f}", "API request ${r} to ${g.toString()} failed: ${He(N,!0)}", "API request ${r} to ${g.toString()} failed", "API request ${r} to ${g.toString()} failed: ${await <PERSON><PERSON>clone().text()}", "API request ${r} to ${g.toString()} response ${E.status}: ${E.statusText}", "object size is ${Zy(s)} ", "content-length", "API request ${r} to ${g.toString()} failed to convert response to json: ${N.message}", "Completion item text is not a string: ${JSON.stringify(r)}", "Completion item skipped suffix is not a string: ${JSON.stringify(r)}", "Completion item suffix replacement text is not a string: ${JSON.stringify(r)}", "BackCompletionResult", "completions", "unknown_blob_names/unknown_memory_names", "BackCheckpointBlobsResult", "new_checkpoint_id", "completion", "checkpoint-blobs", "Checkpoint blobs API returned different checkpoint IDs for ${a}", "BackCodeEditResult", "edit", "chat", "BackChatInstructionStreamResult", "instruction-stream", "smart-paste-stream", "generate-commit-message-stream", "Calling /remote-agents/create with payload: ", "remote-agents/create", "remote-agents/chat", "remote-agents/delete", "remote-agents/interrupt", "remote-agents/pause", "remote-agents/resume", "remote-agents/list", "remote-agents/list-stream", "remote-agents/get-chat-history", "remote-agents/agent-history-stream", "remote-agents/add-ssh-key", "remote-agents/logs", "github/list-repos", "github/list-branches", "github/get-repo", "Failed to get GitHub repo:", "github/is-user-configured", "subscription-info", "BackNextEditLocationResult", "candidate_locations", "BackNextEditResult", "critical_errors", "BackLineRange", "BackLocation", "BackScored", "score", "next_edit_loc", "BackAutofixCheckResponse", "contains_failure", "is_code_related", "BackAutofixPlanResponse", "BackNextEditGenerationResult", "suggestion_id", "existing_code", "suggested_code", "truncation_char", "change_description", "BackCharRange", "editing_score", "localization_score", "editing_score_threshold", "next-edit-stream", "BackMemorizeResult", "mem_object_name", "memorize", "batch-upload", "BackFindMissingResult", "unknown_memory_names", "nonindexed_blob_names", "find-missing", "resolve-completions", "vscode-extension", "resolve-edit", "resolve-smart-paste", "resolve-instruction", "resolve-next-edit", "report-feature-vector", "record-session-events", "record-request-events", "record-preference-sample", "BackModelInfo", "completion_timeout_ms", "suggested_prefix_char_count", "suggested_suffix_char_count", "internal_name", "BackLanguageInfo", "vscodeName", "extensions", "BackGetModelsResult", "default_model", "models", "Parsing of \"memories_params\" failed.", "Parsing of \"elo_model_configuration\" failed.", "Parsing of \"model_registry\" failed.", "languages", "_tier", "get-models", "completion-feedback", "chat-feedback", "next-edit-feedback", "authorization_code", "token", "record-user-events", "client-metrics", "search-external-sources", "get-implicit-external-sources", "agents/codebase-retrieval", "agents/edit-file", "agents/list-remote-tools", "agents/check-tool-safety", "agents/run-remote-tool", "agents/revoke-tool-access", "reportError should only be used via APIServerImplWithErrorReporting", "/client-completion-timelines", "check_command", "contain_errors", "create_fix_plan", "apply_file_fix", "/autofix/check", "/autofix/plan", "/save-chat", "body_length", "${DT(n,this._logger).length}", "start_time", "end_time", "${Date.now()}", "object_size_breakdown", "${Zy(n)}", "${r} call failed with APIStatus ${ze[u]}", "converting ${r} response failed", "API error ${r} to ${i}: ${d}", "report-error", "Dropping error report \"${r}\" due to error: ${He(c)}", "https://auth.augmentcode.com", "Config", "augment-vscode-extension", "enableAutomaticCompletions", "completions.enableAutomaticCompletions", "disableCompletionsByLanguage", "completions.disableCompletionsByLanguage", "enableBackgroundSuggestions", "nextEdit.enableBackgroundSuggestions", "enableGlobalBackgroundSuggestions", "nextEdit.enableGlobalBackgroundSuggestions", "highlightSuggestionsInTheEditor", "nextEdit.highlightSuggestionsInTheEditor", "Failed to parse settings: \n${i.join(", "settings parsed successfully after cleaning", "settings parsed successfully", "${workspaceFolder}", "Variable ${t} cannot be expanded because there are ${r} workspace folders open.", "${r}\\\\s*:\\\\s*(true|false)", "${r}\\\\s*:\\\\s*[\"']?([^\"'\n]*)[\"']?", "\"${n}\"", "(${r}\\\\s*:\\\\s*)([^\\\\n]*)", "$1${s}", "---\n${c}---\n", "${o.endsWith(", ")?o:o+", "}${r}: ${s}\n", "---\n${l}---\n", "---\n${r}: ${s}\n---\n\n${t}", "alwaysApply", ".augment", ".augment-guidelines", "chat-set-user-guidelines", "chat-clear-user-guidelines", "chat-set-workspace-guidelines", "chat-clear-workspace-guidelines", "augment.chat", "fs/promises", "/\\\\/g", "splitPath: ${e} must be a relative path", "Too-deep or malformed path name ${e}", ".git", "Directory", "CommandUtils", "stderr:", "exec error [${r.code}] [${r.signal}] [${r.message}] [${r.stackTrace}]", "git --version", "diff", "--name-status", "ls-files", "--others", "--exclude-standard", "--oneline", "\"${t.object}\"", "symbolic-ref", "--no-merges", "--format=\"${t.format}\"", "--not \"${t.not}\"", "File", "Other", "${e}.${JBe.randomBytes(8).toString(\"hex\")}.tmp", "Error finding VCS:", "Error executing git command:", ".mdc", "Rules<PERSON><PERSON>der", "Loaded guidelines file from ${a}", "Error reading guidelines file ${a}: ${String(c)}", "Loaded ${n.length} guidelines files", "Error loading guidelines files: ${String(i)}", "No workspace root found", "Loaded ${i.length} rules from ${o}", "Rules folder not found at ${o}", "Error loading rules: ${String(s)}", "Error reading rule file ${l}: ${String(u)}", "vscode-local:${r}", "vscode.openWith", "vscode.open", "imported", "RulesWatcher", "**/${Xo}/${Ja}/**/*.md", "**/${Xo}/${Ja}/**", "**/${Ys}", "Error setting up file system watcher: ${String(r)}", "Rule already exists ${u}, not overwriting", "Not in HMR mode", "${e}/${t}", "Failed to load ${t} from ${e}: ${n.statusText}", "Empty response when loading ${t} from ${e}: ${n.statusText}", "/.augment-hmr-env", "Failed to load HMR url from '${r}'", "<head>", "<head><base href=\"${t}\" /><meta http-equiv=\"Content-Security-Policy\" content=\"${r}\" />", "production", "'unsafe-inline'", "default-src 'none';", "font-src", "style-src", "script-src", "img-src", "media-src", "worker-src", "connect-src", "${i} ${o.join(\" \")};", "https://fonts.gstatic.com", "https://fonts.googleapis.com", "https://www.google.com", "https://*.gstatic.com", "https://s2.googleusercontent.com", "blob:", "https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/0.52.2/min/", "fonts", "styles", "scripts", "workers", "media", "images", "connects", "https://cdn.jsdelivr.net", "https://unpkg.com", "'unsafe-eval'", "vscode-resource:", "https://*.vscode-cdn.net", "PanelWebviewBase", "Loading '%s' from dev server on '%s'", "Failed to load '%s' from dev server: ${String(n)} ", "../../common/webviews/index.html", "></code>", ">${String(n)}</code>", "common-webviews", "Failed to load ${n}: ${String(a)}", "Must provide either a response or an error", "Must provide either a response or an error, not both", "async-wrapper", "sidecar", "${n.requestId}-0", "${n.requestId}-${c}", "memories.augment", "AugmentMemoriesEditorProvider", "memories.html", "chat-loaded", "chat-initialize", "open-confirmation-modal", "Failed to load memories.html webview: ${String(a)}", "Failed to load memories editor: ${String(a)}", "Sending content to memories webview", "load-file", "Successfully saved content to ${t.uri.fsPath}", "Error saving content to ${t.uri.fsPath}:", "memories-loaded", "Memories webview is ready, sending initial content", "open-guidelines", "update-workspace-guidelines", "Guidelines file doesn't exist yet, will create it: ${l}", "Successfully updated workspace guidelines at ${l}", "Failed to update workspace guidelines: ${String(u)}", "No workspace folder found", "open-file", "update-user-guidelines", "Successfully updated user guidelines", "Moved content \"${a.data.slice(0,20)}\" to User guidelines", "View in Settings", "vscode-augment.showSettingsPanel", "guidelines", "Failed to update user guidelines: ${String(d)}", "open-settings-page", "get-rules-list-request", "get-rules-list-response", "Loaded ${c.length} rules:", "Failed to get rules list: ${String(c)}", "update-rule-file", "Rule file doesn't exist yet, will create it: ${l}", "Successfully updated rule file at ${l}", "Moved content \"${a.data.content.slice(0,20)}\" to rule file ${a.data.rulePath}", "View Rule File", "Failed to update rule file: ${String(u)}", "Rules changed, refreshing rules list in memories webview", "Refreshed ${r.length} rules for memories webview:", "Failed to refresh rules list: ${String(r)}", "confirmation-modal-response", "Error showing confirmation modal: ${String(r)}", "rules.augment", "AugmentRulesEditorProvider", "rules.html", "Failed to load rules.html webview: ${String(o)}", "Failed to load rules editor: ${String(o)}", "Sending content to rules webview", "rules-loaded", "Rules webview is ready, sending initial content", "authenticated", "incomplete", "augment.sessions", "setContext", "vscode-augment.isLoggedIn", "vscode-augment.useOAuth", "augment.oauth-state", ".augmentcode.com", "/auth/result", "OAuthFlow", "Cancelled by user", "Cancelled due to new sign in", "Creating new session...", "Created session ${r.tenantURL}", "signed-in", "Sign in failed. ${He(r)}", "Signing in...", "No OAuth URL defined.", "Timed out", "User cancelled", "S256", "login", "/authorize?${n.toString()}", "Opening URL: ${i.toString()}", "Copy URL", "Failed to open browser. Copy the URL to sign in manually.", "URL copied to clipboard", "No session", "state", "No state", "No OAuth state found", "Unknown state", "(${s})", "error_description", "OAuth request failed: ${c.join(\" \")}", "No code", "tenant_url", "OAuth request failed: invalid OAuth tenant URL", "No tenant URL", "Failed to get and save access token: ${He(c)}", "If you have a firewall, please add \"${a}\" to your allowlist.", "Failed to process auth request:", "augment-global-state", "${r}.json", "quiet-background-file", "utf-8", "Augment-Memories", "Agent-Memories", "Migrated memories from Agent-Memories to Augment-Memories", "Error during memories migration:", "Cannot determine current working directory", "git rev-parse --is-inside-work-tree", "git config user.name", "git config user.email", " menu) (\n( For repository-level Memories: consider using ", "             ,oXMWO.            cMMMk\n     OMMM:             ", "WMMX.\n     OMMM:           .OMMNo", "xWMMNOdl::;::ldONMMNx", " _ \\ / _ \\| ", ").length;if(!e.every(r=>r.split(", ");if(!await Co(i))return Ee(", ");try{let o=(await hb(i)).map(([a,c])=>a);return Et(o.join(", "))}catch{return this.logger.error(", "),<PERSON><PERSON>(", ");if(!Vr(i))return Ee(", ");let s=await Dn(i);return s===void 0?(this.logger.error(", ")):Et(s)}default:return Ee(", ":\"\";s=i.trim()+c+LJ+", ")}catch(c){throw this.trace.setFlag(nr.failedToWriteGuidelines),new Error(", ")}catch(s){throw this.trace.setFlag(nr.failedToWriteGuidelines),new Error(", ");let v=[];for(let E of this.queries){this.logger.debug(", ")),w=await this.doAgenticTurn(_,c,b);v.push(w),this.logger.debug(", "${y}", "${E.name}", ")}return v})),u=await Promise.all(l);this.trace.setFlag(nr.agenticEnded);let d=u.flat().join(", " tool immediately.\"}}]}this.logger.debug(", "),y===\"complete\")return this.logger.debug(", "),this.progressTracker.inc(d*(u-f-1)),this.trace.setStringStats(", ");let f=BFt(t,u);if(f.trim().length===0)throw new Error(", ",p);let g=this.apiServer.createRequestId();this.trace.setRequestId(", ",g);let A=await this.simpleLlmCall(p,g);this.trace.setStringStats(", ",A);let y=A.match(/<locations>(.*?)<\\/locations>/);if(!y)throw this.trace.setFlag(", "),new Error(", "${c}", ":n;o.push(", "\\u2514\\u2500\\u2500 ", "\\u251C\\u2500\\u2500 ", ",VLt=e=>", ",WLt=e=>", ",HLt=e=>", "weight", ");{X(\"ChatModel\").debug(", ".\\n- When creating, deleting, viewing or editing files, first try prepending ", " to the path.\\n- When running shell commands, do not prepend ", " to the path.\\n\";function yQt(e,t){return e.agentMemories?e.agentMemories+=", ",l)}}catch(o){return console.error(", ",o),[]}}catch(i){console.error(", ",c)}}}catch(i){console.error(", ";for(;c.find(f=>f.name===d&&f.location===t);)u++,d=", ",n),!1}}async renameSetupScript(t,r,n){try{if(!this.isValidScriptName(r))throw new Error(", "}function _Ne(e,t){if(t>0){let r=t===1?\"\":\"s\";e.push(", ";return Et(", ",a+=zs.repeat(\"-\",t.indent+u+3+l.pos)+", ",s?1+c:c):i===PX&&s&&(e.result+=", ");break}for(n?wb(d)?(l=!0,e.result+=zs.repeat(", ",s?1+c:c)):l?(l=!1,e.result+=zs.repeat(", ",c+1)):c===0?s&&(e.result+=\" \"):e.result+=zs.repeat(", ",c):e.result+=zs.repeat(", ",n),i===-1?(o=e.slice(n),n=a):(o=e.slice(n,i+1),n=i+1),o.length&&o!==", "&&(s+=r),s+=o;return s}function OX(e,t){return", ",i=n&&(e[e.length-2]===", "||e===", "),s=i?\"+\":n?\"\":\"-\";return r+s+", "}function LNe(e){return e[e.length-1]===", "?e.slice(0,-1):e}function HUt(e,t){for(var r=/(\\n+)([^\\n]*)/g,n=function(){var l=e.indexOf(", ");return l=l!==-1?l:e.length,r.lastIndex=l,QNe(e.slice(0,l),t)}(),i=e[0]===", "||e[0]===\" \",s,o;o=r.exec(e);){var a=o[1],c=o[2];s=c[0]===\" \",n+=a+(!i&&!s&&c!==\"\"?", "+e.slice(i,s),i=s+1),o=a;return c+=", ",e.length-i>t&&o>i?c+=e.slice(i,o)+", "],e3t=[...KX,", "],t3t=[...KX,", "],r3t=[", "$&'()*,:;<=>?@[\\\\\\]^`{|}])/g,", ")).join(", ")}var n3t=`\nauto_approval:\n  git:\n    - type: prefix\n      args: [", "]\n    - type: prefix\n      args: [", "]\n    - type: exact\n      args: [", "]\n\n  kubectl:\n    - type: prefix\n      args: [", "]\n\n  bazel:\n    - type: prefix\n      args: [", "]\n\n  docker:\n    - type: prefix\n      args: [", "]\n\n  npm:\n    - type: prefix\n      args: [", "]\n\n  terraform:\n    - type: prefix\n      args: [", "]\n\n  gradle:\n    - type: prefix\n      args: [", "]\n\n  helm:\n    - type: prefix\n      args: [", "]\n\n  aws:\n    - type: prefix\n      args: [", "]\n\n  gcloud:\n    - type: prefix\n      args: [", "]\n\n  postgres:\n    - type: prefix\n      args: [", "]\n\n  maven:\n    - type: prefix\n      args: [", "]\n\n  redis-cli:\n    - type: prefix\n      args: [", "]\n\n  yarn:\n    - type: prefix\n      args: [", "]\n\n  az:\n    - type: prefix\n      args: [", "]\n\n  vault:\n    - type: prefix\n      args: [", "]\n\n  podman:\n    - type: prefix\n      args: [", "]\n\n  deno:\n    - type: prefix\n      args: [", "]\n\n  rustup:\n    - type: prefix\n      args: [", "]\n\n  cargo:\n    - type: prefix\n      args: [", "]\n\n  pip:\n    - type: prefix\n      args: [", "]\n  netstat:\n    type: any\n  ss:\n    type: any\n  ip:\n    type: prefix\n    args: [", ")r=jO(i3t);else if(t===", "&&n!==", "){if(l.length<=o.length&&l.every((u,d)=>o[d]===u))return!0}else if(c.type===", "){if(l.length===o.length&&l.every((u,d)=>o[d]===u))return!0}else{if(c.type===", ")return!0;if(c.type===", "&&!l.some(u=>o.includes(u)))return!0}}return!1}var ZO=require(", "],shell:this._shellInfo.path??this._shellInfo.name,signal:n,detached:process.platform===", "||process.platform===", "});o.stdout&&o.stdout.on(", ",c=>{this._processes.get(i).outputChunks.push(c.toString())}),o.stderr&&o.stderr.on(", ",c=>{this._processes.get(i).outputChunks.push(c.toString())}),o.on(", ",returnCode:null});return}if(s.exitCode!==null){i({output:s.outputChunks.join(", "),returnCode:null})};n.addEventListener(", ",a)})}closeAllProcesses(){for(let t of this._processes.values())t.killed||t.process.kill(", "),Sb=V(require(", ")),vPe=V(mPe()),zt=V(require(", "));function o3t(e,t){return e.name===t.name&&e.path===t.path&&e.args?.join(", ")===t.args?.join(", ")&&Object.keys(e.env??{}).join(", ")===Object.keys(t.env??{}).join(", ")&&Object.values(e.env??{}).join(", ")===Object.values(t.env??{}).join(", ").get(", ")&&!e._showedShellIntegrationMessage&&(zt.window.showWarningMessage(", "Terminal > Integrated > Shell Integration: Enabled", ").then(u=>{u===", "&&zt.commands.executeCommand(", ",u=>{this._terminalSettings=u.value,this._logger.verbose(", "),cn().reportEvent({eventName:un.vsCodeTerminalSettingsChanged,conversationId:", "}),this._checkAndUpdateShell()})),this._globalState.load(", ").then(u=>{this._terminalSettings=u??{supportedShells:[]},this._logger.verbose(", ")?(this._logger.verbose(", ";if(c===e._longRunningTerminal&&!a){let d=", ";(!_a(", ")||!this._isTerminalBasicallySupported())&&(d=this.shellName===", "),g(void 0))}finally{p=!1}})())},100)}),1e3).catch(()=>{this._logger.debug(", "),cn().reportEvent({eventName:un.vsCodeTerminalTimedOutWaitingForNoopCommand,conversationId:", "})}).finally(()=>{f&&(clearInterval(f),f=null)})}let u={terminal:c,command:r,lastCommand:l,output:", ",()=>{this.kill(o)}),o}async _createTerminal(r,n){let i=this.shellName===", "||this.shellName===", "?{PAGER:", ",LESS:", ",GIT_PAGER:", "}:this.shellName===", "?{GIT_PAGER:", "),dark:zt.Uri.joinPath(this._extensionRoot,", "}`),o.lastCommand===o.command){this._logger.debug(", "),cn().reportEvent({eventName:un.vsCodeTerminalLastCommandIsSameAsCurrent,conversationId:", "}),s({output:", ",returnCode:null})};i.addEventListener(", "}`),cn().reportEvent({eventName:un.vsCodeTerminalFailedToReadOutput,conversationId:", "});let i=await zt.env.clipboard.readText();await zt.env.clipboard.writeText(", "),await zt.commands.executeCommand(", ");let n=await zt.env.clipboard.readText();await zt.commands.executeCommand(", ",a=o.replace(s,", "&&a.shellInfo.friendlyName!==", ")return 1;if(a.shellInfo.friendlyName===", "&&o.shellInfo.friendlyName!==", ":this._isBasicallySupported(o.capability.capability)?", "),await Oa(5e3-i));for(let s of n){if(s.friendlyName===", "){this._logger.debug(", "),r.push({shellInfo:s,capability:{capability:10,details:", "}});continue}if(!YX(s.name)&&!s.name.includes(", "} ${s.args?.join(", ",tempDir:void 0}}async _createZshShellInfo(r){if(process.platform!==", "&&process.platform!==", ")return;let n=r.find(i=>i.name===", ")?.path;if(n)try{let i=await this._createZshTempEnvironment();return{name:", ",path:n,args:void 0,env:{ZDOTDIR:i},friendlyName:", "});return}}async _createZshTempEnvironment(){let r=await s2e(", ");return await Eo(Sb.default.join(r,", "),`# Empty .zshrc file created by Augment\n`),r}async _createBashShellInfo(r){if(process.platform!==", ")return;let n=r.find(u=>u.name===", ")?.path;if(!n)return;let i=zt.window.createTerminal({name:", ");if(c===-1||c>=a.length-1)return;let l=a[c+1];return{name:", ",path:n,args:[", ",l],friendlyName:", "}}_getOSSection(){return process.platform===", ":process.platform===", "}_processShellInfo(r,n){if(r.name===", "&&process.platform===", "){let i=this._getGitBashInfo(r.path,r.args);if(!i){n&&(this._logger.debug(", "),cn().reportEvent({eventName:un.vsCodeTerminalFailedToFindGitBash,conversationId:", "}));return}r.name=i.name,r.path=i.path,r.args=i.args}if(r.name.includes(", ")&&process.platform===", "){let i=this._findPowerShellPath();if(!i){n&&(this._logger.debug(", "),cn().reportEvent({eventName:un.vsCodeTerminalFailedToFindPowerShell,conversationId:", "}));return}r.name=", "}));return}}_getVSCodeDefaultProfile(){let r=this._getOSSection(),i=zt.workspace.getConfiguration(", ").get(r);if(typeof i==", "),c=s.get(", ",path:i,args:n??[", "],friendlyName:", "]}`];for(let n of r){if(!oA(n))continue;let i=[Sb.default.join(n,", "),Sb.default.join(n,", "]}`],n,i;for(let o of r){let a=Sb.default.join(o,", ");if(!(!oA(o)||!oA(a))){for(let[c,l]of gb(a))if(l===", "&&c.match(/^\\d+$/)){let u=Sb.default.join(a,c,", ");Vr(u)&&(!n||c>n)&&(n=c,i=u)}}}if(i)return i;let s=[", "];for(let o of s){let a=Sb.default.join(process.env.windir??", ")&&!r.includes(", ",properties:{command:{type:", "},wait:{type:", "},max_wait_seconds:{type:", "},cwd:{type:", "}},required:[", ";inputSchemaJson=JSON.stringify({type:", ",properties:{terminal_id:{type:", "wait=true", "max_wait_seconds", "wait=false", "},input_text:{type:", "${i??n}\\`.\n", "unsupportedAPI", "shellNotFound", "noShellIntegration", "noExecutionEvents", "buggyExecutionEvents", "noOutput", "noisyOutput", "buggyOutput", "supported", "Check not started", "TerminalCapabilityChecker", "echo 'Terminal capability test'", "Terminal capability check result: ${this._getCapabilityDescription(r)}", "Error during initial terminal capability check: ${r.message}", "${ex[r.capability]}: ${r.details}", "1.93.0", "VSCode version ${zt.version} does not support required terminal APIs", "Shell ${this._shellInfo.path??this._shellInfo.name} does not exist", "Augment Terminal Capability Check", "Terminal receives execution events but for incorrect commands", "No execution events received", "Execution event received but no output was captured", "Terminal has all required capabilities with exact output", "Terminal has all required capabilities but output contains extra content", "Terminal has all required capabilities but output is incorrect", "Unknown terminal capability", "Terminal capability check failed: ${s.message}", "Check failed with error: ${s.message}", "Terminal not created", "Shell integration already available", "Shell integration detected", "Shell integration", "onDidChangeTerminalShellIntegration API not available", "Terminal capability test", "Error reading execution output: ${f.message}", "Buggy execution event received for command: ${c.execution.commandLine.value}", "Execution event", "Error executing test command: ${i.message}", "Timeout waiting for ${r}", "ignore", "exit", "Read a file.", "The path of the file to read.", "file_path", "Cannot read file: ${s}", "Failed to read file: ${s}: ${o.message??\"\"}", "The path of the file to edit.", "A brief description of the edit to be made. 1-2 sentences.", "A detailed and precise description of the edit. Can include natural language and code snippets.", "edit_summary", "detailed_edit_description", "Cannot read file: ${o}", "Cannot resolve path: ${o}", "Failed to edit file: ${o}", "No changes made to file {${o}}", "${N}\n${H.map(q=>", "${N.range.start.line}-${N.range.end.line}: ${N.message}", "Failed to edit file: ${o}: ${c.message??\"\"}", "The URL to open in the browser.", "Opened ${s} in browser", "Failed to open ${s} in browser: system denied the request", "Failed to open URL in browser: ${s.message??\"\"}", "Required list of file paths to get issues for from the IDE.", "paths", "No paths provided.", "No diagnostics found.", "The IDE reports the following issues:\n${c}", "Failed to get diagnostics: ${s.message??\"\"}", "GitCommitRetrievalTool", "Using git commit checkpoint ID: ${a}", "No git commit checkpoint ID available, using standard retrieval", "Failed to retrieve codebase information: ${o.message??\"\"}", "Whether to read only the selected text in the terminal.", "There are no open VSCode terminals.", "Failed to read terminal output: ${i.message??\"\"}", "workbench.action.terminal.selectAll", "workbench.action.terminal.copySelection", "workbench.action.terminal.clearSelection", "SpawnSubAgentTool", "A short one-sentence summary of the task (for display purposes).", "The detailed initial prompt/task for the sub-agent to work on.", "The repository URL for the sub-agent to work with (e.g., 'https://github.com/user/repo').", "The git branch for the sub-agent to start from (e.g., 'main', 'develop').", "prompt", "repo_url", "branch", "Summary cannot be empty.", "Prompt cannot be empty.", "Repository URL cannot be empty.", "Branch cannot be empty.", "Failed to check GitHub authentication status", "Failed to verify GitHub authentication. Please try again or check your connection.", "Invalid repository URL format.", "Creating remote sub-agent with prompt: \"${c}\", repo: ${l}, branch: ${u}", "Failed to create remote sub-agent: status ${A.status}", "Successfully created remote sub-agent with ID: ${A.remote_agent_id}", "Failed to create remote sub-agent", "Failed to create remote sub-agent: ${a instanceof Error?a.message:String(a)}", "lastRemoteAgentSetupScript", "No previous setup script found, using no setup script", "Using setup script: ${s.name} from ${s.path}", "Previously used setup script not found: ${r}", "Failed to get default setup script", "VSCodeRemoteInfo", "augment-user-assets", "virtual", "${n}:/${r}?left", "${n}:/${r}?right", "vscode.diff", "Diff - ${r}", "CommandManager", "Registering group '${r}' with ${n.length} commands.", "AugmentCommand", "commandID must be defined on subclass ${this.constructor.name}", "Not running '${this.commandID}' command with type ${this.type}.", "Untitled-", "Deleting file ${this.filePath.absPath}", "Failed to delete file ${this.filePath.absPath}: ${String(r)}", "Writing to doc ${this._instance}", "Failed to open document ${this.filePath.absPath}", "Failed to read file ${this.filePath.absPath}: ${String(t)}", "vscode-notebook-cell", "vscode-augment.internal-dv.o", "public", "No workspace manager", "No keybinding watcher", "vscode-augment.internal-dv.i", "No active editor.", "Code instructions are not supported in notebooks.", "vscode-augment.internal-dv.aac", "vscode-augment.internal-dv.afc", "vscode-augment.internal-dv.rfc", "vscode-augment.internal-dv.fpc", "vscode-augment.internal-dv.fnc", "vscode-augment.internal-dv.c", "Start line should be already set when replacement text is set", "End line should be already set when edit is completed.", "PURE INSERTION AFTER LINE:", "Edits overlap, which is not supported", "DiffViewSessionReporter", "accept", "Logging diff panel resolution", "No request id found for diff panel resolution", "instruction", "Logged instruction resolution", "smart-paste", "Logged smart paste resolution", "Unknown session origin: ${this.sessionOrigin}", "find-file-request", "find-folder-request", "find-recently-opened-files", "diff-view-fetch-pending-stream", "diff-view-resolve-chunk", "diff-view-window-focus-change", "dispose-diff-view", "diff-view-loaded", "chat-instruction-message", "diff-view-notify-reinit", "diff-view-accept-all-chunks", "diff-view-accept-selected-chunk", "diff-view-reject-focused-chunk", "diff-view-focus-prev-chunk", "diff-view-focus-next-chunk", "vscode-augment.internal-dv.panel-focused", "onUserSendInstruction: ${JSON.stringify(r)}", "Invalid message type or data for instruction", "Failed to send instruction: ${i}", "Guidelines watcher not initialized", "Sending request to the instruction model: ${n}", "Error in runChatInstructionStream: ${He(A)}", "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789", "${this.streamId}-${VPe()}", "Error streaming in changes to Augment Diff. Please try again.", "Error streaming in changes to Augment Diff: ${r.message}", "diff-view-diff-stream-started", "diff-view-diff-stream-chunk", "diff-view-diff-stream-ended", "diff-view-initialize", "reject", "rgba(0, 255, 0, 0.1)", "rgba(0, 200, 0, 0.05)", "rgba(150, 255, 150, 0.9)", "rgba(0, 100, 0, 0.8)", "rgba(255, 0, 0, 0.05)", "rgba(255, 200, 200, 0.9)", "rgba(150, 0, 0, 0.8)", "rgba(150, 0, 0, 0.3)", "0 0 0 1em", "1px solid ${i.removed.border}", "0 5px 0 0", "original", "modified", "**Removed content:**\n\n", "\\u2296 ${d.count} line${d.count===1?\"\":\"s\"} removed", "diff-view.html", "panel-icon-light.svg", "panel-icon-dark.svg", "augmentDiffView", "DiffViewWebviewPanel", "Augment Diff - ${o}", "ClientWorkspaces", "Failed to delete file ${t.absPath}:", "Cancelling findFiles due to timeout after ${i}ms", "Path is outside the workspace", "Path is not a directory", "Directory not found or cannot be read", "Failed to list directory ${r}:", "node_modules/@vscode/ripgrep/bin/", "node_modules/vscode-ripgrep/bin", "node_modules.asar.unpacked/vscode-ripgrep/bin/", "node_modules.asar.unpacked/@vscode/ripgrep/bin/", "Failed to open document ${e.absPath}", "Scope ${String(n)} is not supported", "Scope ${String(i)} is not supported", "remoteAgentStore", "upstream", "fork", "github", "gitlab", "bitbucket", "vscodeBackgroundAgentsMinVersion", "RemoteWorkspaceResolver", "Background agents are not enabled, skipping remote agent detection", "Error starting remote agent detection", "Error getting hostname", "Invalid response from listRemoteAgents", "Error fetching remote agents", "Error checking if remote agent", "Timeout after ${n}ms: ${i}", "window.title", "Error with direct file editing, trying command-based approach", "All settings update methods failed", "Could not determine remote settings file path", "Error parsing remote settings file", ".vscode-server", "Machine", "settings.json", "Error determining remote settings file path", "Error determining remote settings file path: ${String(r)}", "workbench.action.openRemoteSettingsFile", "Failed to open remote settings file", "Failed to read remote settings file", "Failed to parse remote settings file", "Failed to write remote settings file", "workbench.action.closeActiveEditor", "vscode-augment.focusAugmentPanel", "Timeout opening and focusing Augment panel", "Error updating VSCode settings", "RemoteAgentSessionEventReporter", "GitReferenceMessenger", "Using dev deploy", "get-git-branches-request", "get-workspace-diff-request", "get-remote-url-request", "git-fetch-request", "is-git-repository-request", "is-github-authenticated-request", "authenticate-github-request", "revoke-github-access-request", "list-github-repos-for-authenticated-user-request", "list-github-repo-branches-request", "get-github-repo-request", "get-current-local-branch-request", "git remote", "git branch -r | grep $(git branch --show-current)", "git symbolic-ref refs/remotes/${o}/HEAD", "refs/remotes/${o}/", "git for-each-ref --format=\"%(refname:lstrip=3)\" refs/remotes/${o}/", "HEAD", "${o}/${f}", "get-git-branches-response", "Failed to get local git branches:", "git diff ${n}/${r} || true", "git ls-files --others --exclude-standard", "git diff --no-index NUL ${o} || exit 0", "git diff --no-index /dev/null ${o} || true", "get-workspace-diff-response", "Failed to get workspace diff:", "git remote get-url origin", "Failed to get remote url, no remote found", "https://", "get-remote-url-response", "ssh://", "git@", ".com:", ".com/", "Failed to get remote url:", "Failed to locally get remote url:", "git fetch", "git-fetch-response", "Failed to fetch remote branch:", "git rev-parse --is-inside-work-tree 2>NUL", "git rev-parse --is-inside-work-tree 2>/dev/null", "isGitRepository: ", "is-git-repository-response", "Failed to check if is git repository:", "is-github-authenticated-response", "Failed to check GitHub authentication status:", "Failed to authenticate with GitHub: GitHub OAuth URL not found", "authenticate-github-response", "GitHub OAuth URL not found", "Failed to authenticate with GitHub: Failed to open browser for GitHub authentication", "<PERSON><PERSON><PERSON> opened for GitHub authentication", "Failed to open browser", "Failed to authenticate with GitHub:", "Failed to authenticate with GitHub: ", "Error: ${n instanceof Error?n.message:String(n)}", "revoke-github-access-response", "GitHub access revoked successfully", "GitHub is not configured", "Failed to revoke GitHub access: ${HQ[r.status]}", "Failed to revoke GitHub access:", "Error: ${r instanceof Error?r.message:String(r)}", "list-github-repos-for-authenticated-user-response", "Failed to list user repos:", "list-github-repo-branches-response", "Failed to list repo branches:", "get-github-repo-response", "get-current-local-branch-response", "Failed to get current local branch:", "git branch --show-current", "Reported GitHub API failure with error code ${n} for remote agent ${r}", "Failed to report GitHub API failure:", "getDiffDescription token limit exceeded", "Token limit exceeded", "Failed to get descriptions:", "Failed to get descriptions: ${r instanceof Error?r.message:String(r)}", "${o}-${a}", "File: ${b.new_path||b.old_path}\nChange type: ${b.change_type||\"modified\"}", "LLM request timed out", "LLM request timed out or failed:", "Failed to parse LLM response:", "Skipping LLM grouping due to size: ${i} bytes, ${n.length} files, ${s} changes", "${g}-${A}", "Failed to group changes with LLM:", "<<<<<<<", " ${n.label.a}", "=======", ">>>>>>>", " ${n.label.b}", "get-diff-explanation-request", "get-diff-group-changes-request", "get-diff-descriptions-request", "apply-changes-request", "open-file-request", "report-agent-changes-applied", "gemini-2-flash-001-simple-port", "get-diff-explanation-response", "Failed to explain diff:", "get-diff-group-changes-response", "Failed to group changes:", "get-diff-descriptions-response", "Failed to get descriptions: ${i instanceof Error?i.message:String(i)}", "File path cannot be empty", "New code must be a string", "apply-changes-response", "Failed to apply workspace edit", "open-file-response", "No workspace folder available", "remote.SSH", "remotePlatform", "\\\\bHost\\\\s+${e}\\\\b", "\\\\bHost\\\\s+${e}\\\\b[\\\\s\\\\S]*?(?=\\\\bHost\\\\b|$)", "ms-vscode", "defaultExtensions", "connectTimeout", "\\\\$&", "^\\\\s*Include\\\\s+${t}\\\\s*$", "# Added by Augment for remote agent SSH connections", "vscode.openFolder", "vscode-remote://ssh-remote+${e}/mnt/persist/workspace", "vscode.removeFromRecentlyOpened", "configFile", "${n.trim()}\n\n${o}", "Host ${t}\n", "   HostName ${n}\n", "KnownHostsCommand", "ProxyCommand", "pathToOpenSSL", "git --exec-path", "${l.message}. stderr: ${d.trim()}", "${i.substring(0,i.lastIndexOf(\"Git\")+3)}/usr/bin/openssl.exe", "Error resolving OpenSSL path:", "Include ${await this.getAugmentConfigPath()}", "${dZ}\n${l}\n", "Include ${i}", "${dZ}\n${o}\n\n${n}", "# Augment SSH Configuration\n", "StrictHostKeyChecking", "UserKnownHostsFile", "/dev/null", "<(security find-certificate -a -p /System/Library/Keychains/SystemRootCertificates.keychain)", "${r.ssh_config_options[c].value} -CAfile ${a}", "X-Augment-Proxy-Hostname", "X-Augment-Proxy-OpenSSL-Command", "${n} ${a}", "${n} s_client -connect ${o}:\"%p\" -quiet -verify_return_error -servername \"%h\"", "X-Augment-<PERSON><PERSON><PERSON>", "Port", "[${d}]:${f} ${A}", "${d} ${A}", "IdentityFile", "X-Augment-", "IgnoreUnknown", "X-Augment-*", "ed25519", "augment-remote-agent-key-${new Date().toISOString().split(\"T\")[0]}", "augment_remote_agent_key", "ssh-keys", "EEXIST", "id_${r.type}", "${s}.pub", "ssh-keygen -t ${r.type}", " -b ${r.bits}", " -C \"${r.comment}\"", " -f \"${s}\" -N \"${l}\"", "Failed to generate SSH key: ${String(u)}", "ms-vscode-remote.remote-ssh", "RemoteAgentSshManager", "workbench.extensions.installExtension", "Remote SSH extension is not installed", "Install Remote SSH Extension", "The Remote SSH extension is required to connect to remote agents. Would you like to install it now?", "Remote SSH extension is required but not installed", "Failed to connect to remote agent: ${s}", "Retry", "${n}.pub", "Error sending message to ${t}", "Error broadcasting message to ${this._messageHandlers.size} handlers.", "remote-agent-diff-panel-messenger", "remote-agent-diff-panel-loaded", "remote-agent-diff-panel-set-opts", "remote agent diff", "remote-agent-diff.html", "close-remote-agent-diff-panel", "Remote Agent Changes", "show-remote-agent-diff-panel", "update-shared-webview-state", "get-shared-webview-state", "get-shared-webview-state-response", "remote agent home", "remote-agent-home.html", "close-remote-agent-home-panel", "home", "Remote Agents", "remote-agent-home-panel-messenger", "show-remote-agent-home-panel", "RemoteAgentsMessenger initialized, setting up onDidChangeTextDocument listener", "diff-view-file-focus", "RemoteAgentsMessenger", "remote-agents-messenger", "Disposing RemoteAgentsMessenger", "Registering RemoteAgentsMessenger with AsyncMsgHandler", "create-remote-agent-request", "delete-remote-agent-request", "get-remote-agent-chat-history-request", "remote-agent-overviews-stream-request", "remote-agent-history-stream-request", "cancel-remote-agents-stream-request", "remote-agent-chat-request", "remote-agent-interrupt-request", "get-remote-agent-overviews-request", "list-setup-scripts-request", "save-setup-script-request", "delete-setup-script-request", "rename-setup-script-request", "remote-agent-ssh-request", "set-remote-agent-notification-enabled", "get-remote-agent-notification-enabled-request", "delete-remote-agent-notification-enabled", "remote-agent-notify-ready", "remote-agent-workspace-logs-request", "open-scratch-file-request", "open-diff-in-buffer", "get-remote-agent-status", "save-last-remote-agent-setup-request", "get-last-remote-agent-setup-request", "set-remote-agent-pinned-status", "get-remote-agent-pinned-status-request", "delete-remote-agent-pinned-status", "remote-agent-pause-request", "remote-agent-resume-request", "report-remote-agent-event", "remote-agent-status-changed", "Created remote agent:", "create-remote-agent-response", "Failed to create remote agent:", "Unknown error", "Are you sure you want to delete this remote agent?", "Cancel", "Delete", "delete-remote-agent-response", "User cancelled deletion", "Failed to delete remote agent:", "get-remote-agent-overviews-response", "Failed to get remote agent overviews:", "get-remote-agent-chat-history-response", "Failed to get remote agent chat history:", "remote-agent-overviews-stream-response", "remote-agent-history-stream-response", "remote-agent-chat-response", "Failed to get remote agent chat response:", "Interrupting remote agent:", "remote-agent-interrupt-response", "Failed to interrupt remote agent:", "remote-agent-ssh-response", "Failed to get remote agent ssh config:", "remoteAgentNotificationEnabled", "Failed to set remote agent notification enabled:", "get-remote-agent-notification-enabled-response", "Failed to get remote agent notification enabled:", "Failed to delete remote agent notification enabled:", "Remote agent \"${(n&&n.length>s?", ":n)||\"Remote Agent\"}\" is waiting for your input!", "Go to Remote Agent", "remote-agent-select-agent-id", "list-setup-scripts-response", "Failed to list setup scripts:", "save-setup-script-response", "Failed to save setup script:", "remote-agent-workspace-logs-response", "Failed to get remote agent workspace logs:", "delete-setup-script-response", "Failed to delete setup script:", "rename-setup-script-response", "Failed to rename setup script:", "shellscript", "Failed to open scratch file:", "Failed to open diff in buffer:", "Remote agent status handler called", "Remote agent status: isRemoteAgentSshWindow=${r}, remoteAgentId=${n}", "remote-agent-status-response", "Error getting remote agent status", "lastRemoteAgentGitRepoUrl", "lastRemoteAgentGitBranch", "Failed to save last remote agent setup:", "get-last-remote-agent-setup-response", "Failed to get last remote agent setup:", "remoteAgentPinnedStatus", "Failed to set remote agent pinned status:", "get-remote-agent-pinned-status-response", "Failed to get remote agent pinned status:", "Failed to delete remote agent pinned status:", "Failed to pause remote agent:", "Failed to resume remote agent:", "Failed to report setup page opened event:", "Invalid source folder uri", "cannot access source folder: ${t}", "source folder is not a directory", "source folder already exists", "source folder is home directory", "source folder is not an external source folder", "source folder does not exist in workspace", "source folder enumeration is not complete", "vscodeWorkspaceFolder", "externalFolder", "nestedWorkspaceFolder", "nestedExternalFolder", "untrackedFolder", "toString", "toJSON", "valueOf", "invalid varint", "0000000", "-9223372036854775808", "9223372036854775807", "18446744073709551615", "invalid int64: ${o}", "invalid uint64: ${o}", "invalid int64: ", "invalid uint64: ", "DOUBLE", "FLOAT", "INT64", "UINT64", "INT32", "FIXED64", "FIXED32", "BOOL", "STRING", "BYTES", "UINT32", "SFIXED32", "SFIXED64", "SINT32", "SINT64", "reflect unsafe local", "list", "scalar", "message field with implicit presence", "jsonName", "cannot parse ${e} default value: ${t}", "cannot parse ${se[e]} default value: ${t}", "-inf", "$typeName", "google.protobuf.FileDescriptorSet", "Unable to resolve ${l}, imported by ${a.name}", "kind", "registry", "extension", "service", "file ${e.name}", "enum ${this.typeName}", "enum_value", "enum value ${u.typeName}.${f}", "message ${this.typeName}", "service ${this.typeName}", "bidi_streaming", "client_streaming", "server_streaming", "unary", "invalid MethodDescriptorProto: input_type ${e.inputType} not found", "invalid MethodDescriptorProto: output_type ${e.inputType} not found", "rpc ${t.typeName}.${u}", "oneof", "oneof ${t.typeName}.${this.name}", "field", "[${A}]", "extension ${A}", "invalid FieldDescriptorProto: extendee ${e.extendee} not found", "field ${p.typeName}.${e.name}", "invalid FieldDescriptorProto: type_name ${e.typeName} not found", "defaultValue", "proto2", "proto3", "editions", "${e.name}: unsupported edition", "${e.name}: unsupported syntax \"${e.syntax}\"", "Cannot find ${r}, imported by ${e.name}", "${t.typeName}.${e.name}", "${r.proto.package}.${e.name}", "${e.name}", "oneofIndex", "invalid FieldDescriptorProto: oneof #${e.oneofIndex} for field #${e.number} not found", "fieldPresence", "packed", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enumType", "messageEncoding", "feature default for edition ${t.edition} not found", "google.protobuf.FileDescriptorProto", "google.protobuf.DescriptorProto", "google.protobuf.DescriptorProto.ExtensionRange", "google.protobuf.FieldDescriptorProto", "google.protobuf.FieldOptions", "google.protobuf.FieldOptions.EditionDefault", "google.protobuf.EnumDescriptorProto", "google.protobuf.EnumValueDescriptorProto", "invalid base64 string", "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/", "FieldValueInvalidError", "FieldListRange<PERSON>rror", "ForeignFieldError", "@bufbuild/protobuf/text-encoding", "Varint", "Bit64", "LengthDelimited", "StartGroup", "EndGroup", "Bit32", "invalid state, fork stack empty", "illegal tag: field no ", " wire type ", "invalid end group tag", "cant skip wire type ", "premature EOF", "invalid int32: ", "invalid uint32: ", "invalid float32: ", "expected ${HLe(e)}, got ${Ps(t)}", "expected ${$Le(e)}, got ${Ps(t)}", "list item #${t+1}: ${r3(e,r,n)}", "invalid map key: ${r3({scalar:e.mapKey},t,n)}", "map entry ${Ps(t)}: ${r3(e,r,i)}", "${e.toFixed()} out of range", "invalid UTF8", "${e} out of range", ": ${r}", ", got ${Ps(t)}", "expected ${cqt(e.scalar)}", "expected ${e.enum.toString()}", "expected ${WLe(e.message)}", "Uint8Array(${e.length})", "Array(${e.length})", "message ${e.$typeName}", "\"${e.split('\"').join('\\\\\"')}\"", "ReflectMessage (${e.typeName})", "ReflectList (${e.message.toString()})", "ReflectList (${e.enum.toString()})", "ReflectList (${se[e.scalar]})", "ReflectMap (${se[e.mapKey]}, ${e.message.toString()})", "ReflectMap (${se[e.mapKey]}, ${e.enum.toString()})", "ReflectMap (${se[e.map<PERSON>ey]}, ${se[e.scalar]})", "bigint (int64)", "bigint (uint64)", "Uint8Array", "number (float64)", "number (float32)", "number (uint32)", "number (int32)", "google.protobuf.", "DoubleValue", "FloatValue", "Int64Value", "UInt64Value", "Int32Value", "UInt32Value", "BoolValue", "StringValue", "BytesValue", "google.protobuf.Struct", "google.protobuf.Value", "cannot use ${t.toString()} with message ${e.$typeName}", "list item #${t+1}: out of range", "longAsString", "structValue", "listValue", "nullValue", "numberValue", "stringValue", "boolValue", "google.protobuf.ListValue", "cannot encode ${i} to binary: required field not set", "cannot encode field ${t}.${r} to binary: ${s.message}", "google/protobuf/descriptor.proto", "google.protobuf", "FileDescriptorSet", ".google.protobuf.FileDescriptorProto", "FileDescriptorProto", "package", "dependency", "public_dependency", "weak_dependency", "message_type", ".google.protobuf.DescriptorProto", "enum_type", ".google.protobuf.EnumDescriptorProto", ".google.protobuf.ServiceDescriptorProto", ".google.protobuf.FieldDescriptorProto", "options", ".google.protobuf.FileOptions", "source_code_info", ".google.protobuf.SourceCodeInfo", "syntax", "edition", ".google.protobuf.Edition", "DescriptorProto", "nested_type", "extension_range", ".google.protobuf.DescriptorProto.ExtensionRange", "oneof_decl", ".google.protobuf.OneofDescriptorProto", ".google.protobuf.MessageOptions", "reserved_range", ".google.protobuf.DescriptorProto.ReservedRange", "reserved_name", "ExtensionRange", ".google.protobuf.ExtensionRangeOptions", "ReservedRange", "ExtensionRangeOptions", "uninterpreted_option", ".google.protobuf.UninterpretedOption", "declaration", ".google.protobuf.ExtensionRangeOptions.Declaration", "features", ".google.protobuf.FeatureSet", "verification", ".google.protobuf.ExtensionRangeOptions.VerificationState", "UNVERIFIED", "Declaration", "full_name", "reserved", "repeated", "VerificationState", "DECLARATION", "FieldDescriptorProto", ".google.protobuf.FieldDescriptorProto.Label", ".google.protobuf.FieldDescriptorProto.Type", "type_name", "extendee", "default_value", "oneof_index", "json_name", ".google.protobuf.FieldOptions", "proto3_optional", "Type", "TYPE_DOUBLE", "TYPE_FLOAT", "TYPE_INT64", "TYPE_UINT64", "TYPE_INT32", "TYPE_FIXED64", "TYPE_FIXED32", "TYPE_BOOL", "TYPE_STRING", "TYPE_GROUP", "TYPE_MESSAGE", "TYPE_BYTES", "TYPE_UINT32", "TYPE_ENUM", "TYPE_SFIXED32", "TYPE_SFIXED64", "TYPE_SINT32", "TYPE_SINT64", "Label", "LABEL_OPTIONAL", "LABEL_REPEATED", "LABEL_REQUIRED", "OneofDescriptorProto", ".google.protobuf.OneofOptions", "EnumDescriptorProto", ".google.protobuf.EnumValueDescriptorProto", ".google.protobuf.EnumOptions", ".google.protobuf.EnumDescriptorProto.EnumReservedRange", "EnumReservedRange", "EnumValueDescriptorProto", ".google.protobuf.EnumValueOptions", "ServiceDescriptorProto", ".google.protobuf.MethodDescriptorProto", ".google.protobuf.ServiceOptions", "MethodDescriptorProto", "input_type", "output_type", ".google.protobuf.MethodOptions", "FileOptions", "java_package", "java_outer_classname", "java_multiple_files", "java_generate_equals_and_hash", "java_string_check_utf8", "optimize_for", ".google.protobuf.FileOptions.OptimizeMode", "SPEED", "go_package", "cc_generic_services", "java_generic_services", "py_generic_services", "cc_enable_arenas", "objc_class_prefix", "csharp_namespace", "swift_prefix", "php_class_prefix", "php_namespace", "php_metadata_namespace", "ruby_package", "OptimizeMode", "CODE_SIZE", "LITE_RUNTIME", "MessageOptions", "message_set_wire_format", "no_standard_descriptor_accessor", "map_entry", "deprecated_legacy_json_field_conflicts", "FieldOptions", "ctype", ".google.protobuf.FieldOptions.CType", "jstype", ".google.protobuf.FieldOptions.JSType", "JS_NORMAL", "unverified_lazy", "weak", "debug_redact", "retention", ".google.protobuf.FieldOptions.OptionRetention", "targets", ".google.protobuf.FieldOptions.OptionTargetType", "edition_defaults", ".google.protobuf.FieldOptions.EditionDefault", "feature_support", ".google.protobuf.FieldOptions.FeatureSupport", "EditionDefault", "FeatureSupport", "edition_introduced", "edition_deprecated", "deprecation_warning", "edition_removed", "CType", "CORD", "STRING_PIECE", "JSType", "JS_STRING", "JS_NUMBER", "OptionRetention", "RETENTION_UNKNOWN", "RETENTION_RUNTIME", "RETENTION_SOURCE", "OptionTargetType", "TARGET_TYPE_UNKNOWN", "TARGET_TYPE_FILE", "TARGET_TYPE_EXTENSION_RANGE", "TARGET_TYPE_MESSAGE", "TARGET_TYPE_FIELD", "TARGET_TYPE_ONEOF", "TARGET_TYPE_ENUM", "TARGET_TYPE_ENUM_ENTRY", "TARGET_TYPE_SERVICE", "TARGET_TYPE_METHOD", "OneofOptions", "EnumOptions", "allow_alias", "EnumValueOptions", "ServiceOptions", "MethodOptions", "idempotency_level", ".google.protobuf.MethodOptions.IdempotencyLevel", "IDEMPOTENCY_UNKNOWN", "IdempotencyLevel", "NO_SIDE_EFFECTS", "IDEMPOTENT", "UninterpretedOption", ".google.protobuf.UninterpretedOption.NamePart", "identifier_value", "positive_int_value", "negative_int_value", "double_value", "string_value", "aggregate_value", "NamePart", "name_part", "is_extension", "FeatureSet", "field_presence", ".google.protobuf.FeatureSet.FieldPresence", "EXPLICIT", "IMPLICIT", ".google.protobuf.FeatureSet.EnumType", "CLOSED", "OPEN", "repeated_field_encoding", ".google.protobuf.FeatureSet.RepeatedFieldEncoding", "EXPANDED", "PACKED", "utf8_validation", ".google.protobuf.FeatureSet.Utf8Validation", "NONE", "VERIFY", "message_encoding", ".google.protobuf.FeatureSet.MessageEncoding", "LENGTH_PREFIXED", "json_format", ".google.protobuf.FeatureSet.JsonFormat", "LEGACY_BEST_EFFORT", "ALLOW", "enforce_naming_style", ".google.protobuf.FeatureSet.EnforceNamingStyle", "STYLE_LEGACY", "STYLE2024", "FieldPresence", "FIELD_PRESENCE_UNKNOWN", "LEGACY_REQUIRED", "EnumType", "ENUM_TYPE_UNKNOWN", "RepeatedFieldEncoding", "REPEATED_FIELD_ENCODING_UNKNOWN", "Utf8Validation", "UTF8_VALIDATION_UNKNOWN", "MessageEncoding", "MESSAGE_ENCODING_UNKNOWN", "DELIMITED", "JsonFormat", "JSON_FORMAT_UNKNOWN", "EnforceNamingStyle", "ENFORCE_NAMING_STYLE_UNKNOWN", "FeatureSetDefaults", "defaults", ".google.protobuf.FeatureSetDefaults.FeatureSetEditionDefault", "minimum_edition", "maximum_edition", "FeatureSetEditionDefault", "overridable_features", "fixed_features", "SourceCodeInfo", "location", ".google.protobuf.SourceCodeInfo.Location", "Location", "leading_comments", "trailing_comments", "leading_detached_comments", "GeneratedCodeInfo", "annotation", ".google.protobuf.GeneratedCodeInfo.Annotation", "Annotation", "source_file", "semantic", ".google.protobuf.GeneratedCodeInfo.Annotation.Semantic", "Semantic", "ALIAS", "Edition", "EDITION_UNKNOWN", "EDITION_LEGACY", "EDITION_PROTO2", "EDITION_PROTO3", "EDITION_2023", "EDITION_2024", "EDITION_1_TEST_ONLY", "EDITION_2_TEST_ONLY", "EDITION_99997_TEST_ONLY", "EDITION_99998_TEST_ONLY", "EDITION_99999_TEST_ONLY", "EDITION_MAX", "GROUP", "MESSAGE", "ENUM", "OPTIONAL", "REPEATED", "REQUIRED", "The shell command to execute.", "bash", "bash -l -c ${pPe([s])}", "Failed to execute shell command: ${a instanceof Error?a.message:String(a)}", "stderr", "stdout", "type.googleapis.com/${e}", "invalid type url: ${e}", "NULL_VALUE", "extension ${e.typeName} can only be applied to message ${e.extendee.typeName}", "cannot encode ${s} to JSON: required field not set", "cannot encode ${e} to JSON: expected number, got ${Ps(t)}", "google.protobuf.NullValue", "cannot encode ${e} to JSON: ${(r=cg(e,t))===null||r===void 0?void 0:r.message}", "cannot encode ${e} to JSON: ${(n=cg(e,t))===null||n===void 0?void 0:n.message}", "Infinity", "-Infinity", "cannot encode ${e} to JSON: ${(i=cg(e,t))===null||i===void 0?void 0:i.message}", "cannot encode ${e} to JSON: ${(s=cg(e,t))===null||s===void 0?void 0:s.message}", "cannot encode ${e} to JSON: ${(o=cg(e,t))===null||o===void 0?void 0:o.message}", "cannot encode ${e} to JSON: ${(a=cg(e,t))===null||a===void 0?void 0:a.message}", "google.protobuf.Any", "google.protobuf.Timestamp", "google.protobuf.Duration", "google.protobuf.FieldMask", "cannot encode message ${e.$typeName} to JSON: \"${e.typeUrl}\" is not in the type registry", "@type", "cannot encode message ${e.$typeName} to JSON: value out of range", "000000", "cannot encode message ${e.$typeName} to JSON: lowerCamelCase of path name \"", "\" is irreversible", "${e.$typeName} cannot be NaN or Infinity", "${e.$typeName} must have a value", "0001-01-01T00:00:00Z", "9999-12-31T23:59:59Z", "cannot encode message ${e.$typeName} to JSON: nanos must not be negative", ".000Z", "cannot decode ${i.field()} from JSON: ${i.message}", "cannot decode ${e.desc} from JSON: ${Ps(t)}", "oneof set multiple times by ${l.name} and ${c.name}", "cannot decode ${e.desc} from JSON: key \"${o}\" is unknown", "expected object, got ", "map value must not be null", "expected Array, got ", "list item must not be null", "cannot decode ${e} from JSON: ${Ps(t)}", "unexpected NaN number", "unexpected infinite number", "cannot decode message ${e.$typeName} from JSON: expected object but got ${Ps(t)}", "cannot decode message ${e.$typeName} from JSON: \"@type\" is empty", "cannot decode message ${e.$typeName} from JSON: \"@type\" is invalid", "cannot decode message ${e.$typeName} from JSON: ${i} is not in the type registry", "cannot decode message ${e.$typeName} from JSON: ${Ps(t)}", "cannot decode message ${e.$typeName} from JSON: invalid RFC 3339 string", "cannot decode message ${e.$typeName} from JSON: path names must be lowerCamelCase", "cannot decode message ${e.$typeName} from JSON ${Ps(t)}", "com.augmentcode.client.rpc", "com.augmentcode.client.rpc.request", "com.augmentcode.client.rpc.response", "No handlers registered for service: ${t.serviceTypeName}", "gRPC server error for ${t.serviceTypeName}.${t.methodLocalName} (ID: ${t.id}): ${t.error}", "gRPC response missing data field for ${t.serviceTypeName}.${t.methodLocalName} (ID: ${t.id})", "Failed to process gRPC response for ${t.serviceTypeName}.${t.methodLocalName} (ID: ${t.id}): ${i}", "Service name is required for unary calls", "gRPC request aborted before sending: ${l}.${c} (ID: ${a})", "gRPC request aborted during execution: ${l}.${c} (ID: ${a})", "Streaming is not supported by this transport", "Canceled", "InvalidArgument", "DeadlineExceeded", "AlreadyExists", "PermissionDenied", "ResourceExhausted", "FailedPrecondition", "Aborted", "OutOfRange", "Internal", "Unavailable", "DataLoss", "Unauthenticated", "ConnectError", "rawMessage", "cause", "[${XZ(t)}] ${e}", "[${XZ(t)}]", "the operation timed out", "This operation was aborted", "${e.typeName}.${n.name} is not implemented", "Service not registered: ${t.serviceTypeName}", "Server streaming is not supported", "Client streaming is not supported", "Bi-directional streaming is not supported", "Could not resolve path: ${t.path}", "BACKGROUND", "FILE", "modification", "augment://sign-in", "Augment is only available to signed in users. [Please sign in](${tee}).", "${e}--${t}--${r}", "augment-chat.focus", "GitHub.copilot", "Codeium.codeium", "github.copilot", "codeium", "ConflictingExtensions", "disabledGithubCopilot", "disabledCodeium", "copilot", "enable", "workbench.extensions.search", "@enabled GitHub Copilot", "enableConfig", "@enabled Codeium", "buffer", "src/lib/args/pathspec.ts", "src/lib/errors/git-error.ts", "src/lib/errors/git-response-error.ts", "src/lib/errors/task-configuration-error.ts", "src/lib/utils/util.ts", "src/lib/utils/argument-filters.ts", "number|boolean|function", "src/lib/utils/exit-codes.ts", "SUCCESS", "ERROR", "NOT_FOUND", "UNCLEAN", "src/lib/utils/git-output-streams.ts", "src/lib/utils/line-parser.ts", "LineParser:useMatches not implemented", "src/lib/utils/simple-git-options.ts", "string|number", "src/lib/utils/task-options.ts", "src/lib/utils/task-parser.ts", "src/lib/utils/index.ts", "bare", "rev-parse", "--is-inside-work-tree", "--git-dir", "--is-bare-repository", "src/lib/tasks/check-is-repo.ts", "tree", "src/lib/responses/CleanSummary.ts", "src/lib/tasks/task.ts", "clean", "-${e}", "-${i}", "--interactive", "src/lib/tasks/clean.ts", "Git clean interactive mode is not supported", "Git clean mode parameter (\"n\" or \"f\") is required", "Git clean unknown option found in: ", "src/lib/responses/ConfigList.ts", "config", "--${n}", "--add", "--null", "--show-origin", "--get-all", "--${t}", "--list", "--${e}", "local", "src/lib/tasks/config.ts", "system", "worktree", "src/lib/tasks/diff-name-status.ts", "git.grep: use of \"${i}\" is not supported.", "grep", "--full-name", "src/lib/tasks/grep.ts", "gre<PERSON><PERSON><PERSON><PERSON>", "--and", "soft", "src/lib/tasks/reset.ts", "hard", "merge", "keep", "simple-git", "%s ${n}", "[${e}]", "[${u}]", "${i} ${d}", "src/lib/git-logger.ts", "src/lib/runners/tasks-pending-queue.ts", "GitExecutor", "Adding task to the queue, commands = %o", "Failed %o", "Fatal exception, any as-yet un-started tasks run through this executor will not be attempted", "A fatal exception occurred in a previous task, the queue has been purged: %o", "Queue size should be zero after fatal: ${this._queue.size}", "TasksPendingQueue: attempt called for an unknown task", "Starting task", "task:${e}:${++R3.counter}", "[ERROR] child process exception %o", "ascii", "%s received %L bytes", "src/lib/runners/git-executor-chain.ts", "spawn.binary", "spawn.args", "SPAWN", "HANDLE", "passing response to task's parser as a %s", "empty task bypassing child process to call to task's parser", "Preparing to handle process response exitCode=%d stdOut=", "task.error", "exitCode=%s handling with custom error handler", "custom error handler treated as success", "custom error returned a %s", "handling as error: exitCode=%s stdErr=%s rejection=%o", "retrieving task output complete", "spawn.options", "%s %o", "spawn.before", "stdOut", "stdErr", "Passing child process stdOut/stdErr to custom outputHandler", "spawn.after", "SIGINT", "src/lib/runners/git-executor.ts", "src/lib/task-callback.ts", "Git.cwd: cannot change to non-directory \"${e}\"", "src/lib/tasks/change-working-directory.ts", "checkout", "src/lib/tasks/checkout.ts", "count-objects", "--verbose", "src/lib/tasks/count-objects.ts", "src/lib/parsers/parse-commit.ts", "core.abbrev=40", "commit", "git.commit: requires the commit message to be supplied as a string/string[]", "src/lib/tasks/commit.ts", "rev-list", "--max-parents=0", "src/lib/tasks/first-commit.ts", "hash-object", "src/lib/tasks/hash-object.ts", "src/lib/responses/InitSummary.ts", "init", "--bare", "src/lib/tasks/init.ts", "--${r[1]}", "src/lib/args/log-format.ts", "src/lib/responses/DiffSummary.ts", "src/lib/parsers/parse-diff-summary.ts", "--stat", "--numstat", "--name-only", "src/lib/parsers/parse-list-log-summary.ts", "\\xF2\\xF2\\xF2\\xF2\\xF2\\xF2 ", " \\xF2\\xF2", " \\xF2 ", "hash", "refs", "author_name", "author_email", "--stat=4096", "Summary flags are mutually exclusive - pick one of ${t.join(\",\")}", "Summary flag ${t} parsing is not compatible with null termination option '-z'", "src/lib/tasks/diff.ts", "--pretty=format:${jee}${s}${zee}", "max-count", "--max-count=${c}", "${e.from||\"\"}${l}${e.to||\"\"}", "--follow", "git.log(string, string) should be replaced with git.log({ from: string, to: string })", "src/lib/tasks/log.ts", "--pretty", "maxCount", "from", "splitter", "symmetric", "mailMap", "multiLine", "strictDate", "src/lib/responses/MergeSummary.ts", "${this.file}:${this.reason}", "success", "CONFLICTS: ${this.conflicts.join(\", \")}", "src/lib/responses/PullSummary.ts", "src/lib/parsers/parse-remote-objects.ts", "src/lib/parsers/parse-remote-messages.ts", "src/lib/parsers/parse-pull.ts", "create", "src/lib/parsers/parse-merge.ts", "Git.merge requires at least one option", "src/lib/tasks/merge.ts", "deleted", "src/lib/parsers/parse-push.ts", "(.+)", " set up to track remote branch ", " and ", "Failed to parse additional chat models: ${i.message}", "onUserCancel", "rootPath", "re<PERSON><PERSON><PERSON>", "Error calculating rules and guidelines state:", "currently-open-files", "should-show-summary", "saw-summary", "source-folders-sync-status", "sync-enabled-state", "update-guidelines-state", "source-folders-updated", "file-ranges-selected", "chat-model-reply", "Internal error: prompt length exceeded. If this condition persists, try starting a new conversation", "${n.errorDetails.message}\n${n.errorDetails.detail}", "Conversation state error. If this condition persists, try starting a new conversation.", "${n.errorDetails.message}\n${n.errorDetails.detail||\"Check MCP Server(s)\"}", "The selected text exceeds the allowable limit. Please reduce the amount of text and try again", "Classify and distill prompt missing.", "{message}", "Di<PERSON>ill prompt missing.", "${Xo}/${Ja}/${H.path}", "Rules and guidelines total character count (${T}) exceeds limit (${N})", "message_timestamp_ms", "${r.data.createdTimestamp}", "extension_timestamp_ms", "${n}", "error_timestamp_ms", "chunks_received", "${p}", "last_chunk_timestamp_ms", "${g}", "cause_${v}", "\n${b.stack}", "chat_stream_failed", "Chat stream failed: ${String(A)}${A instanceof Error?", ":\"\"}", "Error: Cancelled", "Failed to send chat message: ${A}", "Model comparison is not available at this time.", "find-external-sources-response", "Error searching rules: ${String(n)}", "Error in _smartPasteWithChatInstruction: ${He(d)}", "autofix-panel-execute-command-partial-output", "chat-autofix-execute-command-result", "chat-autofix-plan-response", "chat-create-file", "Directory ${Xa(n)} not found.", "Cannot create file at ${o.fsPath}. File already exists.", "Could not open document at ${o.fsPath} during file creation.", "Could not create file at ${o.fsPath}: ${a}", "Could not create untitled file: ${i}", "chat-smart-paste", "Apply Codeblock", "Are you sure you want to apply the codeblock to ${o.pathName}?", "Apply", "Cannot apply codeblock. No valid target file found.", "chat-failed-smart-paste-resolve-file", "Could not get valid edit stream for smart paste.", "chat-precompute-smart-paste", "open-memories-file", "main-panel-perform-action", "used-slash-action", "used-chat", "chat-clear-metadata", "augment-link", "Failed to open URL: ${He(n)}", "trigger-initial-orientation", "get-orientation-status", "execute-command", "Failed to execute command ${n}: ${i}", "toggle-collapse-unchanged-regions", "diffEditor.toggleCollapseUnchangedRegions", "Failed to execute diffEditor.toggleCollapseUnchangedRegions: ${n}", "workbench.action.openSettings", "augment.userGuidelines", "disable-github-copilot", "disable-codeium", "move-extension-aside", "hasMovedExtensionAside", "open-folder", "close-folder", "vscode.closeFolder", "grant-sync-permission", "main-panel-actions", "chat-autofix-state-update-request", "chat-autofix-suggestions-applied", "run-slash-command", "explain", "new-thread", "write-test", "document", "reset-agent-onboarding", "call-tool-response", "cancel-tool-run-response", "check-safe-response", "checkToolExistsResponse", "get-ide-state-node-response", "chat-agent-edit-list-has-updates", "chat-memory-has-updates", "chat-save-image-response", "chat-delete-image-response", "chat-load-image-response", "get-subscription-info-response", "Failed to get subscription info: ${String(n)}", "Hello World!", "Intentional error from service", "_vscode-augment.showSidebarChat", "Sorry, Augment ${e} encountered an unexpected error: ${He(t)}", "vscode-augment.chat.slash.fix", "Fix using Augment", "Quick Fix", "vscode-augment.chat.slash.explain", "Explain using Augment", "Explain", "vscode-augment.chat.slash.test", "Write test using Augment", "Write a Test", "vscode-augment.chat.slash.document", "Document", "Reset Agent Onboarding", "augment.resetAgentOnboarding", "memoriesFileOpenCount", "Run Agent Initial Orientation", "vscode-augment.runAgentInitialOrientation", "Workspace manager is not ready", "Agent checkpoint manager is not ready", "vscode-augment.signIn", "vscode-augment.signOut", "Autofix Command Output", "vscode-augment.autofixCommand", "AutofixCommand", "${o}:/${s.path}.right", "Diff - ${s.path}", "Enter the test command to run (e.g. `pytest`)", "New command", "Previous command", "Running command: ", "Checkpoint not found", "No candidate locations found", "Would you like to apply and test the changes?", "The tests has passed.", "Command failed. Keep changes anyway?", "return code != 0", "git diff ${r} --name-status -M", "Unexpected change type: modified with rename", "Unexpected change type: rename with no change", "git show ${r}:${d}", "Failed to execute git command", "Failed to execute git command: ${r}\nOutput: ${i.output}", "No workspace folders", "git merge-base HEAD ${s}", "git diff -U10 ${c}", "Failed to get git diff: ${u.output}", "Failed to parse git diff: ${k instanceof Error?k.message:String(k)}", "Failed to upload blobs: ${k instanceof Error?k.message:String(k)}", "No test command provided.", "The tests has passed, would you like to continue anyway?", "return code: 0", "No command output found.", "Requesting edit locations with requestId=${y}", "Requesting check command with requestId=${y}", "Requesting contain errors with requestId=${y}", "Command doesn't appear code-related. Continue anyway?", "\\u2705 Command check passed.", "Failed to check command: ${k instanceof Error?k.message:String(k)}", "Couldn't find errors in the output. Continue anyway?", "\\u2705 Contain errors check passed.", "Failed to check for errors: ${k instanceof Error?k.message:String(k)}", "Failed to get edit locations: ${k instanceof Error?k.message:String(k)}", "Found ${_.length} potential edit locations.", "Creating fix plan...", "Requesting create fix plan with requestId=${y}", "Failed to create fix plan: ${k instanceof Error?k.message:String(k)}", "Changes:\n${q.changes.map(k=>", "${q.fix_desc}\n\n${Y}", "Requesting apply file fix with requestId=${y}", "Do you confirm the the following fix?", "Generating patch...", "Failed to generate patch: ${k instanceof Error?k.message:String(k)}", "Sorry, no changes to apply.", "Close all diff views to continue.", "Failed to open diff views: ${k.message}", "Failed to apply changes: ${k instanceof Error?k.message:String(k)}", "showDiffInHover", "enableAutoApply", "nextEditUxMigrationStatus", "nextEdit.", "command:${r}?${encodeURIComponent(JSON.stringify([n,...i]))}", "groupId", "vscode-augment.next-edit.force", "vscode-augment.next-edit.update", "private", "_vscode-augment.next-edit.update.loading", "_vscode-augment.next-edit.update.disabled-no-changes", "_vscode-augment.next-edit.update.disabled-cached", "vscode-augment.next-edit.background.accept", "vscode-augment.next-edit.background.accept-all", "_vscode-augment.next-edit.background.accept-code-action", "vscode-augment.next-edit.background.reject", "vscode-augment.next-edit.background.reject-all", "vscode-augment.next-edit.background.dismiss", "vscode-augment.next-edit.background.next", "vscode-augment.next-edit.background.next-forward", "vscode-augment.next-edit.background.previous", "_vscode-augment.next-edit.background.open", "vscode-augment.next-edit.toggle-panel-horizontal-split", "vscode-augment.next-edit.learn-more", "_vscode-augment.next-edit.background.next-forward.disabled", "_vscode-augment.next-edit.background.previous.disabled", "vscode-augment.next-edit.open-panel", "command:augment-next-edit.focus", "_vscode-augment.next-edit.undo-accept-suggestion", "_vscode-augment.next-edit.toggle-hover-diff", "vscode-augment.next-edit.toggle-bg", "Turn Background Suggestions Off", "Turn Background Suggestions On", "Are you sure you want to disable Next Edit Suggestions?", "You can re-enable them in Settings > Augment > Enable Background Suggestions.", "Disable", "Go to Settings", "@ext:augment.vscode-augment nextEdit.enableBackgroundSuggestions", "vscode-augment.next-edit.toggle-all-highlights", "Turn Off All Line Highlights", "Turn On All Line Highlights", "vscode-augment.next-edit.enable-bg", "Enable Background Suggestions", "vscode-augment.next-edit.disable-bg", "Disable Background Suggestions", "Reset Next Edit Onboarding", "_vscode-augment.next-edit.reset-onboarding", "nextEditSuggestionSeen", "nextEditSuggestionAccepted", "nextEditKeybindingUsageCount", "vscode-augment.next-edit.settings", "@ext:augment.vscode-augment augment.nextEdit", "None", "VK_UNKNOWN", "Hyper", "Super", "FnLock", "Suspend", "Resume", "Turbo", "Sleep", "VK_SLEEP", "WakeUp", "KeyA", "VK_A", "KeyB", "VK_B", "KeyC", "VK_C", "KeyD", "VK_D", "KeyE", "VK_E", "KeyF", "VK_F", "KeyG", "VK_G", "KeyH", "VK_H", "KeyI", "VK_I", "KeyJ", "VK_J", "KeyK", "VK_K", "KeyL", "VK_L", "KeyM", "VK_M", "KeyN", "VK_N", "KeyO", "VK_O", "KeyP", "VK_P", "KeyQ", "VK_Q", "KeyR", "VK_R", "KeyS", "VK_S", "KeyT", "VK_T", "KeyU", "VK_U", "KeyV", "VK_V", "KeyW", "VK_W", "KeyX", "VK_X", "KeyY", "VK_Y", "KeyZ", "VK_Z", "Digit1", "VK_1", "Digit2", "VK_2", "Digit3", "VK_3", "Digit4", "VK_4", "Digit5", "VK_5", "Digit6", "VK_6", "Digit7", "VK_7", "Digit8", "VK_8", "Digit9", "VK_9", "Digit0", "VK_0", "Enter", "VK_RETURN", "Escape", "VK_ESCAPE", "Backspace", "VK_BACK", "VK_TAB", "Space", "VK_SPACE", "Minus", "VK_OEM_MINUS", "OEM_MINUS", "Equal", "VK_OEM_PLUS", "OEM_PLUS", "BracketLeft", "VK_OEM_4", "OEM_4", "BracketRight", "VK_OEM_6", "OEM_6", "Backslash", "VK_OEM_5", "OEM_5", "IntlHash", "Semicolon", "VK_OEM_1", "OEM_1", "Quote", "VK_OEM_7", "OEM_7", "Backquote", "VK_OEM_3", "OEM_3", "Comma", "VK_OEM_COMMA", "OEM_COMMA", "Period", "VK_OEM_PERIOD", "OEM_PERIOD", "Slash", "VK_OEM_2", "OEM_2", "CapsLock", "VK_CAPITAL", "VK_F1", "VK_F2", "VK_F3", "VK_F4", "VK_F5", "VK_F6", "VK_F7", "VK_F8", "VK_F9", "VK_F10", "VK_F11", "VK_F12", "PrintScreen", "ScrollLock", "VK_SCROLL", "Pause", "PauseBreak", "VK_PAUSE", "Insert", "VK_INSERT", "Home", "VK_HOME", "PageUp", "VK_PRIOR", "VK_DELETE", "VK_END", "PageDown", "VK_NEXT", "ArrowRight", "RightArrow", "VK_RIGHT", "Right", "ArrowLeft", "LeftArrow", "VK_LEFT", "Left", "ArrowDown", "DownArrow", "VK_DOWN", "Down", "ArrowUp", "UpArrow", "VK_UP", "NumLock", "VK_NUMLOCK", "NumpadDivide", "NumPad_Divide", "VK_DIVIDE", "NumpadMultiply", "NumPad_Multiply", "VK_MULTIPLY", "NumpadSubtract", "NumPad_Subtract", "VK_SUBTRACT", "NumpadAdd", "NumPad_Add", "VK_ADD", "NumpadEnter", "Numpad1", "NumPad1", "VK_NUMPAD1", "Numpad2", "NumPad2", "VK_NUMPAD2", "Numpad3", "NumPad3", "VK_NUMPAD3", "Numpad4", "NumPad4", "VK_NUMPAD4", "Numpad5", "NumPad5", "VK_NUMPAD5", "Numpad6", "NumPad6", "VK_NUMPAD6", "Numpad7", "NumPad7", "VK_NUMPAD7", "Numpad8", "NumPad8", "VK_NUMPAD8", "Numpad9", "NumPad9", "VK_NUMPAD9", "Numpad0", "NumPad0", "VK_NUMPAD0", "NumpadDecimal", "NumPad_Decimal", "VK_DECIMAL", "IntlBackslash", "OEM_102", "VK_OEM_102", "ContextMenu", "Power", "NumpadEqual", "VK_F13", "VK_F14", "VK_F15", "VK_F16", "VK_F17", "VK_F18", "VK_F19", "VK_F20", "VK_F21", "VK_F22", "VK_F23", "VK_F24", "Open", "Help", "Select", "Again", "Undo", "Copy", "Paste", "Find", "AudioVolumeMute", "VK_VOLUME_MUTE", "AudioVolumeUp", "VK_VOLUME_UP", "AudioVolumeDown", "VK_VOLUME_DOWN", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "NumPad_Separator", "VK_SEPARATOR", "IntlRo", "ABNT_C1", "VK_ABNT_C1", "KanaMode", "IntlYen", "Convert", "NonConvert", "Lang1", "Lang2", "Lang3", "Lang4", "Lang5", "Abort", "Props", "NumpadParenLeft", "NumpadParenRight", "NumpadBackspace", "NumpadMemoryStore", "NumpadMemoryRecall", "NumpadMemoryClear", "NumpadMemoryAdd", "NumpadMemorySubtract", "NumpadClear", "Clear", "VK_CLEAR", "NumpadClearEntry", "Ctrl", "VK_CONTROL", "Shift", "VK_SHIFT", "VK_MENU", "Meta", "VK_COMMAND", "ControlLeft", "VK_LCONTROL", "ShiftLeft", "VK_LSHIFT", "AltLeft", "VK_LMENU", "MetaLeft", "VK_LWIN", "ControlRight", "VK_RCONTROL", "ShiftRight", "VK_RSHIFT", "AltRight", "VK_RMENU", "MetaRight", "VK_RWIN", "BrightnessUp", "BrightnessDown", "MediaPlay", "MediaRecord", "MediaFastForward", "MediaRewind", "MediaTrackNext", "VK_MEDIA_NEXT_TRACK", "MediaTrackPrevious", "VK_MEDIA_PREV_TRACK", "MediaStop", "VK_MEDIA_STOP", "Eject", "MediaPlayPause", "VK_MEDIA_PLAY_PAUSE", "MediaSelect", "LaunchMediaPlayer", "VK_MEDIA_LAUNCH_MEDIA_SELECT", "LaunchMail", "VK_MEDIA_LAUNCH_MAIL", "LaunchApp2", "VK_MEDIA_LAUNCH_APP2", "LaunchApp1", "VK_MEDIA_LAUNCH_APP1", "SelectTask", "LaunchScreenSaver", "BrowserSearch", "VK_BROWSER_SEARCH", "BrowserHome", "VK_BROWSER_HOME", "BrowserBack", "VK_BROWSER_BACK", "BrowserForward", "VK_BROWSER_FORWARD", "BrowserStop", "VK_BROWSER_STOP", "BrowserRefresh", "VK_BROWSER_REFRESH", "BrowserFavorites", "VK_BROWSER_FAVORITES", "ZoomToggle", "MailReply", "MailForward", "MailSend", "KeyInComposition", "ABNT_C2", "VK_ABNT_C2", "OEM_8", "VK_OEM_8", "VK_KANA", "VK_HANGUL", "VK_JUNJA", "VK_FINAL", "VK_HANJA", "VK_KANJI", "VK_CONVERT", "VK_NONCONVERT", "VK_ACCEPT", "VK_MODECHANGE", "VK_SELECT", "VK_PRINT", "VK_EXECUTE", "VK_SNAPSHOT", "VK_HELP", "VK_APPS", "VK_PROCESSKEY", "VK_PACKET", "VK_DBE_SBCSCHAR", "VK_DBE_DBCSCHAR", "VK_ATTN", "VK_CRSEL", "VK_EXSEL", "VK_EREOF", "VK_PLAY", "VK_ZOOM", "VK_NONAME", "VK_PA1", "VK_OEM_CLEAR", "String representation missing for key code ${c} around scan code ${a}", "Illegal argument: ${e}", "Illegal argument", "CodeExpectedError", "K${t}${r}${n}${i}${this.keyCode}", "S${t}${r}${n}${i}${this.scanCode}", "chords", "keybindings file changed: ${s} ${o}", "userKeybindingInfo", "vscode-userdata", "KeybindingWatcher", "keybindings.json", "exthost", "Loading user keybindings", "error parsing keybinding ${r}", "error formatting keybinding, returning unformatted keybinding.", "Failed to parse '${r}': ${i.message}", "/Users/", "\\u2318", "\\u2303", "\\u2325", "\\u21E7", "\\u23CE", "\\u2191", "\\u2193", "\\u232B", "input1", "input2", "noop", "Cmd+Z", "Ctrl+Z", "redo", "Cmd+Shift+Z", "Ctrl+Y", "Clear Recent Editing History", "vscode-augment.clear-recent-editing-history", "CopySessionIdPanelCommand", "vscode-augment.copySessionId", "Copied session ID to clipboard", "Failed to copy session ID: ${r}", "Start Extension CPU Profile", "vscode-augment.cpu-profile.start", "End Extension CPU Profile", "vscode-augment.cpu-profile.stop", "$(sync) Enable workspace syncing", "vscode-augment.enable-workspace-syncing", "initializing", "disabled", "$(circle-slash) Disable workspace syncing", "vscode-augment.disable-workspace-syncing", "Show Extension Status", "vscode-augment.extensionStatus", "vscode-augment.generateCommitMessage", "vscode.git", "Cannot generate commit message: Failed to interact with git extension", "Cannot generate commit message: no repo", "Augment is generating...", "Cannot generate commit message: no workspace", "Cannot generate commit message: Failed to generate diff details", "Cannot generate commit message: no diff", "Server error: failed to generate commit message.", "vscode-augment.insertCompletion", "Waiting for completion...", "Failed to request a completion", "timeout", "cancelled", "Completion request cancelled", "No completions found", "editor.action.inlineSuggest.trigger", "Show Internal Context", "vscode-augment.showWorkspaceContext", "Augment Workspace Context.txt", "untitled", "no workspace manager", "Augment workspace context\n", "    ${l}: ${v} has recent chunks but is not open", "Repo root ${l}: ${f} paths (${p} open)", "    ${E} -> ${v}: open, recent chunks: ${_}", " (x ${_})", "    ${E} -> ${v}${w}", "unknown blob: ${l}", "Recent chunks", "    (no recent chunks)", "Chunk ${s+1} of ${r.length}", "    seq:      ${o.seq}", "    uploaded: ${o.uploaded}", "    repoRoot: ${o.repoRoot}", "    pathName: ${o.pathName}", "    blobName: ${o.blobName}", "    start:    ${o.origStart}", "    length:   ${o.origLength}", "======== chunk begin ========", "-------- chunk end ----------", "Clear Stored MCP Servers", "vscode-augment.clearMCPServers", "vscode-augment.manageAccountCommunity", "vscode-augment.manageAccountProfessional", "vscode-augment.manageAccountEnterprise", "https://app.augmentcode.com/account", "community", "professional", "enterprise", "Open Augment SSH Config", "vscode-augment.openSshConfig", "Error opening SSH config: ${String(r)}", "vscode-augment.settings", "@ext:augment.vscode-augment", "vscode-augment.keyboard-shortcuts", "workbench.action.openGlobalKeybindings", "vscode-augment.showAccountPage", "vscode-augment.showDocs", "https://docs.augmentcode.com", "history", "Augment History", "history.html", "instructions", "next-edit-suggestions", "history-config", "history-loaded", "history-initialize", "copy-request-id-to-clipboard", "Copied request ID to clipboard", "completion-rating", "Failed to submit feedback: ${i.message}", "completion-rating-done", "next-edit-rating", "next-edit-rating-done", "vscode-augment.showHistoryPanel", "vscode-augment.showAugmentCommands", "Augment Commands", "Show Remote Agents Panel", "vscode-augment.showRemoteAgentsPanel", "enableWorkspaceManagerUi", "WorkspaceUIModel", "ws-context-get-source-folders-request", "ws-context-get-children-request", "ws-context-source-folders-changed", "ws-context-folder-contents-changed", "ws-context-get-source-folders-response", "ws-context-get-children-response", "included", "excluded", "folder", "Extension received message: ${r.type}", "ws-context-add-more-source-folders", "Add Source Folder", "Failed to add source folder:", "One or more source folders could not be added:\n", "${s.path}: ${s.message}", "ws-context-remove-source-folder", "Failed to remove source folder:", "Failed to remove source folder ${r.data}:\n ${String(i)}", "ws-context-user-requested-refresh", "ToolConfigError", "Failed to parse tool config: ${t}", "ToolConfigParseError", ".cursor/rules", ".cursorrules", "<PERSON><PERSON><PERSON>", ".windsurf/rules", ".windsurf<PERSON><PERSON>", "Windsurf", ".github/copilot-instructions.md", "GitHub Copilot", ".clinerules", "Cline", ".roo/rules", ".roorules", "Roo Code", ".trae/rules", "<PERSON><PERSON>", "Augment Settings", "settings.html", "orientation-status-update", "augmentSettingsPanel", "SettingsWebviewPanel", "navigate-to-settings-section", "Unexpected error: ${He(r)}", "settings-panel-loaded", "tool-config-loaded", "tool-config-save", "tool-config-get-definitions", "tool-config-start-oauth", "tool-config-revoke-access", "get-stored-mcp-servers", "set-stored-mcp-servers", "execute-initial-orientation", "get-terminal-settings", "update-terminal-settings", "sign-out", "create-rule", "open-rule", "delete-rule", "import-file-request", "trigger-import-dialog-response", "import-directory-request", "auto-import-rules", "trigger-import-dialog", "tool-config-initialize", "Failed to parse tool config: ${He(a)}", "Error saving tool configuration: ${He(n)}", "tool-config-definitions-response", "Failed to open URL: ${i}", "Error opening URL: ${He(i)}", "tool-config-start-oauth-response", "Revoking access for remote tool: ${i.definition.name} (${s})", "API error revoking access: ${He(a)}", "Error revoking access: ${He(a)}", "Successfully revoked access for ${i.definition.name} (${s}).", "Tool ${i.definition.name} (${s}) has no access to revoke.", "Revoking access is not implemented for ${i.definition.name} (${s}).", "Tool not found: ${i.definition.name} (${s}).", "Failed to revoke access for ${i.definition.name} (${s}).", "Unknown status (${o.status}) when revoking access for ${i.definition.name} (${s}).", "Tool not found: ${r.toolId.hostName} ${r.toolId.toolId}", "Error revoking access: ${He(n)}", "Confirmation modal requested: ", "Error handling confirmation modal: ${He(n)}", "get-stored-mcp-servers-response", "Error getting storage value: ${He(r)}", "Error setting storage value: ${He(n)}", "Failed to post message to webview: ${He(n)}", "Failed to execute initial orientation: ${He(r)}", "Failed to get orientation status: ${He(r)}", "terminal-settings-response", "Failed to get terminal settings: ${He(r)}", "Failed to update terminal settings: ${He(n)}", "Failed to get rules list: ${He(r)}", "Enter a name for the new rule", "e.g. architecture.md", "Rule name cannot be empty", "create-rule-response", "Failed to create rule: ${He(r)}", "Failed to open rule: ${He(n)}", "Failed to open guidelines: ${He(n)}", "Are you sure you want to delete the rule \"${n}\"? This action cannot be undone.", "Rule \"${n}\" deleted successfully.", "Deletion of rule \"${n}\" cancelled by user.", "Failed to delete rule: ${He(n)}", "File not found: ${i}", "${Np.default.parse(r.filename).name.replace(\".\",\"\")}", "Successfully created rule from ${a}.", "Existing rule found for ${a}.", "Error reading directory ${r.filename}: ${o}", "Error importing file: ${i}", "Loaded ${i.length} existing rules from ${n}", "fulfilled", "Successfully created ${l} rules from ${r.directoryPath}, but found ${u} duplicate rules.", "Successfully created ${l} rules from ${r.directoryPath}.", "Found ${u} duplicate rules in ${r.directoryPath}, not creating any new rules.", "Error importing directory: ${i}", "Handling auto-import rules", "No workspace folder open to import from.", "Import existing rules from ${u} and ${d}", "Import existing rules from ${u}", "Import existing rules from ${d}", "No existing rules found in workspace.", "Select existing rules to import from.", "Auto-import rules completed, total imported: ${l}", "auto-import-rules-response", "Handling trigger import dialog", "No workspace root found for import dialog.", "No workspace folder open to import into.", "Import", "Select files or a folder to import as rules", "Selected URIs: ${i?.length||0}", "Import dialog cancelled by user.", "Error stating URI ${l.fsPath}: ${He(u)}", "The selected folder is not within the workspace, cannot import rules.", "Importing rules from directory: ${l}", "Import directory completed, total imported: ${d}", "Importing rules from file ${p}", "Import files completed, total imported: ${d}", "Invalid selection.", "Please select only a single folder, or multiple files.", "Please select either files or a folder, not both.", "Please select multiple files OR a single folder to import.", "Invalid selection for import: ${s.length} files, ${o.length} folders.", "Error in import dialog: ${n}", "An error occurred during import: ${n}", "Show Settings Panel", "vscode-augment.startNewChat", "Start chat command", "_vscode-augment.statusbarClick", "Turn Automatic Completions Off", "Turn Automatic Completions On", "vscode-augment.toggleAutomaticCompletionSetting", "$(sign-in) Sign In", "Completions", "Code Instruction", "Cha<PERSON>", "Next Edit Suggestions", "Debug", "$(sign-out) Sign Out", "context:\n    text: ${e.trigger<PERSON>haracter}\n    triggerKind: ${e.triggerKind}", "ln: ${t.line} ch: ${t.character} offset: ${e.offsetAt(t)}", "<undefined>", "start: ${TA(e,t.start)} -> end: ${TA(e,t.end)}", "${e}'${t}'", "CompletionItemsProvider", "python", "typescript", "javascript", "java", "rust", "csharp", "scss", "less", "sass", "json", "yaml", "swift", "kotlin", "objective-c", "perl", "scala", "groovy", "powershell", "Pop-Up Request - ${t.uri.toString()} ${TA(t,r)}${r5(\" \",i.triggerCharacter)}", "Returning no completions because trigger kind was a trigger character", "Automatic completions are disabled", "Failed to find completions in time", "`'<>]+/);return i?i[0]===n?n:i[0]+", "),[];if(!i)return this._logger.debug(", "),[]):s}};var Ste=V(require(", "));var RD=class extends Error{constructor(r){super(`Configured model ", " is not available`);this.modelName=r}};var n5=class extends Error{constructor(){super(", ")}},RA=class extends Error{constructor(t=", ",value:i})})}_logger=X(", ");let E=[],w=v[0];return w.skippedSuffix.includes(`\n`)&&(this._logger.debug(", "),w.<PERSON><PERSON><PERSON><PERSON>=", ",w.suffixReplacementText=", ",pathName:", ",blobName:", ",text:n.text,origStart:0,origLength:0,expectedBlobName:", ",n.skippedS<PERSON><PERSON>=", ")}var DA=V(require(", ");var s5=(i=>(i[i.high=0]=", ",i[i.medium=1]=", ",i[i.low=2]=", ",i[i.neutral=3]=", ",i))(s5||{}),Ite=Object.values(s5).filter(e=>typeof e==", ").sort();var zx={background:new DD.ThemeColor(", "),foreground:new DD.ThemeColor(", ")},Tte={background:new DD.ThemeColor(", ")},e5e={priority:3,tooltip:", ",icon:", "},t5e={priority:3,tooltip:", "},r5e={priority:0,tooltip:", ",colors:zx},n5e={priority:2,tooltip:", "},i5e={priority:0,tooltip:", ",colors:zx},s5e={priority:0,tooltip:", ",colors:zx},o5e={priority:2,tooltip:", "},a5e={priority:0,tooltip:", ",colors:zx},c5e={priority:0,tooltip:", ",colors:zx},l5e={priority:0,tooltip:", ",colors:zx},u5e={priority:2,tooltip:", "},d5e={priority:1,tooltip:", "},Rte={priority:0,tooltip:", ",colors:Tte},f5e={priority:0,tooltip:", ",colors:Tte},p5e={priority:1,tooltip:", "},h5e={priority:2,tooltip:", "},g5e={priority:0,tooltip:", ",colors:Tte},m5e={priority:1,tooltip:", "},A5e={priority:2,tooltip:", "},y5e={priority:0,tooltip:", ");_pendingCompletion;getPendingCompletion(r,n){if(!this._pendingCompletion){this._logger.verbose(", "&&(c=c+l,l=", "));var l5=class extends ne{_logger=X(", "),!1):this._checkIfForwardDeletion(t,n)?(this._logger.debug(", "&&Ag(!1)}))}_logger=X(", "),Oqe(),Ag(!1),[];if(Qqe(c),!c)return this._logger.debug(", "&&(t=new e(t,t+1)),this.start<t.stop&&this.stop>t.start}touches(t){return typeof t==", ")),Ire=V(require(", ")),Sg=V(require(", ")),dv=V(require(", ")),J5=V(require(", ")),vVe=require(", "),EVe=require(", ",{get:function(){return this},configurable:!0}),t=UeWvA,t.udLZv=t,r=4;break;case 4:r=typeof udLZv>", "?3:9;break;case 3:throw", ")};e=1;break}}();U.t2=function(){return typeof U[225456].H5GvTML==", ",i[21]=", ",i[74]=", ",i[86]=", ",i[38]=", ",i[11]=", ",i[92]=", ",i[3]=", ",i[8]=", ",i[5]=", ",i[6]=", ",i[88],i[48],i[29]),n=248;break;case 80:i[68]=", ",i[71]=", ",i[80]=", ",n=103;break;case 20:i[6]=", ",i[1]=", ",i[45]=", ",i[51]=", ",i[19]=", ",i[56]=", ",n=99;break;case 210:s(t,i[26],i[29],i[84]),n=252;break;case 241:s(a,", ",i[88],i[17],i[29]),n=236;break;case 238:s(l,", ",i[88],i[63],i[29]),n=237;break;case 235:s(t,i[75],i[29],i[18],i[29]),n=234;break;case 251:s(a,", ",i[88],i[42],i[29]),n=250;break;case 68:i[97]=", ",i[97]=", ",i[89]=", ",n=89;break;case 229:i[635]=i[3],i[635]+=i[4],i[635]+=i[8],i[930]=i[44],n=225;break;case 99:i[33]=", ",i[33]=", ",i[47]=", ",n=95;break;case 114:i[49]=", ",i[22]=", ",i[14]=", ",i[34]=", ",n=110;break;case 250:s(l,", ",i[88],i[59],i[29]),n=249;break;case 61:i[73]=", ",i[73]=", ",i[85]=", ",i[10]=", ",i[16]=", ",i[81]=", ",n=45;break;case 57:i[85]=", ",i[36]=", ",n=76;break;case 244:s(l,", ",i[88],i[35],i[29]),n=243;break;case 72:i[53]=", ",i[53]=", ",i[67]=", ",n=68;break;case 252:s(t,i[41],i[29],i[60]),n=251;break;case 110:i[34]=", ",i[31]=", ",i[72]=", ",i[91]=", ",n=134;break;case 76:i[52]=", ",i[52]=", ",i[69]=", ",i[61]=", ",i[24]=", ",i[27]=", ",i[55]=", ",n=37;break;case 85:i[74]=", ",i[30]=", ",i[68]=", ",n=80;break;case 134:i[87]=", ",i[98]=", ",i[76]=", ",i[88]=1,i[70]=", ",n=129;break;case 248:s(t,", ",i[29],i[46],i[29]),n=247;break;case 3:i[7]=", ",i[4]=", ",i[9]=", ",n=6;break;case 95:i[58]=", ",i[78]=", ",i[23]=", ",n=118;break;case 27:i[45]=", ",i[57]=", ",i[25]=", ",i[82]=", ",n=21;break;case 29:i[43]=", ",i[83]=", ",i[43]=", ",i[39]=", ",n=42;break;case 45:i[16]=", ",i[95]=", ",i[40]=", ",i[29],i[12],i[29]),n=246;break;case 236:s(r,", ",i[88],i[28],i[29]),n=239;break;case 242:s(c,", ",i[29],i[15],i[29]),n=241;break;case 21:i[20]=", ",i[20]=", ",i[44]=", ",i[79]=", ",i[50]=", ",n=29;break;case 2:var i=[arguments];i[2]=", ",i[2]=", ",i[7]=", ",n=3;break;case 118:i[62]=", ",i[62]=", ",i[90]=", ",n=114;break;case 175:i[26]+=i[81],i[26]+=i[61],i[65]=i[43],i[65]+=i[30],n=171;break;case 246:s(a,", ",i[29],i[13],i[29]),n=245;break;case 243:s(t,", ",v[9]=", ",v[7]=", ",T[9]=", ",T[7]=", ":1%3C%1BA", ":4*GN%0B%207%20GC#-&6%13D7***%13V;=7%20XL,(,7XD6", "cGD,*,(%7DD1&e&TK-%20*&TI+4&cYL,;;cVD.-6)TQ", "0,ZKdh%207LU6!e*F%03-=%11%20Y@#=&c%5DJ/+%07,G@!:,7Ll,!e1Zs", ".%20%13W#%20", "%20%18H#-+,%5B@o", "c%01F!we%1AA@::%06+VJ&+1cXD!h66Wa", "e1%5CH", "%1D&7%5CD.=e#YJ-%3Ce,%5BQ", "%3C-$X@d/6!%5CJ%06+5,V@%0B*0c%60V", ",6%13B2;%0A+SJd;7,Y%030+%22!sL.+%10%3C%5BFd-1%20TQ", ".%20ZP6h*!jW1/m5@Gd:&)PH", "%22&$F@%06/7%20%13C1%1E%221%5D%031:,7TB", "%20%20%13H#%206#TF6;1%20G%03%20/0%20WJ#%3C", ",G%03!&%226FL1%07-#Z%03/!", "%1B1,eD6&e6FM%12;!)%5CF%09+:cFL8+et%04%10zh%22)Y%037;*!%13C7%22%25,YI", "-cXD!&*+Pl&h&%7C%0DCd::5P%035", "h/%20%5BB6&e3PW1", "ZD0*%0A+SJd=3)%5CQd%25&%3CF%03uwv$%13U#%3C0%20%13H", "6*6AV%117-&%13C./$6%13V*/qp%03%034", "01Fv;%20%20cEJ5+16%5D@.%22cgr@6c%00,Xl,=7$%5BF", "-7e%18%60:%3E%22+Qu0!3%20GQ;n%0F$FQ%00!,1%60U%16", "%13D7***q@4", "=7%0CQ%03!/-*%5BL!//,O@%03%3C1$L%03%25", "&#039;", "shift", "delete", "escape", "semicolon", "backspace", "ctrl", "control", "$(augment-kb-${r})", "augment-kb-${r}", "&nbsp;", "&nbsp;&nbsp;", "[${n.toPrettyString(t.getSimplifiedPlatform())}]", "\"Augment.vscode-augment/augment-kb-icon-font.woff\"", "bottom", "DecorationManager", "Got an undefined decoration type.", "insertion", "deletion", "EditSuggestion(${this.qualifiedPathName.relPath}:${this.lineRange.toString()})", "${s} 0 0 0", "0 0 ${s} 0", "solid", "0.2em", "0.1em", "upper", "lower", "nextedit-addition-selected-light.svg", "nextedit-addition-selected-dark.svg", "nextedit-addition-inbetween-selected-light.svg", "nextedit-addition-inbetween-selected-dark.svg", "nextedit-deletion-selected-light.svg", "nextedit-deletion-selected-dark.svg", "nextedit-change-selected-light.svg", "nextedit-change-selected-dark.svg", "nextedit-addition-light.svg", "nextedit-addition-dark.svg", "nextedit-addition-inbetween-light.svg", "nextedit-addition-inbetween-dark.svg", "nextedit-deletion-light.svg", "nextedit-deletion-dark.svg", "nextedit-change-light.svg", "nextedit-change-dark.svg", "nextedit-applied-light.svg", "nextedit-applied-dark.svg", "nextedit-applied-inbetween-light.svg", "nextedit-applied-inbetween-dark.svg", "bg-next-edit-gray-line.svg", "bg-next-edit-gray-hook.svg", "rgb(from var(--vscode-diffEditor-insertedTextBackground) r g b / calc(alpha * 1.8))", "rgb(from var(--vscode-diffEditor-insertedTextBackground) r g b / calc(alpha * 1.3))", "diffEditor.insertedLineBackground", "editorOverviewRuler.addedForeground", "rgb(from var(--vscode-diffEditor-removedTextBackground) r g b / calc(alpha * 2))", "var(--vscode-diffEditor-removedLineBackground)", "rgb(from var(--vscode-diffEditor-removedLineBackground) r g b / calc(alpha * 0.5))", "editorOverviewRuler.deletedForeground", "rgb(from var(--vscode-editorOverviewRuler-modifiedForeground) r g b / calc(alpha * 0.5))", "rgb(from var(--vscode-editorOverviewRuler-modifiedForeground) r g b / calc(alpha * 0.35))", "rgb(from var(--vscode-editorOverviewRuler-modifiedForeground) r g b / calc(alpha * 0.2))", "editorOverviewRuler.modifiedForeground", "#27E14966", "#BCF0C9", "#82CC0266", "#4B543E", "#FF272766", "#F4A2A6", "#412C2D", "#FFE30966", "#F4EEB6", "#EDBD0099", "#433C1E", "color-mix(in srgb, rgb(0, 0, 0) 50%, var(--vscode-editor-background))", "color-mix(in srgb, rgb(0,0,0) 15%, var(--vscode-editor-background))", "color-mix(in srgb, rgb(255, 255, 255) 90%, var(--vscode-editor-background))", "color-mix(in srgb, rgb(255, 255, 255) 25%, var(--vscode-editor-background))", "next-edit", "active", "light", "dark", "insertionInBetween", "inactive", "applied", "appliedInBetween", "grayline", "cover", "grayhook", "0 0.2em 0 0", "0 0 0 0.2em", "0 0 0 0.4em", "0 0.4em 0 0", "var(--vscode-editor-background)", "color-mix(in srgb, ${f.dark.after.color} 50%, ${f.dark.after.backgroundColor})", "#87BFFF80", "#87BFFF33", "#87BFFF", "#9ECBFF80", "#9ECBFF1A", "#9ECBFF", "0.4em", "rgb(from var(--vscode-diffEditor-insertedLineBackground) r g b / 1)", "color-mix(in srgb, #87BFFF 33%, var(--vscode-editor-background))", "color-mix(in srgb, #9ECBFF 10%, var(--vscode-editor-background))", "0.3em", "rgb(from var(--vscode-diffEditor-removedLineBackground) r g b / calc(alpha * 1.3))", "BackgroundDecorationManager", "vscode-augment.nextEdit.linesWithGutterIconActions", "vscode-augment.nextEdit.filesWithGutterIconActions", "${_}", "suggestion-hint-shown", "suggestion-visibly-shown", "${p>0?\"\\u2191\":\"\\u2193\"}${Math.abs(p)} lines: ", "${A}${l}", "suggestion-offset-text-shown", "${f} ${l}", "suggestion-bottom-text-shown", "in file: ", "${l}${_d(a.qualifiedPathName.relPath,e._maxGrayTextChars-Ru(l),!0)}", "\\u21AA ", "${l}${d}: ${f}", "suggestion-global-bottom-text-shown", "0 ${-v}ch 0 ${-v}ch", "${\"\\xA0\".repeat(v)}", "0 0 0.3lh 0", "0 -${A+1}ch 0 0", "${\"\\xA0\".repeat(A)}", "0.2em 1ch 0.15em 1ch", "\\uE901", "\"Augment.vscode-augment/augment-icon-font.woff\"", "0 1ch 0 0", "0 0 0 1ch", "0.2em 1ch 0.15em 0", "0 0.3em 0.3em 0", "Augment: Accept suggestion", "code-action-shown", "markdown-html-utils", "1.92.0", "&dollar;($1)", "Untrimmed whitespace", "No common leading whitespace for line: ${t}", "updated", "<strong>", "</strong>", "background-color:var(--vscode-editor-background);", "border-radius:5px;", "\n${v}<pre><span style=\"${_}\">${A}${f}${w}</span></pre>${E}\n", "1.97.0", "infinite loop in groupLines", "#00000000", "<span class=\"codicon codicon-blank\"></span>", "<span class=\"codicon codicon-diff-remove\"></span>", "<span class=\"codicon codicon-diff-insert\"></span>", "<span style=\"color:${r.lineNumberColor};\">${Cw(\" \",2*l+1)}...(MORE CHANGES)...</span>", "background-color:${r.originalLineColor};", "background-color:${r.updatedLineColor};", "background-color:${r.originalTextColor};", "background-color:${r.updatedTextColor};", "No common leading whitespace for span: ${o.text}", "<span style=\"${a}\">${u}</span>${d?", "$(augment-icon-simple)", "&nbsp;&nbsp;&nbsp;&nbsp;", "${u}${YVe(t[l])}", "<br/>${r}", "${s}&nbsp;&nbsp;&nbsp;&nbsp;${o}<br/>${e}${a}", "<a href=\"${e.href}\" title=\"${e.tooltip}\">${e.text}${e.keybindingIcons?", ":\"\"}</a>", "BackgroundNextEditsHoverProvider", "#FF000066", "var(--vscode-diffEditor-insertedLineBackground)", "#9CCC2C66", "var(--vscode-editorGhostText-foreground)", "hover-shown", "Error in hover provider: ${a.message}, requestId: ${s}", "error-hover-error", "Error rendering hover. requestId: ${s}", "${y}${b?\"&nbsp;\"+b:\"\"}", "Reject", "Reject Suggestion", "$(layout-panel)", "Open Suggestions Panel", "$(diff-single)", "<PERSON><PERSON>", "Show Diff", "$(gear)", "Open Settings", "Learn More", "Apply Suggestion", "Undo Suggestion", "suggestion-truncated-in-hover", "editor.action.showHover", "noAutoFocus", "1.97.0-insider", "editor.action.hideHover", "editorS<PERSON>roll", "down", "vscode-augment.nextEdit.canNextSmart", "vscode-augment.nextEdit.canNext", "vscode-augment.nextEdit.canPrevious", "vscode-augment.nextEdit.canAccept", "vscode-augment.nextEdit.canReject", "vscode-augment.nextEdit.canDismiss", "vscode-augment.nextEdit.canAcceptCodeAction", "vscode-augment.nextEdit.canAcceptAll", "vscode-augment.nextEdit.canRejectAll", "vscode-augment.nextEdit.canUndoAcceptSuggestion", "FORCED", "state-transitioned-to-no-suggestions", "state-transitioned-to-hinting", "state-transitioned-to-before-preview", "state-transitioned-to-after-preview", "state-transitioned-to-animating", "EditorNextEdits", "editor", "preview-decoration-dismissed", "reverse-decoration-dismissed", "undid-accepted-suggestion", "cursor-inside-suggestion", "No Next Edit to accept.", "error-no-suggestion-to-accept", "error-accept-suggestion-wrong-document", "Error waiting for suggestion to be accepted.", "No Next Edits to accept.", "accept-all-in-file", "accept-all", "reject-all", "No Next Edits to reject.", "reject-all-in-file", "No more suggestions right now.", "goto-hinting-triggered-from", "goto-hinting-triggered-to", "Could not goto hinting.", "next-triggered-from", "previous-triggered-from", "next-triggered-to", "previous-triggered-to", "Finishing animation instead of opening suggestion.", "undo", "undo-accept", "No Next Edits to undo.", "undo-all-in-file", "Clearing inline completion before opening next edit suggestion.", "editor.action.inlineSuggest.hide", "workbench.action.focusActiveEditorGroup", "suggestion-global-offset-text-triggered", "Suggestion for ${r.qualifiedPathName.relPath} is no longer relevant.", "Unable to go to suggestion in ${r.qualifiedPathName.absPath}.", "Trying to move to a line that doesn't exist.", "error-moving-to-line-that-doesnt-exist", "Tried to open stale suggestion. ${r.result.suggestionId}", "Unable to open suggestion in ${r.qualifiedPathName.absPath}.", "suggestion-opened", "No suggestion found for ${r.toString()} at line ${n}.", "No suggestion found.", "error-no-suggestion-found", "extension.vim_escape", "extension.vim_insert", "WORKSPACE", ".next-edit-results.json5", "mock-suggestion-${o.blobN<PERSON>}-${s++}", "queryNextEditStream", "Skipping Next Edit with cancelled token.", "Skipping Next Edit with too much context.", "Skipping Next Edit with no changes.", "[${e.requestId}] Starting request for ${g?.relPath} (mode=${e.mode}, scope=${e.scope}).", "[${e.requestId}] Skipping next edit with cancelled token.", "[${e.requestId}/${E.result.suggestionId}]", "${_} Found ${E.unknownBlobNames.length} unknown blobs.", "${_} Checkpoint was not found.", "${_} Cancelled by the client.", "${_} Response path ${E.result.path} has no document.", "error-no-document-for-response", "${_} Response path ${T.relPath} does not exist.", "error-response-file-is-deleted", "${_} Response was invalidated by pending edits.", "${_} Response was not line-aligned ${RVe(L)}.", "${_} Converting char range ${E.result.charStart}-${E.result.charEnd} to ${H}-${q}.", "${_} Updated char range to ${Y?.start}-${Y?.stop}.", "${_} The bad line is: \"${w.lineAt(L.end.character!==0?L.end.line:L.start.line).text}\".", "error-response-not-line-aligned", "error-response-not-line-aligned-for-current-file", "${_} Code in buffer doesn't match code in response.", "${_} Buffer code: \"${w.getText(L)}\", response code: \"${E.result.existingCode}\".", "error-code-in-buffer-doesnt-match-code-in-response", "error-code-in-buffer-doesnt-match-code-in-response-for-current-file", "${_} Returning ${ce.changeType} suggestion for ${T.relPath}@${ce.lineRange.toString()}.", "[${e.requestId}] Request completed.", "[${e.requestId}] Cancelled by the server.", "[${e.requestId}] Next edit failed: ${v}.", "error-api-error", "WARNING", "INFORMATION", "HINT", "GlobalNextEdits", "vscode-augment.nextEdit.global.updating", "global-mode-refreshed", "FOREGROUND", "Global next edit failed: ${a}", "error-global-mode-error", "global-mode-canceled", "vscode-augment.nextEdit.global.canUpdate", "vscode-augment.nextEdit.global.updateCached", "vscode-augment.nextEdit.loading", "inflight", "NextEditRequestManager", "ready", "Skipping ${n}/${i} request because it is subsumed by inflight request ${c.id}.", "${r?.relPath}@${a.selection?.toString()}", "Skipping ${n}/${i} request at ${u} because it is subsumed by ${l.id}, which was recently completed.", "${r?.relPath}@${s?.toString()}", "Starting enqueuing ${n}/${i} request ${l.id} at ${c}.", "Clearing requests for foreground request @ ${c}.", "Clearing requests for background request @ ${c}.", "Clearing background workspace requests for background file request @ ${c}.", "Cancelling inflight request ${this._inflightRequest.id}.", "Waiting for inflight request to complete.", "Waiting for a request to be enqueued.", "Starting to process ${r.id} after ${i} ms.", "[${p.id}] queued for ${H} ms.", "next_edit_bg_stream_preprocessing_latency_ms", "next_edit_bg_stream_finish_latency_ms", "next_edit_bg_stream_partial_latency_ms", "next_edit_bg_stream_cancel_latency_ms", "next_edit_bg_stream_error_latency_ms", "next_edit_bg_first_change_latency_ms", "next_edit_bg_sufficient_noops_latency_ms", "[${f}] Next edit failed: ${v}.", "No more pending requests.", "Request ${f} failed with status: ${A}.", "CURSOR", "SuggestionManagerImpl", "Clearing ${s-this._rejectedSuggestions.length} old rejections.", "Dropping ${s-r.length} suggestions that overlap with rejected suggestions.", "nonempty-suggestion-added", "nonempty-suggestion-cleared", "Filtering out ${this._rejectedSuggestions.length-n.length} expired rejections.", "Rejecting ${r.length} suggestions.", "nonempty-suggestion-redone", "nonempty-suggestion-accepted", "nonempty-suggestion-invalidated", "nonempty-suggestion-undone", "nonempty-suggestion-dropped", "nonempty-suggestion-becomes-stale", "Found intersecting suggestions, ${i.toString()} and ${o.toString()}", "error-intersecting-suggestions", "0 0 0 ${s}ch", "0 0.65em 0 0.5em", "ResolveFileService", "Could not read file: ${r}", "read-file-request", "read-file-response", "    ", "================\n", "<null>", "\"${t}\"", ">>>>>>>>>>>>>>>> ", "|<---- (ends here)", "<<<<<<<<<<<<<<<< ", "\"${r}\"", "vscode-augment.PrimaryStatusBarItem", "${t.icon} ${r}", "REMOVE_OLDEST", "REJECT_NEW_ITEMS", "SimpleQueueProcessor", "Unhandled error while processing task: ${t.message} ${t.stack}", "Unhandled error while processing task: ${t}", "FileUploader#BlobStatusExecutor", "[WARN] retryBlobNames: missing start time for ${u}", "FindMissingProcess started: for [${r.size}] items", "[ERROR] FindMissingProcess failed: for [${r.size}] items", "FindMissingProcess found unknown: for [${i.unknownBlobNames.length}] items", "FindMissingProcess found nonindexed: for [${i.nonindexedBlobNames.length}] items", "FindMissingProcess found not missing: for [${s.length}] items", "FileUploader#UploadExecutor", "Upload started [${n}]: for [${r.length}] items", "Upload complete [${n}]: for [${i.blobNames.length} / ${r.length}] items", "[WARN]Upload blob name mismatch: ${c} -> ${l}", "[WARN] Scheduling for retry [${s}] items", "[WARN] UploadExecutor: skipping upload for ${o.path} because it is too large", "UploadExecutor: no items to upload", "[ERROR] UploadExecutor failed: for [${n.length}] items. Caused by: ${o.message} ${o.stack}", "[ERROR] UploadExecutor failed: for [${s.blobName}]", "[ERROR] Upload failed: ${s.path}", "BlobUploaderImpl", "blobNameCalculator returned undefined for ${r.path}", "upload enqueued: ${r.path}. total: ${this._handledItems.size}", "master", "GitCommitIndexer", "Already indexing commits, skipping", "No workspace folders found, skipping commit indexing", "Error indexing commits: ${r.message}", "No commits to checkpoint", "Created commit checkpoint with ID: ${this._checkpointId}", "Error creating commit checkpoint: ${n.message}", "No VCS found in folder ${r.name}, skipping commit indexing", "Indexing commits in folder ${r.name}", "Could not determine any branch in ${r.name}, skipping commit indexing", "Using branch '${s}' for indexing in ${r.name}", "No commits found on branch ${s} in ${r.name}", "Found ${a.length} commits on branch ${s} in ${r.name}", "All commits are already indexed in ${r.name}", "Uploading ${l.length} commits in ${r.name}", "Enqueuing ${l.length} commits for upload in ${r.name}", "git://commit/${u.commit.hash}", "Started upload for ${l.length} commits in ${r.name}", "Finished indexing commits in ${r.name}", "Error indexing commits in folder ${r.name}: ${n.message}", "refs/remotes/origin/HEAD", "refs/remotes/origin/", "refs/heads/${n}", "%H%n%an%n%ae%n%at%n%s%n%b", "---COMMIT---", "Fetching batch of ${u} commits (skip=${c})", "${s}${o}", "-n${u} --skip=${c}", "${o}\n", "Retrieved ${p} commits in this batch, total: ${a.length}", "diff --git ", "git://commit/${r.hash}", "next-edit-suggestions.html", "next-edit-suggestions-changed", "next-edit-preview-active", "next-edit-dismiss", "next-edit-next-suggestion-changed", "panel-created", "NextEditSuggestionsPanel", "next-edit-suggestions-action", "acceptAllInFile", "rejectAllInFile", "undoAllInFile", "Unknown action message: ", "next-edit-loaded", "panel-opened", "next-edit-open-suggestion", "panel-suggestion-clicked", "next-edit-refresh-started", "next-edit-refresh-finished", "next-edit-cancel", "next-edit-active-suggestion", "ToolConfigStore", "toolsConfiguration", "Failed to load tool configurations: ${He(t)}", "Failed to save tool configurations: ${He(r)}", "mcpServers", "Failed to load MCP servers: ${He(t)}", "Failed to save MCP servers: ${He(r)}", "Failed to update sidecar MCP servers: ${He(t)}", "terminalSettings", "Failed to load terminal settings: ${He(t)}", "Failed to save terminal settings: ${He(r)}", "main-panel.html", "vscode-augment.mainPanel.app", "main-panel-loaded", "main-panel-display-app", "MainPanelWebviewProvider", "Disposing of main panel webview view", "SingletonExecutor has been disposed", "external-source-folders:original", "workspaceMessageStates", "longRunning", "running", "Syncing enabled state not initialized", "vscode-augment.syncingEnabledState", "Initial syncing permission", "syncingPermission.**********", "SyncingPermissionTracker", "Permission to sync folder ${r} unknown: no permission information recorded", "Permission to sync folder ${r} denied at ${i}", "denied", "Permission to sync folder ${r} granted at ${s}; type = ${i.type}", "granted", "Permission to sync folder ${r} unknown: no current permission for folder", "implicit", "explicit", "Updating syncing permission", "${r}: undefined", "${r}: syncing permission denied for workspace at ${s}", "\n    ${s.sourceFolder} (${s.type}) at ${o}", "${r}: syncing permission granted for workspace. Folders:${i}", "file-edit-events.json", "FileEditEventsStore", "Using [${t.directory}] to store events", "Saving ${t.length} events to ${this._storeFile}", "Loading events from ${this._storeFile}", "File ${this._storeFile} does not exist. Not loading events.", "Version mismatch: ${r.version} !== ${this._version}. Not loading events from ${this._storeFile}", "Loaded ${r.events.length} events from ${this._storeFile}", "Failed to load events from ${this._storeFile}", "unexpected empty block: s1=${e}, s2=${t}", "ADDED", "REMOVED", "FileEditEventsQueue", "Removed ${n-this._queue.numEvents} events prior to ${t}", "FileEditEventsWatcher[${this._folderName}]", "no known text for [${r}]", "Ignoring event for ${n} because it is too large", "Updating last known text for ${n} - based on empty event", "Ignoring event for ${n} - no content changes", "Ignoring event for ${r.relPath} because it is too large", "Adding last known text for ${r.relPath}. size before = ${this._lastKnownText.size}", "Removing last known text for ${r.relPath}. size before = ${this._lastKnownText.size}", "Deleting events for ${r.relPath}", "Renaming events for file [${r}] to [${n}]", "Renaming events for file/folder [${r.oldRelPath}] to [${r.newRelPath}]", "Disposing FileEditEventsWatcher", "FileEditManager", "Listening to events", "Stash changed for repo ${r.repoId}", "Head changed for repo ${r.repoId}", "Tracking folder ${r} with store at ${i.directory}", "Disposing FileEditManager", "Tracked", "Tracked (${this.ignoreSourceName})", "Not tracked (${this.ignoreSourceName})", "candidatePath \"${t}\" is not below ignore file's parent \"${r.dirName}\"", "default Augment rules", "*.pem", "*.key", "*.pfx", "*.p12", "*.jks", "*.keystore", "*.pkcs12", "*.crt", "*.cer", "id_rsa", "id_ed25519", "id_ecdsa", "id_dsa", ".env", "total", "${this.name}:\n", "  - ${r}: ${n} ms", "${this.name}:", "  - ${s}: ${o.value}", "  - timing stats:", "    - ${s}: ${o.value} ms", "PathIterator[${this._name}]: startUri ${this._name} must contain an absolute pathname", "PathIterator[${this._name}]: rootUri ${n.toString()} must contain an absolute pathname", "Created PathIterator for startUri ${this._startUri.fsPath}, rootUri ${this._rootUri.fsPath}", "Path metrics", "PathIterator", "directories emitted", "files emitted", "other paths emitted", "total paths emitted", "readDir", "yield", "Unsupported file extension (${this.extension})", "Absolute path ${r} passed to PathFilter", "Too-deep or malformed directory name ${r}", "FileUploaderImpl", "blobNameCalculator returned undefined for ${r}", "Upload started: path [${r.path}] with blob name [${r.blobName}]", "Upload complete: path [${r.path}] with blob name [${r.blobName}]", "Failed upload for [${r.path}]. Caused by: ${n.stack}.", "^R\\\\d{3}$", "Unknown diff change type [${e}]", "refs/remotes/origin", "${t}/HEAD", "Could not get commit changes for commit ${i}", "${r}:${t}", "Could not find file ${t} in commit ${r}", "${r}^", "VCSRepoWatcher[${r}]", "Handling head changes", "Error collecting commit changes", "Method not implemented.", "Handling file change [${r.relPath}]", "disk", "Unknown file change origin ${JSON.stringify(r)}", "Collecting disk changes", "VCSWatcher", "only git is supported for now", "startTracking folderId ${r}", "folderId ${r} is already being tracked", "stopTracking folderId ${t}", "Disposing VCSWatcher", "Registering for events", "Head changed for repo ${t.repoId}", "Error handling head change: ${r.message??\"\"} ${r.stack}", "Error handling head change: ${r}", "OpenFileManagerProxy", "notebook", "[WARN] getTrackedPaths in new but not in old [${t}]\n[${JSON.stringify(i)}]", "[WARN] getTrackedPaths in old but not in new [${t}]\n[${JSON.stringify(s)}]", "Record with primary key ${r} already exists.", "BlobStatusStore", "blobName", "Path [${t}] is embargoed", "indexed", "No indexed blob found for ${t}", "Multiple indexed blobs found for ${t}", "uploaded", "[ERROR] Failed to find record for ${t}", "[WARN] Blob ${t} is already indexed", "Blob ${t} is indexed but there is a newer blob to upload for ${n.pathName}", "Path [${t}] is already embargoed", "Embargoing path [${t}]", "OpenDocumentSnapshotCache", "[WARN] no known last text for path [${t}]. initializing.", "OpenFileManagerV2", "focus loss", "file change", "Blob ${n} indexed", "[WARN] Blob ${n} was indexed but not tracked. Ignoring.", "Blob ${n} failed to upload", "Handling ${r.expectedToActualBlobNameMap.size} uploaded blobs", "Blob ${n} was uploaded but not tracked. Ignoring.", "[WARN] Blob name mismatch. Expected ${n} but got ${i}.", "[WARN] Blob ${n} was uploaded but folder ${s} is not tracked. Ignoring.", "[${o}] Uploading [${i}] because [${s}]", "[${this._folderResources.get(r)?.workspaceName}] Stopping tracking [${n}]", "Source folder [${r.folderId}] is not open", "TODO [${s}] Ignoring notebook document ${r.relPath}", "[WARN] Failed to calculate blob name for ${r.relPath}", "new file", "[INFO] Blob name mismatch. Expected ${n} but got ${a}.", "[${a}] Re-uploading ${i} for ${n} in ${r}", "missing blob", "[${n}] Handling closed document ${r.relPath}", "Ignoring change event for ${r.relPath} because folder is not tracked", "[${i}] Ignoring notebook document ${r.relPath}", "[${i}] Ignoring change event for ${r.relPath} because folder is not tracked", "large change", "WorkQueueItemFailed: ${i}, retry = ${n}", "Cannot dequeue from empty queue", "processOne", "processBatch", "Invalid processor type", "WorkQueue[${t}]", "QueueStatusChanged", "ItemFailed", "item succeeded; retries = ${n}", "item failed, not retrying; retries = ${n}", "item failed, retrying in ${r} ms; retries = ${n}", "retrying", "BlobsCheckpointManager", "checkpoint", "BlobsCheckpointManager created. checkpointThreshold: ${this._checkpointThreshold}", "derefBlob: blob ${t} not found in checkpoint or toAdd", "derefFromCheckpoint: blob ${t} has reference count ${r}. In toRemove? ${this._toRemove.has(t)}", "expandBlobs: checkpointId mismatch: ${t.checkpointId} != ${this._checkpointId}", "checkpointId mismatch: ${t.checkpointId} vs ${r.checkpointId}", "addedBlobs mismatch: -${s.length}/+${o.length}", "left-added: ${s.slice(0,5).join(\",\")}", "right-added: ${o.slice(0,5).join(\",\")}", "deletedBlobs mismatch: -${s.length}/+${o.length}", "left-deleted: ${s.slice(0,5).join(\",\")}", "right-deleted: ${o.slice(0,5).join(\",\")}", "notifyBlobChange ${t}: ${r} to ${n}", "blob with 0 references was not found in toRemove: ${t}", "blob in toRemove was not found in checkpoint: ${t}", "Begin checkpoint of working set into ${n}", "add ${i.length} blobs, remove ${s.length} blobs into ${n}", "${a}", "{initial}", "checkpoint-blobs from ${l} failed with invalid argument: ${c}. Recreating checkpoint.", "checkpoint-blobs failed with error: ${c}.", "checkpointId ${n} advanced to ${o.newCheckpointId}", "In _checkpoint: deleted blob ${a} not found in checkpoint", "starting a new round of checkpointing due to size ${this._toAdd.size} + ${this._toRemove.size}", "queue checkpoint", "  ${s}", "  ...", "DiskFileManager[${r}]", "Rejecting requested probe batch size of ${o} (min = ${e.minProbeBatchSize})", "Rejecting requested probe batch size of ${o} (max = ${e.maxProbeBatchSize})", "Not a file", "File not readable", "Binary file", "Upload failed", "File metrics", "paths accepted", "paths not accessible", "not plain files", "large files", "blob name calculation fails", "encoding errors", "mtime cache hits", "mtime cache misses", "probe batches", "blob names probed", "files read", "blobs uploaded", "ingestPath", "probe", "stat", "read", "upload", "File too large (${r} > ${this._pathHandler.maxBlobSize})", "inaccessible", "not a file", "large file", "binary", "probe ${i.size} blobs", "unknown blob names:", "nonindexed blob names:", "upload ${i.items.size} blobs", "upload begin: ${r.length} blobs", "    - ${s.folderId}:${s.pathName}; expected blob name ${s.blobName}", "batch upload failed: ${He(s)}", "sequential upload of ${o.pathName} -> ${o.blobName}", "inflight items signaling empty", "path map invalidate: ${r}:${n} (${s})", "probe enqueue ${i.blobName} -> ${n}, ${i.folderId}:${i.relPath}", "upload enqueue ${n}:${i} -> ${r}", "probe-retry enqueue ${n.blobName} -> ${r}, ${n.folderId}:${n.relPath}", "probe-retry enqueue backoff ${n.blobName} -> ${r}, ${n.folderId}:${n.relPath}", "mtime-cache.json", "mtime-cache.json.tmp", "MtimeCache[${e}]", "reading blob name cache from ${i}", "blob naming version ${a.namingVersion} !== ${n1}", "read ${s} entries from ${i}", "no blob name cache found at ${i} (probably new source folder); error = ${o}", "failed to read blob name cache ${i}: ${o}", "MTimeCacheWriter", "persisting to ${this._cacheFileName}", "persisted ${n.entries.length} entries at naming version ${n1} to ${this._cacheFileName}", "ChangeTracker", "invalid chunk: ", "OpenFileManager", "Source folder ${r} is already open", "Opened source folder ${r}", "Closed source folder ${r}", "stop tracking ${r}:${n}", "blob name reported missing", "document lost focus", "Source folder ${r} is not open", "blob name calculation failed", "new document has no blob name", "start tracking ${r}:${n}", "apply: new changeset for ${r}:${n}; total = ${i.recentChangesets.length}", "multiple non-uploaded chunks", "apply: no longer tracking non-uploaded changes for ${r}:${n}", "apply: new changeset for ${r}:${n}; chunks = ${l}; total = ${i.recentChangesets.length}", "cancel in-progress upload: ${r}:${n}", "upload request: ${r}:${n}; reason = ${i}", "upload request delayed: upload for ${r}:${n} already in progress", "retry upload; ${r}:${n}", "retry upload: document is no longer tracked; ${r}:${n}", "retry upload: upload already in progress; ${r}:${n}", "enqueue upload: ${r}:${n}", "upload: upload cancelled or no longer tracking document ${n}:${i}", "failed to compute blob name", "upload: begin; ${n}:${i}, ${c}", "upload: failed; ${n}:${i}, ${c}; ${He(p)};", "upload encountered permanent error: ${He(p)}", "upload: upload cancelled; pathName = ${n}:${i}", "upload: upload timed out, cancelling; pathName = ${n}:${i}", "upload: completed; ${n}:${i}, ${f}", "upload: completed with mismatched blobName; pathName, received, expected = ${n}:${i}, ${f}, ${c}", "requeue verify-wait: upload cancelled; ${i}:${s}, ${n}", "verify-wait: enqueue long; pathName = ${i}:${s}", "verify-wait: enqueue; ${r.folderId}:${r.pathName}, ${n}", "verify batch: blob count = ${i.length}", "verify: timeout exceeded", "commit: upload cancelled for ${i}:${s}", "commit: ${i}:${s}, ${n}; uploadSeq = ${c.uploadSeq}", "purge: removed ${o} changesets from ${s.folderId}:${s.pathName}", "embargoing: ${r}:${n} reason = ${s}", "PathMap", "Source folder ${t} is already open", "Source folder ${t} contains ${a.folderRoot}", "Source folder ${a.folderRoot} contains ${t}", "Opened source folder ${t} with id ${n}", "Closed source folder ${n} with id ${t}", "PathNotifier[${n}]", "created", "changed", "${s} ${a}: ${i}, acceptance = ${o.format()}", "Path deleted: ${n}", "**/*", "TabWatcher", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enqueue: ${i.rootPath}:${i.relPath}", "find-missing reported ${s.nonindexedBlobNames.length} nonindexed blob names", "probe enqueue: ${i.qualifiedPath.rootPath}:${i.qualifiedPath.relPath}: ${n}", "retry enqueue: ${n.qualifiedPath.rootPath}:${n.qualifiedPath.relPath}: ${r}", "long retry enqueue: ${n.qualifiedPath.rootPath}:${n.qualifiedPath.relPath}: ${r}", "Source folder has been disposed", "home directory", "too large", "nested", "permission denied", "trackable", "qualifying", "permission needed", "OpenFileManagerProxy created. V2 enabled: [${this._openFileManager.isV2Enabled}]", "syncingPermitted", "workspaceTooLarge", "uploadingHomeDir", ".augmentroot", ".giti<PERSON>re", ".augment<PERSON>ore", "WorkspaceManager", "Rejecting external source folder ${s}: home directory", "Adding external source folder ${s}", "Adding workspace folder ${s}; folderRoot = ${o}; syncingPermission = ${a}", "Removing workspace folder ${s}", "Adding external source folder ${y4(r)}", "Removing external source folder ${r}", "Enabling syncing for all trackable source folders", "Disabling syncing for all trackable source folders", "Source folder ${n} will not be tracked. Containing folder: ${s}", "full", "Beginning ${a} qualification of source folder ${r}", "Cancelled qualification of source folder ${r}", "phony", "Beginning ${a} qualification of source folder ${r} per feature flag", "Finished ${a} qualification of source folder ${r}: syncing disabled for workspace", "Requesting syncing permission because source folder does not appear to be a source repo", "Finished ${a} qualification of source folder ${r}: folder not trackable; too large", "source folder has been removed", "syncing is disabled", "source folder is nested inside folder ${a.containingFolderRoot}", "source folder is a home directory", "source folder is too large", "syncing permission denied for this source folder", "syncing permission not yet granted for this source folder", "source folder is being qualified", "WorkspaceManager[${o.folderName}]", "Stop tracking: ${a}", "Start tracking", "Startup metrics", "Cancelled in-progress creation of source folder", "create SourceFolder", "Stopped tracking source folder", "read MtimeCache", "pre-populate PathMap", "enumerate", "await <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> quiesced", "enable persist", "finished-syncing", "Migrating mtime cache for ${n.folderName} from \"${s}\" to \"${i}\"", "Failed to migrate mtime cache for ${n.folderName} from \"${s}\" to \"${i}\": ${He(o)}", "One or more source folders failed to refresh: ${He(n)}", "Refreshing source folder ${r.folderName}", "Failed to install SourceFolderTracker for ${r.folderName}: ${He(c)}", "enumerate paths", "purge stale PathMap entries", "create PathFilter", "create PathNotifier", "_trackFileEdits was called on ${r.folderName}", "_fileEditManager is undefined", "_fileEditManager tracking the folder", "_trackVcsRepo was called on ${r.folderName}", "_vcsWatcher is undefined", "vcsDetails is undefined", "Not creating VCSRepoWatcher: vcs root ${ko(i.root)} !== repo root ${r.repoRoot}", "_vcsWatcher tracking the folder", "Cancelled in-progress tracking of source folder", "blob_context_mismatch", "received checkpoint not found for request id ${r}", "Directory created: ${n}", "Directory removed: ${n}", "Syncing permission parameters", "enableFileLimitsForSyncingPermission", "maxTrackableFiles", "maxTrackableFilesWithoutPermission", "minUploadedFractionWithoutPermission", "minUploadedFractionWithoutPermission as a percentage", "verifyFolderIsSourceRepo", "refuseToSyncHomeDirectories", "Source folder: ${s}", "Folder type", "vscode workspace folder", "external folder", "Not tracked: nested folder. Containing folder", "Not tracked: home directory", "Not tracked: folder is too large", "Not tracked: syncing permission denied", "Not tracked: syncing permission not yet granted", "Tracking in progress", "Folder root", "Repo root", "Mtime cache dir", "Source folder startup", "in progress", "Tracked files", "Syncing backlog size", "Source folders: no open source folders", "Workspace status", "Workspace startup", "Blobs in context", "Verifying blob names... ${l} / ${s.blobNames.length} ", "Unknown blob names", "Unable to verify blob names: ${a}", "No source folders in workspace", "Current checkpoint", "Blobs in current checkpoint", "Added blobs not in checkpoint", "Deleted blobs not in checkpoint", "Tracking enabled", "Workspace startup complete in ${r} ms", "permission not granted", "workspacePopulated", "workspaceSelected", "/actions/open/chat", "/actions/open/augment-settings", "/actions/open/memories", "/actions/open/guidelines-settings", "1.96.0", "Starting macCA", "macCa Done", "Exception loading mac-ca certs:", "Augment Extension Status", "Unable to enable extension: ${He(n)}", "lastEnabledExtensionVersion", "No API token is configured", "No completion URL is configured", "augment.userTier", "${ce.name} - ${ce.internalName}", "Failed to get model config: ${ie}", "Enabling viewTextDocument background file scheme", "Open in Augment Chat", "Instruct", "Failed to start the MCP server. ", "Show in settings", "Augment-VSCode/1.0", "sign-in", "workspace-context", "awaiting-syncing-permission", "folder-selection", "Unhandled app case: ${k}", "Disabling Hindsight Data", "Enabling Hindsight Data", "initialization-success", "Error initializing background next edit: ", "initialization-failure", "background_next_edit_initialization_failure", "Failed to enable background next edit generation", "initialization-skip", "this.workspaceManager", "this.keybindingW<PERSON>er", "this._<PERSON><PERSON><PERSON><PERSON>", "this._nextEditRequestManager", "Background next edit initialization failed because ${Ae}", "disposed", "background-suggestions-enabled", "background-suggestions-disabled", "highlights-enabled", "highlights-disabled", "augment-next-edit", "preRelease", "Is the extension in pre-release? ${Y}", "configuration-snapshot", "Failed to report initial feature vector:", "Failed to report feature vector:", "Failed to fetch feature flags: ", "Model info not set", "Retrieving model config", "Retrieved model config", "Failed to retrieve model config: ", "Model config retrieval cancelled", "Returning model config", "Retrying model config retrieval in ${n} msec", "Registering inline completions  provider.", "Registering completion items provider.", "suggestion-forced", "noop-clicked", "next-edit-toggle-suggestion-tree", "toggle-panel-horizontal-split", "panel-focus-executed", "augment-next-edit.focus", "next-edit-panel-focus", "learn-more-clicked", "https://docs.augmentcode.com/using-augment/next-edit", "Augment extension is initializing", "Augment is not enabled in this workspace", "Extension version", "augment.vscode-augment", "Cannot retrieve extension version", "Session ID", "Recent Completion Requests (oldest to newest)", "No recent completion requests", "Recent Instruction Requests (oldest to newest)", "No recent instruction requests", "Recent Chat Requests (oldest to newest)", "No recent chat requests", "Extension configuration", "Using API token", "Tenant URL", "Back-end Configuration", "MaxUploadSizeBytes", "enableCompletionFileEditEvents", "Supported languages (Augment name / VSCode name):", "${c.name} / ${c.vscodeName}", "Available Models", " (default)", " (current)", "No models available", "Current Model", "Querying current model", "(in progress...)", "(Using default model)", "Model \"${o.modelName}\" not known.", "Unable to query info about model \"${o.modelName}\": ${He(c)}", "Blob upload", "Blob upload enabled in configuration settings", "Blob upload disabled in configuration settings", "Completion status", "Attempting completion from ${a}", "Request ID", "this is the prefix", "this is the suffix", "/this/is/the/path", "Response received in ${Date.now()-l} ms", "No completion received", "${u.completionItems.length} completion(s) received", "Completion request failed: ${l}", "Feature Flags", "Internal error. Cannot get Augment extension status.", "sessionId", "activate()", "======== Activating extension ========", "======== Deactivating extension ========", "======== Reloading extension ========", "Ignoring URI ${j.toString()}", "agent", "Invalid chat mode: ${Me}", "Could not open memories: path not found.", "Unhandled URI ${ct.Uri.from({scheme:j.scheme,authority:j.authority,path:j.path}).toString()}", "${t6.default.platform()}; ${t6.default.arch()}; ${t6.default.release()}", "${e.extension.id}/${e.extension.packageJSON.version} (${a}) ${ct.env.uriScheme}/${ct.version}", "UserShouldSignIn", "WorkspaceNotSelected", "ShouldDisableCopilot", "ShouldDisableCodeium", "SyncingPermissionNeeded", "augment-chat", "apiToken", "completionURL", "o<PERSON>h", "modelName", "Reloading extension due to config change", "vscode-augment.enableDebugFeatures", "vscode-augment.enableReviewerWorkflows", "vscode-augment.enableNextEdit", "vscode-augment.enableNextEditBackgroundSuggestions", "vscode-augment.enableGenerateCommitMessage", "vscode-augment.nextEdit.enablePanel", "vscode-augment.featureFlags.enableRemoteAgents", "enableSmartPaste", "enableSmartPasteMinVersion", "enableInstructions", "vscodeSourcesMinVersion", "vscodeChatHintDecorationMinVersion", "vscodeEnableCpuProfile", "vscodeNextEditMinVersion", "vscodeGenerateCommitMessageMinVersion", "vscode-augment.workspace-manager-ui.enabled", "vscode-augment.internal-new-instructions.enabled", "vscode-augment.internal-dv.enabled", "vscode-augment.sources-enabled", "vscode-augment.chat-hint.decoration", "vscode-augment.cpu-profile.enabled"], "imports": ["crypto", "util", "ajv/dist/runtime/validation_error", "ajv/dist/runtime/uri", "ajv/dist/runtime/ucs2length", "ajv/dist/runtime/equal", "ajv-formats/dist/formats", "assert", "http", "stream", "net", "buffer", "querystring", "events", "diagnostics_channel", "tls", "worker_threads", "zlib", "perf_hooks", "util/types", "http2", "url", "async_hooks", "console", "dns", "string_decoder", "https", "child_process", "os", "tty", "fs", "path", "process", "osx-temperature-sensor", "vscode", "fs/promises"], "exports": [], "vscodeAPIs": ["vscode.openWith", "vscode.open", "vscode.provideDocumentSemanticTokensLegend", "vscode.provideDocumentSemanticTokens", "vscode.diff", "vscode.openFolder", "vscode.removeFromRecentlyOpened", "vscode.closeFolder", "vscode.git"], "trialRelated": ["trial", "TRIAL", "Expire", "expire", "Remaining", "remaining", "days", "Usage", "usage", "limit", "Limit", "LIMIT"], "licenseRelated": ["license", "License", "LICENSE", "activation", "Activation", "subscription", "Subscription"], "machineIdRelated": ["machineId", "Telemetry", "Fingerprint", "fingerprint", "<PERSON><PERSON>", "device", "DEVICE", "hardware", "Hardware", "HARDWARE"], "usageTracking": ["Count", "count", "COUNT", "track", "Track", "TRACK", "metrics", "Metrics", "stats", "Stats", "statS"], "fileOperations": ["fs.call", "fs.toLowerCase", "fs.readFile", "fs.delete", "fs.writeFile", "fs.createDirectory", "fs.stat", "fs.readDirectory", "fs.push"], "networkRequests": ["request(", "fetch("]}, "summary": {"totalFunctions": 3160, "totalStrings": 7523, "vscodeAPIs": 9, "trialRelated": 12, "licenseRelated": 7, "machineIdRelated": 10}}