#!/usr/bin/env node

/**
 * Manual Augment Extension Decompiler
 * This script attempts to locate and decompile the Augment extension
 */

const fs = require('fs');
const path = require('path');
const os = require('os');

class AugmentDecompiler {
    constructor() {
        this.extensionPaths = [
            path.join(os.homedir(), '.vscode', 'extensions'),
            path.join(os.homedir(), '.vscode-insiders', 'extensions'),
            path.join(process.env.APPDATA || '', 'Code', 'User', 'extensions'),
            path.join(process.env.LOCALAPPDATA || '', 'Programs', 'Microsoft VS Code', 'resources', 'app', 'extensions')
        ];
        this.outputDir = './decompiled-augment';
    }

    findAugmentExtensions() {
        console.log('🔍 Searching for Augment extensions...');
        const foundExtensions = [];

        for (const basePath of this.extensionPaths) {
            try {
                if (fs.existsSync(basePath)) {
                    console.log(`  Checking: ${basePath}`);
                    const dirs = fs.readdirSync(basePath);
                    
                    for (const dir of dirs) {
                        if (dir.toLowerCase().includes('augment')) {
                            const fullPath = path.join(basePath, dir);
                            if (fs.statSync(fullPath).isDirectory()) {
                                console.log(`  ✅ Found: ${fullPath}`);
                                foundExtensions.push(fullPath);
                            }
                        }
                    }
                }
            } catch (error) {
                console.log(`  ❌ Cannot access: ${basePath}`);
            }
        }

        return foundExtensions;
    }

    analyzePackageJson(extensionPath) {
        console.log('\n📋 Analyzing package.json...');
        
        const packagePath = path.join(extensionPath, 'package.json');
        if (!fs.existsSync(packagePath)) {
            console.log('  ❌ package.json not found');
            return null;
        }

        try {
            const packageContent = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
            
            console.log(`  ✅ Extension: ${packageContent.displayName || packageContent.name}`);
            console.log(`  ✅ Version: ${packageContent.version}`);
            console.log(`  ✅ Publisher: ${packageContent.publisher}`);
            console.log(`  ✅ Description: ${packageContent.description}`);
            
            if (packageContent.main) {
                console.log(`  ✅ Main file: ${packageContent.main}`);
            }
            
            if (packageContent.activationEvents) {
                console.log(`  ✅ Activation events: ${packageContent.activationEvents.length}`);
            }
            
            if (packageContent.contributes) {
                console.log(`  ✅ Contributes: ${Object.keys(packageContent.contributes).join(', ')}`);
            }

            return packageContent;
        } catch (error) {
            console.log(`  ❌ Error reading package.json: ${error.message}`);
            return null;
        }
    }

    decompileExtensionJs(extensionPath) {
        console.log('\n🔧 Decompiling extension.js...');
        
        const extensionJsPath = path.join(extensionPath, 'out', 'extension.js');
        if (!fs.existsSync(extensionJsPath)) {
            console.log('  ❌ extension.js not found in out/ directory');
            
            // Try alternative locations
            const altPaths = [
                path.join(extensionPath, 'extension.js'),
                path.join(extensionPath, 'dist', 'extension.js'),
                path.join(extensionPath, 'lib', 'extension.js')
            ];
            
            for (const altPath of altPaths) {
                if (fs.existsSync(altPath)) {
                    console.log(`  ✅ Found extension.js at: ${altPath}`);
                    return this.analyzeJavaScript(altPath);
                }
            }
            
            console.log('  ❌ No extension.js found in any location');
            return null;
        }

        return this.analyzeJavaScript(extensionJsPath);
    }

    analyzeJavaScript(jsPath) {
        try {
            const jsContent = fs.readFileSync(jsPath, 'utf8');
            const stats = fs.statSync(jsPath);
            
            console.log(`  ✅ File size: ${stats.size} bytes`);
            console.log(`  ✅ Lines: ${jsContent.split('\n').length}`);
            
            // Create output directory
            if (!fs.existsSync(this.outputDir)) {
                fs.mkdirSync(this.outputDir, { recursive: true });
            }
            
            // Save original minified code
            fs.writeFileSync(path.join(this.outputDir, 'extension-original.js'), jsContent);
            
            // Beautify the code
            const beautified = this.beautifyCode(jsContent);
            fs.writeFileSync(path.join(this.outputDir, 'extension-beautified.js'), beautified);
            
            // Extract patterns
            const analysis = this.extractPatterns(jsContent);
            fs.writeFileSync(
                path.join(this.outputDir, 'code-analysis.json'), 
                JSON.stringify(analysis, null, 2)
            );
            
            console.log(`  ✅ Decompiled files saved to: ${this.outputDir}`);
            
            return analysis;
            
        } catch (error) {
            console.log(`  ❌ Error analyzing JavaScript: ${error.message}`);
            return null;
        }
    }

    beautifyCode(code) {
        console.log('  🎨 Beautifying code...');
        
        // Basic JavaScript beautification
        let beautified = code;
        
        // Add line breaks after semicolons and braces
        beautified = beautified.replace(/;(?![^"']*["'][^"']*$)/g, ';\n');
        beautified = beautified.replace(/\{(?![^"']*["'][^"']*$)/g, '{\n');
        beautified = beautified.replace(/\}(?![^"']*["'][^"']*$)/g, '\n}\n');
        beautified = beautified.replace(/,(?![^"']*["'][^"']*$)/g, ',\n');
        
        // Basic indentation
        const lines = beautified.split('\n');
        let indentLevel = 0;
        const indentedLines = [];
        
        for (let line of lines) {
            line = line.trim();
            if (line === '') continue;
            
            if (line.includes('}')) indentLevel = Math.max(0, indentLevel - 1);
            
            indentedLines.push('  '.repeat(indentLevel) + line);
            
            if (line.includes('{')) indentLevel++;
        }
        
        return indentedLines.join('\n');
    }

    extractPatterns(code) {
        console.log('  🔍 Extracting patterns...');
        
        const patterns = {
            functions: [],
            strings: [],
            imports: [],
            exports: [],
            vscodeAPIs: [],
            trialRelated: [],
            licenseRelated: [],
            machineIdRelated: [],
            usageTracking: [],
            fileOperations: [],
            networkRequests: []
        };

        // Extract function names
        const functionMatches = code.match(/function\s+([a-zA-Z_$][a-zA-Z0-9_$]*)/g) || [];
        patterns.functions = functionMatches.map(match => match.replace('function ', ''));

        // Extract string literals
        const stringMatches = code.match(/(['"`])((?:(?!\1)[^\\]|\\.)*)(\1)/g) || [];
        patterns.strings = stringMatches
            .map(s => s.slice(1, -1))
            .filter(s => s.length > 3 && s.length < 100);

        // Extract require/import statements
        const requireMatches = code.match(/require\(['"`]([^'"`]+)['"`]\)/g) || [];
        patterns.imports = requireMatches.map(match => match.match(/['"`]([^'"`]+)['"`]/)[1]);

        // Extract VS Code API calls
        const vscodeMatches = code.match(/vscode\.[a-zA-Z_$][a-zA-Z0-9_$.]*/g) || [];
        patterns.vscodeAPIs = [...new Set(vscodeMatches)];

        // Extract trial-related patterns
        const trialPatterns = [
            /trial/gi, /expire/gi, /remaining/gi, /days/gi, /usage/gi, /limit/gi
        ];
        trialPatterns.forEach(pattern => {
            const matches = code.match(pattern) || [];
            patterns.trialRelated.push(...matches);
        });

        // Extract license-related patterns
        const licensePatterns = [
            /license/gi, /activation/gi, /subscription/gi, /premium/gi
        ];
        licensePatterns.forEach(pattern => {
            const matches = code.match(pattern) || [];
            patterns.licenseRelated.push(...matches);
        });

        // Extract machine ID patterns
        const machineIdPatterns = [
            /machineId/gi, /telemetry/gi, /fingerprint/gi, /device/gi, /hardware/gi
        ];
        machineIdPatterns.forEach(pattern => {
            const matches = code.match(pattern) || [];
            patterns.machineIdRelated.push(...matches);
        });

        // Extract usage tracking patterns
        const usagePatterns = [
            /count/gi, /track/gi, /analytics/gi, /metrics/gi, /stats/gi
        ];
        usagePatterns.forEach(pattern => {
            const matches = code.match(pattern) || [];
            patterns.usageTracking.push(...matches);
        });

        // Extract file operations
        const fileMatches = code.match(/fs\.[a-zA-Z_$][a-zA-Z0-9_$]*/g) || [];
        patterns.fileOperations = [...new Set(fileMatches)];

        // Extract network requests
        const networkMatches = code.match(/(fetch|axios|request|http[s]?)\s*\(/g) || [];
        patterns.networkRequests = [...new Set(networkMatches)];

        // Remove duplicates and clean up
        Object.keys(patterns).forEach(key => {
            if (Array.isArray(patterns[key])) {
                patterns[key] = [...new Set(patterns[key])].filter(Boolean);
            }
        });

        return patterns;
    }

    generateReport(packageInfo, codeAnalysis) {
        console.log('\n📄 Generating decompilation report...');
        
        const report = {
            timestamp: new Date().toISOString(),
            extension: packageInfo,
            codeAnalysis: codeAnalysis,
            summary: {
                totalFunctions: codeAnalysis?.functions?.length || 0,
                totalStrings: codeAnalysis?.strings?.length || 0,
                vscodeAPIs: codeAnalysis?.vscodeAPIs?.length || 0,
                trialRelated: codeAnalysis?.trialRelated?.length || 0,
                licenseRelated: codeAnalysis?.licenseRelated?.length || 0,
                machineIdRelated: codeAnalysis?.machineIdRelated?.length || 0
            }
        };

        fs.writeFileSync(
            path.join(this.outputDir, 'decompilation-report.json'),
            JSON.stringify(report, null, 2)
        );

        console.log(`  ✅ Report saved to: ${path.join(this.outputDir, 'decompilation-report.json')}`);
        
        return report;
    }

    async decompile() {
        console.log('🚀 Starting Augment Extension Decompilation...\n');
        
        try {
            // Find Augment extensions
            const extensions = this.findAugmentExtensions();
            
            if (extensions.length === 0) {
                console.log('❌ No Augment extensions found!');
                return;
            }
            
            // Use the most recent extension
            const latestExtension = extensions[extensions.length - 1];
            console.log(`\n🎯 Analyzing: ${latestExtension}`);
            
            // Analyze package.json
            const packageInfo = this.analyzePackageJson(latestExtension);
            
            // Decompile extension.js
            const codeAnalysis = this.decompileExtensionJs(latestExtension);
            
            // Generate report
            if (packageInfo || codeAnalysis) {
                const report = this.generateReport(packageInfo, codeAnalysis);
                
                console.log('\n🎉 Decompilation completed successfully!');
                console.log(`📁 Results saved to: ${this.outputDir}`);
                
                if (codeAnalysis) {
                    console.log('\n📊 Quick Summary:');
                    console.log(`  Functions found: ${codeAnalysis.functions.length}`);
                    console.log(`  VS Code APIs: ${codeAnalysis.vscodeAPIs.length}`);
                    console.log(`  Trial-related patterns: ${codeAnalysis.trialRelated.length}`);
                    console.log(`  License-related patterns: ${codeAnalysis.licenseRelated.length}`);
                    console.log(`  Machine ID patterns: ${codeAnalysis.machineIdRelated.length}`);
                }
            } else {
                console.log('❌ Decompilation failed - no data extracted');
            }
            
        } catch (error) {
            console.error('❌ Decompilation failed:', error.message);
        }
    }
}

// Run the decompiler
const decompiler = new AugmentDecompiler();
decompiler.decompile().catch(console.error);
