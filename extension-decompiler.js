#!/usr/bin/env node

/**
 * VS Code Extension Decompiler
 * Analyzes and decompiles VS Code extensions to understand their functionality
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class ExtensionDecompiler {
    constructor(extensionPath) {
        this.extensionPath = extensionPath;
        this.outputDir = './decompiled-output';
        this.analysisResults = {
            packageInfo: {},
            fileStructure: {},
            codeAnalysis: {},
            securityFindings: [],
            detectionMechanisms: [],
            usageTracking: []
        };
    }

    async decompile() {
        console.log('🔍 Starting VS Code Extension Decompilation...');
        console.log(`📁 Extension Path: ${this.extensionPath}`);
        
        // Create output directory
        if (!fs.existsSync(this.outputDir)) {
            fs.mkdirSync(this.outputDir, { recursive: true });
        }

        try {
            // Step 1: Analyze package.json
            await this.analyzePackageJson();
            
            // Step 2: Extract and analyze file structure
            await this.analyzeFileStructure();
            
            // Step 3: Decompile main extension file
            await this.decompileMainExtension();
            
            // Step 4: Search for security and tracking mechanisms
            await this.analyzeSecurityMechanisms();
            
            // Step 5: Generate comprehensive report
            await this.generateReport();
            
            console.log('✅ Decompilation completed successfully!');
            console.log(`📄 Results saved to: ${this.outputDir}`);
            
        } catch (error) {
            console.error('❌ Decompilation failed:', error.message);
            throw error;
        }
    }

    async analyzePackageJson() {
        console.log('📋 Analyzing package.json...');
        
        const packagePath = path.join(this.extensionPath, 'package.json');
        if (fs.existsSync(packagePath)) {
            const packageContent = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
            this.analysisResults.packageInfo = packageContent;
            
            // Save formatted package.json
            fs.writeFileSync(
                path.join(this.outputDir, 'package-analysis.json'),
                JSON.stringify(packageContent, null, 2)
            );
            
            console.log(`  ✓ Extension: ${packageContent.displayName || packageContent.name}`);
            console.log(`  ✓ Version: ${packageContent.version}`);
            console.log(`  ✓ Publisher: ${packageContent.publisher}`);
        }
    }

    async analyzeFileStructure() {
        console.log('🗂️  Analyzing file structure...');
        
        const structure = this.getDirectoryStructure(this.extensionPath);
        this.analysisResults.fileStructure = structure;
        
        // Save file structure
        fs.writeFileSync(
            path.join(this.outputDir, 'file-structure.json'),
            JSON.stringify(structure, null, 2)
        );
        
        console.log('  ✓ File structure mapped');
    }

    getDirectoryStructure(dirPath, level = 0) {
        const structure = {};
        
        if (level > 3) return structure; // Limit depth
        
        try {
            const items = fs.readdirSync(dirPath);
            
            for (const item of items) {
                const itemPath = path.join(dirPath, item);
                const stats = fs.statSync(itemPath);
                
                if (stats.isDirectory()) {
                    structure[item] = {
                        type: 'directory',
                        children: this.getDirectoryStructure(itemPath, level + 1)
                    };
                } else {
                    structure[item] = {
                        type: 'file',
                        size: stats.size,
                        extension: path.extname(item)
                    };
                }
            }
        } catch (error) {
            console.warn(`  ⚠️  Could not read directory: ${dirPath}`);
        }
        
        return structure;
    }

    async decompileMainExtension() {
        console.log('🔧 Decompiling main extension file...');
        
        const mainFile = path.join(this.extensionPath, 'out', 'extension.js');
        if (!fs.existsSync(mainFile)) {
            console.log('  ⚠️  Main extension file not found in out/ directory');
            return;
        }

        try {
            // Read the minified/bundled extension file
            const extensionCode = fs.readFileSync(mainFile, 'utf8');
            
            // Basic code analysis
            await this.analyzeCode(extensionCode);
            
            // Try to beautify the code
            const beautifiedCode = this.beautifyJavaScript(extensionCode);
            
            // Save beautified code
            fs.writeFileSync(
                path.join(this.outputDir, 'extension-beautified.js'),
                beautifiedCode
            );
            
            // Extract strings and patterns
            await this.extractStringsAndPatterns(extensionCode);
            
            console.log('  ✓ Main extension file analyzed');
            
        } catch (error) {
            console.error('  ❌ Failed to decompile main extension:', error.message);
        }
    }

    async analyzeCode(code) {
        console.log('🔍 Analyzing code patterns...');
        
        const analysis = {
            totalLines: code.split('\n').length,
            totalSize: code.length,
            functions: [],
            imports: [],
            exports: [],
            apiCalls: [],
            fileOperations: [],
            networkRequests: [],
            storageOperations: []
        };

        // Extract function patterns
        const functionMatches = code.match(/function\s+([a-zA-Z_$][a-zA-Z0-9_$]*)/g) || [];
        analysis.functions = functionMatches.map(match => match.replace('function ', ''));

        // Extract require/import statements
        const requireMatches = code.match(/require\(['"`]([^'"`]+)['"`]\)/g) || [];
        analysis.imports = requireMatches.map(match => match.match(/['"`]([^'"`]+)['"`]/)[1]);

        // Extract VS Code API calls
        const vscodeApiMatches = code.match(/vscode\.[a-zA-Z_$][a-zA-Z0-9_$.]*/g) || [];
        analysis.apiCalls = [...new Set(vscodeApiMatches)];

        // Extract file system operations
        const fsMatches = code.match(/fs\.[a-zA-Z_$][a-zA-Z0-9_$]*/g) || [];
        analysis.fileOperations = [...new Set(fsMatches)];

        // Extract network requests
        const networkMatches = code.match(/(fetch|axios|request|http[s]?)\s*\(/g) || [];
        analysis.networkRequests = [...new Set(networkMatches)];

        // Extract storage operations
        const storageMatches = code.match(/(localStorage|sessionStorage|globalState|workspaceState)\.[a-zA-Z_$][a-zA-Z0-9_$]*/g) || [];
        analysis.storageOperations = [...new Set(storageMatches)];

        this.analysisResults.codeAnalysis = analysis;
        
        // Save code analysis
        fs.writeFileSync(
            path.join(this.outputDir, 'code-analysis.json'),
            JSON.stringify(analysis, null, 2)
        );
        
        console.log(`  ✓ Found ${analysis.functions.length} functions`);
        console.log(`  ✓ Found ${analysis.apiCalls.length} VS Code API calls`);
        console.log(`  ✓ Found ${analysis.storageOperations.length} storage operations`);
    }

    beautifyJavaScript(code) {
        // Simple JavaScript beautifier
        let beautified = code;
        
        // Add line breaks after semicolons and braces
        beautified = beautified.replace(/;/g, ';\n');
        beautified = beautified.replace(/\{/g, '{\n');
        beautified = beautified.replace(/\}/g, '\n}\n');
        
        // Add indentation (basic)
        const lines = beautified.split('\n');
        let indentLevel = 0;
        const indentedLines = [];
        
        for (let line of lines) {
            line = line.trim();
            if (line === '') continue;
            
            if (line.includes('}')) indentLevel = Math.max(0, indentLevel - 1);
            
            indentedLines.push('  '.repeat(indentLevel) + line);
            
            if (line.includes('{')) indentLevel++;
        }
        
        return indentedLines.join('\n');
    }

    async extractStringsAndPatterns(code) {
        console.log('🔤 Extracting strings and patterns...');
        
        const patterns = {
            strings: [],
            urls: [],
            filePaths: [],
            regexPatterns: [],
            apiEndpoints: [],
            authTokens: [],
            licenseKeys: [],
            machineIds: []
        };

        // Extract all string literals
        const stringMatches = code.match(/(['"`])((?:(?!\1)[^\\]|\\.)*)(\1)/g) || [];
        patterns.strings = stringMatches.map(s => s.slice(1, -1)).filter(s => s.length > 3);

        // Extract URLs
        patterns.urls = patterns.strings.filter(s => 
            s.match(/^https?:\/\//) || s.includes('api.') || s.includes('.com')
        );

        // Extract file paths
        patterns.filePaths = patterns.strings.filter(s => 
            s.includes('/') || s.includes('\\') || s.includes('.json') || s.includes('.js')
        );

        // Extract potential API endpoints
        patterns.apiEndpoints = patterns.strings.filter(s => 
            s.includes('/api/') || s.includes('/v1/') || s.includes('endpoint')
        );

        // Extract potential auth tokens or keys
        patterns.authTokens = patterns.strings.filter(s => 
            s.includes('token') || s.includes('auth') || s.includes('key') || s.includes('secret')
        );

        // Extract license-related strings
        patterns.licenseKeys = patterns.strings.filter(s => 
            s.includes('license') || s.includes('trial') || s.includes('subscription')
        );

        // Extract machine ID related strings
        patterns.machineIds = patterns.strings.filter(s => 
            s.includes('machine') || s.includes('device') || s.includes('fingerprint') || s.includes('uuid')
        );

        // Save extracted patterns
        fs.writeFileSync(
            path.join(this.outputDir, 'extracted-patterns.json'),
            JSON.stringify(patterns, null, 2)
        );
        
        console.log(`  ✓ Extracted ${patterns.strings.length} strings`);
        console.log(`  ✓ Found ${patterns.urls.length} URLs`);
        console.log(`  ✓ Found ${patterns.authTokens.length} auth-related strings`);
        console.log(`  ✓ Found ${patterns.licenseKeys.length} license-related strings`);
    }

    async analyzeSecurityMechanisms() {
        console.log('🔒 Analyzing security and tracking mechanisms...');

        const mainFile = path.join(this.extensionPath, 'out', 'extension.js');
        if (!fs.existsSync(mainFile)) return;

        const code = fs.readFileSync(mainFile, 'utf8');

        // Analyze trial and license detection
        await this.analyzeTrialDetection(code);

        // Analyze account detection mechanisms
        await this.analyzeAccountDetection(code);

        // Analyze usage tracking
        await this.analyzeUsageTracking(code);

        // Analyze machine fingerprinting
        await this.analyzeMachineFingerprinting(code);

        console.log('  ✓ Security analysis completed');
    }

    async analyzeTrialDetection(code) {
        const trialMechanisms = {
            trialFiles: [],
            trialChecks: [],
            dateValidation: [],
            usageCounters: [],
            resetDetection: []
        };

        // Search for trial-related file operations
        const trialFileMatches = code.match(/['"](.*trial.*\.json)['"]/gi) || [];
        trialMechanisms.trialFiles = trialFileMatches.map(m => m.replace(/['"]/g, ''));

        // Search for trial validation logic
        const trialCheckPatterns = [
            /trial.*expired/gi,
            /trial.*remaining/gi,
            /trial.*days/gi,
            /trial.*count/gi,
            /isTrialExpired/gi,
            /checkTrial/gi
        ];

        trialCheckPatterns.forEach(pattern => {
            const matches = code.match(pattern) || [];
            trialMechanisms.trialChecks.push(...matches);
        });

        // Search for date validation
        const datePatterns = [
            /new Date\(\)/gi,
            /Date\.now\(\)/gi,
            /getTime\(\)/gi,
            /timestamp/gi
        ];

        datePatterns.forEach(pattern => {
            const matches = code.match(pattern) || [];
            trialMechanisms.dateValidation.push(...matches);
        });

        // Search for usage counters
        const usagePatterns = [
            /usage.*count/gi,
            /prompt.*count/gi,
            /request.*count/gi,
            /increment/gi,
            /decrement/gi
        ];

        usagePatterns.forEach(pattern => {
            const matches = code.match(pattern) || [];
            trialMechanisms.usageCounters.push(...matches);
        });

        this.analysisResults.detectionMechanisms.push({
            type: 'trial_detection',
            mechanisms: trialMechanisms
        });
    }

    async analyzeAccountDetection(code) {
        const accountMechanisms = {
            machineIdChecks: [],
            userIdentification: [],
            deviceFingerprinting: [],
            registryChecks: [],
            fileSystemChecks: []
        };

        // Search for machine ID related code
        const machineIdPatterns = [
            /machineId/gi,
            /telemetry\.machineId/gi,
            /device.*id/gi,
            /hardware.*id/gi,
            /system.*uuid/gi
        ];

        machineIdPatterns.forEach(pattern => {
            const matches = code.match(pattern) || [];
            accountMechanisms.machineIdChecks.push(...matches);
        });

        // Search for user identification
        const userPatterns = [
            /user.*id/gi,
            /account.*id/gi,
            /email/gi,
            /username/gi,
            /getUser/gi
        ];

        userPatterns.forEach(pattern => {
            const matches = code.match(pattern) || [];
            accountMechanisms.userIdentification.push(...matches);
        });

        // Search for device fingerprinting
        const fingerprintPatterns = [
            /fingerprint/gi,
            /browser.*agent/gi,
            /platform/gi,
            /os.*version/gi,
            /cpu.*info/gi
        ];

        fingerprintPatterns.forEach(pattern => {
            const matches = code.match(pattern) || [];
            accountMechanisms.deviceFingerprinting.push(...matches);
        });

        this.analysisResults.detectionMechanisms.push({
            type: 'account_detection',
            mechanisms: accountMechanisms
        });
    }

    async analyzeUsageTracking(code) {
        const trackingMechanisms = {
            promptCounting: [],
            sessionTracking: [],
            analyticsEvents: [],
            telemetryData: [],
            storageOperations: []
        };

        // Search for prompt counting
        const promptPatterns = [
            /prompt.*count/gi,
            /message.*count/gi,
            /request.*count/gi,
            /api.*calls/gi,
            /usage.*limit/gi
        ];

        promptPatterns.forEach(pattern => {
            const matches = code.match(pattern) || [];
            trackingMechanisms.promptCounting.push(...matches);
        });

        // Search for session tracking
        const sessionPatterns = [
            /session.*id/gi,
            /session.*start/gi,
            /session.*end/gi,
            /activity.*tracking/gi
        ];

        sessionPatterns.forEach(pattern => {
            const matches = code.match(pattern) || [];
            trackingMechanisms.sessionTracking.push(...matches);
        });

        // Search for analytics and telemetry
        const analyticsPatterns = [
            /analytics/gi,
            /telemetry/gi,
            /track.*event/gi,
            /log.*event/gi,
            /metrics/gi
        ];

        analyticsPatterns.forEach(pattern => {
            const matches = code.match(pattern) || [];
            trackingMechanisms.analyticsEvents.push(...matches);
        });

        this.analysisResults.usageTracking.push({
            type: 'usage_tracking',
            mechanisms: trackingMechanisms
        });
    }

    async analyzeMachineFingerprinting(code) {
        const fingerprintMechanisms = {
            hardwareInfo: [],
            systemInfo: [],
            networkInfo: [],
            browserInfo: [],
            uniqueIdentifiers: []
        };

        // Search for hardware fingerprinting
        const hardwarePatterns = [
            /cpu.*info/gi,
            /memory.*info/gi,
            /disk.*info/gi,
            /graphics.*info/gi,
            /hardware.*hash/gi
        ];

        hardwarePatterns.forEach(pattern => {
            const matches = code.match(pattern) || [];
            fingerprintMechanisms.hardwareInfo.push(...matches);
        });

        // Search for system fingerprinting
        const systemPatterns = [
            /os\.platform/gi,
            /os\.version/gi,
            /os\.arch/gi,
            /process\.platform/gi,
            /system.*info/gi
        ];

        systemPatterns.forEach(pattern => {
            const matches = code.match(pattern) || [];
            fingerprintMechanisms.systemInfo.push(...matches);
        });

        // Search for unique identifiers
        const idPatterns = [
            /uuid/gi,
            /guid/gi,
            /unique.*id/gi,
            /generate.*id/gi,
            /crypto.*random/gi
        ];

        idPatterns.forEach(pattern => {
            const matches = code.match(pattern) || [];
            fingerprintMechanisms.uniqueIdentifiers.push(...matches);
        });

        this.analysisResults.detectionMechanisms.push({
            type: 'machine_fingerprinting',
            mechanisms: fingerprintMechanisms
        });
    }

    async generateReport() {
        console.log('📄 Generating comprehensive report...');

        const report = {
            timestamp: new Date().toISOString(),
            extensionPath: this.extensionPath,
            summary: this.generateSummary(),
            packageInfo: this.analysisResults.packageInfo,
            fileStructure: this.analysisResults.fileStructure,
            codeAnalysis: this.analysisResults.codeAnalysis,
            detectionMechanisms: this.analysisResults.detectionMechanisms,
            usageTracking: this.analysisResults.usageTracking,
            securityFindings: this.analysisResults.securityFindings,
            recommendations: this.generateRecommendations()
        };

        // Save comprehensive report
        fs.writeFileSync(
            path.join(this.outputDir, 'decompilation-report.json'),
            JSON.stringify(report, null, 2)
        );

        // Generate human-readable report
        const readableReport = this.generateReadableReport(report);
        fs.writeFileSync(
            path.join(this.outputDir, 'ANALYSIS-REPORT.md'),
            readableReport
        );

        console.log('  ✓ Reports generated');
    }

    generateSummary() {
        const summary = {
            extensionName: this.analysisResults.packageInfo.displayName || this.analysisResults.packageInfo.name,
            version: this.analysisResults.packageInfo.version,
            publisher: this.analysisResults.packageInfo.publisher,
            totalFunctions: this.analysisResults.codeAnalysis.functions?.length || 0,
            apiCalls: this.analysisResults.codeAnalysis.apiCalls?.length || 0,
            storageOperations: this.analysisResults.codeAnalysis.storageOperations?.length || 0,
            detectionMechanismsFound: this.analysisResults.detectionMechanisms.length,
            trackingMechanismsFound: this.analysisResults.usageTracking.length
        };

        return summary;
    }

    generateRecommendations() {
        const recommendations = [];

        // Check for trial detection mechanisms
        const trialMechanisms = this.analysisResults.detectionMechanisms.find(m => m.type === 'trial_detection');
        if (trialMechanisms && trialMechanisms.mechanisms.trialFiles.length > 0) {
            recommendations.push({
                type: 'trial_bypass',
                description: 'Extension uses trial files for license validation',
                files: trialMechanisms.mechanisms.trialFiles,
                action: 'Remove or modify trial tracking files'
            });
        }

        // Check for machine fingerprinting
        const fingerprintMechanisms = this.analysisResults.detectionMechanisms.find(m => m.type === 'machine_fingerprinting');
        if (fingerprintMechanisms) {
            recommendations.push({
                type: 'fingerprint_bypass',
                description: 'Extension uses machine fingerprinting for user identification',
                action: 'Reset machine identification values'
            });
        }

        // Check for account detection
        const accountMechanisms = this.analysisResults.detectionMechanisms.find(m => m.type === 'account_detection');
        if (accountMechanisms) {
            recommendations.push({
                type: 'account_bypass',
                description: 'Extension tracks user accounts and device information',
                action: 'Clear account tracking data and reset device identifiers'
            });
        }

        return recommendations;
    }

    generateReadableReport(report) {
        return `# Augment Extension Decompilation Report

## Summary
- **Extension**: ${report.summary.extensionName}
- **Version**: ${report.summary.version}
- **Publisher**: ${report.summary.publisher}
- **Analysis Date**: ${report.timestamp}

## Code Analysis
- **Total Functions**: ${report.summary.totalFunctions}
- **VS Code API Calls**: ${report.summary.apiCalls}
- **Storage Operations**: ${report.summary.storageOperations}

## Detection Mechanisms Found
${report.detectionMechanisms.map(mechanism => `
### ${mechanism.type.replace('_', ' ').toUpperCase()}
${Object.entries(mechanism.mechanisms).map(([key, values]) =>
    values.length > 0 ? `- **${key}**: ${values.length} instances found` : ''
).filter(Boolean).join('\n')}
`).join('\n')}

## Usage Tracking
${report.usageTracking.map(tracking => `
### ${tracking.type.replace('_', ' ').toUpperCase()}
${Object.entries(tracking.mechanisms).map(([key, values]) =>
    values.length > 0 ? `- **${key}**: ${values.length} instances found` : ''
).filter(Boolean).join('\n')}
`).join('\n')}

## Recommendations
${report.recommendations.map(rec => `
### ${rec.type.replace('_', ' ').toUpperCase()}
- **Description**: ${rec.description}
- **Action**: ${rec.action}
${rec.files ? `- **Files**: ${rec.files.join(', ')}` : ''}
`).join('\n')}

## Files Generated
- \`decompilation-report.json\` - Complete analysis data
- \`package-analysis.json\` - Package.json analysis
- \`file-structure.json\` - Extension file structure
- \`code-analysis.json\` - Code analysis results
- \`extracted-patterns.json\` - Extracted strings and patterns
- \`extension-beautified.js\` - Beautified extension code
- \`ANALYSIS-REPORT.md\` - This human-readable report

## Next Steps
1. Review the beautified extension code for detailed implementation
2. Check extracted patterns for sensitive data
3. Follow recommendations to bypass detection mechanisms
4. Test the extension behavior after applying bypasses
`;
    }
}

// Main execution
async function main() {
    const args = process.argv.slice(2);

    if (args.length === 0) {
        console.log('Usage: node extension-decompiler.js <extension-path>');
        console.log('Example: node extension-decompiler.js "/path/to/extension"');
        process.exit(1);
    }

    const extensionPath = args[0];

    if (!fs.existsSync(extensionPath)) {
        console.error(`❌ Extension path does not exist: ${extensionPath}`);
        process.exit(1);
    }

    try {
        const decompiler = new ExtensionDecompiler(extensionPath);
        await decompiler.decompile();

        console.log('\n🎉 Decompilation completed successfully!');
        console.log('📁 Check the ./decompiled-output directory for results');

    } catch (error) {
        console.error('❌ Decompilation failed:', error.message);
        process.exit(1);
    }
}

// Run if called directly
if (require.main === module) {
    main().catch(console.error);
}

module.exports = ExtensionDecompiler;
