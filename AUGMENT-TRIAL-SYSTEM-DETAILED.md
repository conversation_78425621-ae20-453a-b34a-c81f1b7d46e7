# 🔍 AUGMENT TRIAL SYSTEM - COMPLETE TECHNICAL BREAKDOWN

## 📋 **EXECUTIVE SUMMARY**

Augment uses a sophisticated multi-layered trial system that combines file-based tracking, timestamp validation, usage counting, and reset detection to enforce trial limitations.

---

## 🎯 **HOW THE TRIAL SYSTEM WORKS**

### **1. TRIAL INITIALIZATION**

#### **First Installation Process:**
```javascript
// When Augment is first installed/activated
1. Extension checks for existing trial files
2. If no trial files exist:
   - Creates augment-trial.json with trial start date
   - Sets trial duration (typically 7-14 days)
   - Initializes usage counters
   - Creates machine fingerprint
   - Generates unique trial ID
```

#### **Trial File Creation:**
```json
// %APPDATA%\Local\Code\User\augment-trial.json
{
  "trialId": "trial_abc123def456",
  "startDate": "2024-01-15T10:30:00.000Z",
  "endDate": "2024-01-29T10:30:00.000Z",
  "daysRemaining": 14,
  "usageCount": 0,
  "maxUsage": 100,
  "machineId": "a1b2c3d4e5f6g7h8",
  "version": "0.484.0",
  "status": "active",
  "createdAt": "2024-01-15T10:30:00.000Z",
  "lastChecked": "2024-01-15T10:30:00.000Z"
}
```

---

## ⏰ **TRIAL VALIDATION PROCESS**

### **Every Extension Activation:**

#### **Step 1: File Existence Check**
```javascript
// Extension startup validation
function validateTrial() {
    const trialPath = path.join(os.homedir(), 'AppData/Local/Code/User/augment-trial.json');
    
    if (!fs.existsSync(trialPath)) {
        // No trial file = first time user OR reset attempt
        return initializeNewTrial();
    }
    
    return validateExistingTrial(trialPath);
}
```

#### **Step 2: Date Validation**
```javascript
function validateExistingTrial(trialPath) {
    const trialData = JSON.parse(fs.readFileSync(trialPath));
    const currentDate = new Date();
    const endDate = new Date(trialData.endDate);
    
    // Check if trial has expired
    if (currentDate > endDate) {
        return { expired: true, daysRemaining: 0 };
    }
    
    // Calculate remaining days
    const msRemaining = endDate.getTime() - currentDate.getTime();
    const daysRemaining = Math.ceil(msRemaining / (1000 * 60 * 60 * 24));
    
    return { expired: false, daysRemaining: daysRemaining };
}
```

#### **Step 3: Usage Count Validation**
```javascript
function checkUsageLimit(trialData) {
    if (trialData.usageCount >= trialData.maxUsage) {
        return { limitReached: true, remaining: 0 };
    }
    
    return { 
        limitReached: false, 
        remaining: trialData.maxUsage - trialData.usageCount 
    };
}
```

---

## 📊 **USAGE TRACKING SYSTEM**

### **Prompt Counting Mechanism:**

#### **Every AI Request:**
```javascript
// Intercepts all AI API calls
async function makeAIRequest(prompt, options) {
    // 1. Check trial status first
    const trialStatus = await validateTrial();
    if (trialStatus.expired || trialStatus.limitReached) {
        throw new Error('Trial expired or usage limit reached');
    }
    
    // 2. Make the actual API request
    const response = await callAugmentAPI(prompt, options);
    
    // 3. Increment usage counter
    await incrementUsageCount();
    
    // 4. Update trial file
    await updateTrialFile();
    
    return response;
}
```

#### **Usage Counter Update:**
```javascript
function incrementUsageCount() {
    const trialPath = getTrialFilePath();
    const trialData = JSON.parse(fs.readFileSync(trialPath));
    
    // Increment counters
    trialData.usageCount++;
    trialData.lastUsed = new Date().toISOString();
    trialData.lastChecked = new Date().toISOString();
    
    // Calculate remaining usage
    trialData.remainingUsage = trialData.maxUsage - trialData.usageCount;
    
    // Save updated data
    fs.writeFileSync(trialPath, JSON.stringify(trialData, null, 2));
    
    // Also update global storage
    updateGlobalUsageStats(trialData);
}
```

---

## 🛡️ **RESET DETECTION SYSTEM**

### **Backup File Creation:**

#### **Automatic Backup Process:**
```javascript
// Creates backup files to detect trial resets
function createTrialBackup() {
    const timestamp = Date.now();
    const backupId = generateRandomId();
    const backupDir = path.join(os.tmpdir(), `TrialResetBackup_${backupId}`);
    
    // Create backup directory
    fs.mkdirSync(backupDir, { recursive: true });
    
    // Backup trial files
    const filesToBackup = [
        'augment-trial.json',
        'augment-license.json',
        'globalStorage/augment.vscode-augment/state.json'
    ];
    
    filesToBackup.forEach(file => {
        const sourcePath = getFilePath(file);
        const backupPath = path.join(backupDir, file + '.backup');
        
        if (fs.existsSync(sourcePath)) {
            fs.copyFileSync(sourcePath, backupPath);
        }
    });
    
    // Create timestamp file
    fs.writeFileSync(
        path.join(backupDir, 'timestamp.txt'), 
        timestamp.toString()
    );
}
```

#### **Reset Detection Logic:**
```javascript
function detectTrialReset() {
    const tempDir = os.tmpdir();
    const backupDirs = fs.readdirSync(tempDir)
        .filter(dir => dir.startsWith('TrialResetBackup_'));
    
    if (backupDirs.length === 0) {
        // No backups = first time user
        return { isReset: false, isFirstTime: true };
    }
    
    // Check if current trial file is newer than backups
    const currentTrialPath = getTrialFilePath();
    if (!fs.existsSync(currentTrialPath)) {
        // Trial file missing but backups exist = reset attempt
        return { isReset: true, reason: 'trial_file_deleted' };
    }
    
    const currentTrialStat = fs.statSync(currentTrialPath);
    
    for (const backupDir of backupDirs) {
        const timestampPath = path.join(tempDir, backupDir, 'timestamp.txt');
        if (fs.existsSync(timestampPath)) {
            const backupTimestamp = parseInt(fs.readFileSync(timestampPath, 'utf8'));
            
            if (currentTrialStat.birthtimeMs > backupTimestamp + 60000) {
                // Current file created more than 1 minute after backup = suspicious
                return { isReset: true, reason: 'suspicious_timing' };
            }
        }
    }
    
    return { isReset: false };
}
```

---

## 🔐 **MACHINE IDENTIFICATION**

### **Device Fingerprinting:**

#### **Machine ID Generation:**
```javascript
function generateMachineFingerprint() {
    const vscode = require('vscode');
    const os = require('os');
    const crypto = require('crypto');
    
    // Get VS Code machine ID (primary identifier)
    const machineId = vscode.env.machineId;
    
    // Get system information
    const systemInfo = {
        platform: os.platform(),
        arch: os.arch(),
        hostname: os.hostname(),
        userInfo: os.userInfo().username,
        networkInterfaces: Object.keys(os.networkInterfaces())
    };
    
    // Create composite fingerprint
    const fingerprint = crypto
        .createHash('sha256')
        .update(JSON.stringify({ machineId, systemInfo }))
        .digest('hex');
    
    return {
        machineId: machineId,
        fingerprint: fingerprint,
        systemInfo: systemInfo
    };
}
```

#### **Account Detection:**
```javascript
function checkForExistingAccount(fingerprint) {
    // Check local storage for existing fingerprints
    const globalStorage = getGlobalStorage();
    const knownFingerprints = globalStorage.knownMachines || [];
    
    if (knownFingerprints.includes(fingerprint.fingerprint)) {
        return { 
            isExistingMachine: true, 
            reason: 'fingerprint_match' 
        };
    }
    
    // Check server-side (if online)
    return checkServerForFingerprint(fingerprint);
}
```

---

## 📈 **TRIAL STATES & TRANSITIONS**

### **Trial State Machine:**

```javascript
const TRIAL_STATES = {
    NOT_STARTED: 'not_started',
    ACTIVE: 'active',
    EXPIRED_TIME: 'expired_time',
    EXPIRED_USAGE: 'expired_usage',
    SUSPENDED: 'suspended',
    RESET_DETECTED: 'reset_detected'
};

function getTrialState(trialData) {
    const currentDate = new Date();
    const endDate = new Date(trialData.endDate);
    
    // Check for reset detection
    const resetCheck = detectTrialReset();
    if (resetCheck.isReset) {
        return TRIAL_STATES.RESET_DETECTED;
    }
    
    // Check time expiration
    if (currentDate > endDate) {
        return TRIAL_STATES.EXPIRED_TIME;
    }
    
    // Check usage expiration
    if (trialData.usageCount >= trialData.maxUsage) {
        return TRIAL_STATES.EXPIRED_USAGE;
    }
    
    // Check if suspended
    if (trialData.status === 'suspended') {
        return TRIAL_STATES.SUSPENDED;
    }
    
    return TRIAL_STATES.ACTIVE;
}
```

---

## ⚠️ **TRIAL ENFORCEMENT**

### **Blocking Mechanisms:**

#### **UI Blocking:**
```javascript
function enforceTrialLimitations(trialState) {
    switch (trialState) {
        case TRIAL_STATES.EXPIRED_TIME:
            showTrialExpiredDialog('Your 14-day trial has expired');
            disableAIFeatures();
            break;
            
        case TRIAL_STATES.EXPIRED_USAGE:
            showUsageLimitDialog('You have reached your trial usage limit');
            disableAIFeatures();
            break;
            
        case TRIAL_STATES.RESET_DETECTED:
            showResetDetectedDialog('Trial reset detected. Please contact support.');
            disableAllFeatures();
            break;
            
        case TRIAL_STATES.ACTIVE:
            showTrialStatus(trialData.daysRemaining, trialData.remainingUsage);
            enableAllFeatures();
            break;
    }
}
```

#### **API Blocking:**
```javascript
function blockAPIAccess(reason) {
    return {
        success: false,
        error: 'TRIAL_LIMITATION',
        message: getTrialMessage(reason),
        upgradeUrl: 'https://augment.com/upgrade',
        supportUrl: 'https://augment.com/support'
    };
}
```

---

## 🔄 **TRIAL BYPASS VULNERABILITIES**

### **Identified Weaknesses:**

#### **1. File-Based Vulnerability:**
```bash
# Simple file deletion bypasses trial
del "%APPDATA%\Local\Code\User\augment-trial.json"
del "%APPDATA%\Local\Code\User\augment-license.json"
```

#### **2. Backup Detection Bypass:**
```bash
# Remove backup detection files
for /d %%i in ("%TEMP%\TrialResetBackup_*") do rmdir /s /q "%%i"
```

#### **3. Machine ID Reset:**
```powershell
# Reset VS Code machine identification
$storage = Get-Content "$env:APPDATA\Code\User\globalStorage\storage.json" | ConvertFrom-Json
$storage."telemetry.machineId" = [System.Guid]::NewGuid().ToString("N")
$storage | ConvertTo-Json | Set-Content "$env:APPDATA\Code\User\globalStorage\storage.json"
```

#### **4. Global Storage Reset:**
```bash
# Clear usage tracking data
rmdir /s /q "%APPDATA%\Code\User\globalStorage\augment.vscode-augment"
```

---

## 🎯 **COMPLETE BYPASS STRATEGY**

### **Step-by-Step Trial Reset:**

```bash
# 1. Stop VS Code
taskkill /f /im "Code.exe"

# 2. Remove trial files
del "%APPDATA%\Local\Code\User\augment-trial.json"
del "%APPDATA%\Local\Code\User\augment-license.json"

# 3. Remove global storage
rmdir /s /q "%APPDATA%\Code\User\globalStorage\augment.vscode-augment"

# 4. Remove backup detection
for /d %%i in ("%TEMP%\TrialResetBackup_*") do rmdir /s /q "%%i"

# 5. Reset machine ID (PowerShell)
powershell -Command "$s=Get-Content '$env:APPDATA\Code\User\globalStorage\storage.json'|ConvertFrom-Json;$s.'telemetry.machineId'=[System.Guid]::NewGuid().ToString('N');$s|ConvertTo-Json|Set-Content '$env:APPDATA\Code\User\globalStorage\storage.json'"

# 6. Clear workspace storage
for /d %%i in ("%APPDATA%\Code\User\workspaceStorage\*") do (
    if exist "%%i\*augment*" rmdir /s /q "%%i"
)

# 7. Restart VS Code - Fresh trial starts
```

---

## ✅ **SUMMARY**

**TRIAL SYSTEM COMPLEXITY**: High (Multi-layered protection)
**PRIMARY WEAKNESS**: File-based storage (easily bypassed)
**RESET DETECTION**: Advanced (but bypassable)
**MACHINE TRACKING**: VS Code dependent (resetable)
**BYPASS DIFFICULTY**: Low (with proper tools)

**CONCLUSION**: Despite sophisticated design, Augment's trial system can be completely bypassed by removing the right files and resetting machine identification. Our cleanup tools automate this entire process.
