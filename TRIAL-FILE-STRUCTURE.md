# 📄 AUGMENT TRIAL FILE STRUCTURE ANALYSIS

## 🎯 **TRIAL FILE BREAKDOWN**

### **Primary Trial File: `augment-trial.json`**
**Location**: `%APPDATA%\Local\Code\User\augment-trial.json`

```json
{
  "trialId": "trial_abc123def456789",
  "userId": "user_xyz789abc123def",
  "startDate": "2024-01-15T10:30:00.000Z",
  "endDate": "2024-01-29T10:30:00.000Z",
  "createdAt": "2024-01-15T10:30:00.000Z",
  "lastChecked": "2024-01-18T14:22:15.123Z",
  "lastUsed": "2024-01-18T14:20:30.456Z",
  "daysRemaining": 11,
  "status": "active",
  "version": "0.484.0",
  "machineId": "a1b2c3d4e5f6g7h8i9j0k1l2m3n4",
  "fingerprint": "sha256:abcd1234efgh5678ijkl9012mnop3456",
  "usageStats": {
    "totalRequests": 47,
    "maxRequests": 100,
    "remainingRequests": 53,
    "dailyUsage": [
      { "date": "2024-01-15", "requests": 12 },
      { "date": "2024-01-16", "requests": 18 },
      { "date": "2024-01-17", "requests": 9 },
      { "date": "2024-01-18", "requests": 8 }
    ]
  },
  "features": {
    "codeCompletion": true,
    "chatAssistant": true,
    "codeExplanation": true,
    "debugging": false,
    "refactoring": false
  },
  "limits": {
    "dailyRequestLimit": 25,
    "totalRequestLimit": 100,
    "sessionTimeLimit": 3600000,
    "concurrentSessions": 1
  },
  "tracking": {
    "installationId": "install_def456ghi789jkl",
    "sessionCount": 15,
    "totalSessionTime": 45600000,
    "averageSessionTime": 3040000,
    "lastSessionDuration": 1800000
  },
  "validation": {
    "checksum": "md5:1a2b3c4d5e6f7g8h9i0j1k2l3m4n5o6p",
    "signature": "rsa:base64encodedSignature...",
    "lastValidated": "2024-01-18T14:22:15.123Z"
  }
}
```

---

## 🔐 **LICENSE FILE: `augment-license.json`**
**Location**: `%APPDATA%\Local\Code\User\augment-license.json`

```json
{
  "licenseKey": "AUGMENT-TRIAL-2024-ABC123DEF456",
  "licenseType": "trial",
  "isValid": true,
  "activatedAt": "2024-01-15T10:30:00.000Z",
  "expiresAt": "2024-01-29T10:30:00.000Z",
  "userInfo": {
    "email": "<EMAIL>",
    "userId": "user_xyz789abc123def",
    "accountType": "trial",
    "registrationDate": "2024-01-15T10:30:00.000Z"
  },
  "machineBinding": {
    "machineId": "a1b2c3d4e5f6g7h8i9j0k1l2m3n4",
    "fingerprint": "sha256:abcd1234efgh5678ijkl9012mnop3456",
    "bindingDate": "2024-01-15T10:30:00.000Z",
    "allowedMachines": 1
  },
  "features": {
    "maxRequestsPerDay": 25,
    "maxTotalRequests": 100,
    "advancedFeatures": false,
    "prioritySupport": false,
    "offlineMode": false
  },
  "validation": {
    "serverValidated": true,
    "lastServerCheck": "2024-01-18T14:22:15.123Z",
    "nextServerCheck": "2024-01-19T14:22:15.123Z",
    "validationHash": "sha256:validation_hash_here"
  }
}
```

---

## 🗂️ **GLOBAL STORAGE STRUCTURE**
**Location**: `%APPDATA%\Code\User\globalStorage\augment.vscode-augment\`

### **Main State File: `state.json`**
```json
{
  "extensionVersion": "0.484.0",
  "userId": "user_xyz789abc123def",
  "machineId": "a1b2c3d4e5f6g7h8i9j0k1l2m3n4",
  "installationId": "install_def456ghi789jkl",
  "firstInstallDate": "2024-01-15T10:30:00.000Z",
  "lastActiveDate": "2024-01-18T14:22:15.123Z",
  "settings": {
    "autoComplete": true,
    "chatEnabled": true,
    "telemetryEnabled": true,
    "updateChannel": "stable"
  },
  "usage": {
    "totalSessions": 15,
    "totalRequests": 47,
    "totalTime": 45600000,
    "lastSession": "2024-01-18T14:22:15.123Z"
  }
}
```

### **Usage Statistics: `usageStats.json`**
```json
{
  "daily": {
    "2024-01-15": { "requests": 12, "time": 7200000, "sessions": 3 },
    "2024-01-16": { "requests": 18, "time": 10800000, "sessions": 4 },
    "2024-01-17": { "requests": 9, "time": 5400000, "sessions": 2 },
    "2024-01-18": { "requests": 8, "time": 3600000, "sessions": 2 }
  },
  "weekly": {
    "2024-W03": { "requests": 47, "time": 27000000, "sessions": 11 }
  },
  "monthly": {
    "2024-01": { "requests": 47, "time": 27000000, "sessions": 11 }
  },
  "features": {
    "codeCompletion": { "used": 35, "successful": 32 },
    "chatAssistant": { "used": 12, "successful": 11 },
    "codeExplanation": { "used": 8, "successful": 7 }
  }
}
```

---

## 🔄 **BACKUP FILE STRUCTURE**
**Location**: `%TEMP%\TrialResetBackup_[randomId]\`

### **Backup Directory Contents:**
```
TrialResetBackup_7b1c76cd/
├── timestamp.txt                    # Backup creation time
├── augment-trial.json.backup        # Trial file backup
├── augment-license.json.backup      # License file backup
├── globalStorage/
│   └── augment.vscode-augment/
│       ├── state.json.backup        # State backup
│       └── usageStats.json.backup   # Usage backup
└── metadata.json                    # Backup metadata
```

### **Backup Metadata: `metadata.json`**
```json
{
  "backupId": "backup_7b1c76cd",
  "createdAt": "2024-01-15T10:30:00.000Z",
  "reason": "trial_initialization",
  "extensionVersion": "0.484.0",
  "machineId": "a1b2c3d4e5f6g7h8i9j0k1l2m3n4",
  "files": [
    {
      "original": "augment-trial.json",
      "backup": "augment-trial.json.backup",
      "size": 1024,
      "checksum": "md5:abc123def456"
    },
    {
      "original": "augment-license.json", 
      "backup": "augment-license.json.backup",
      "size": 512,
      "checksum": "md5:def456ghi789"
    }
  ]
}
```

---

## 🕐 **TIMESTAMP VALIDATION**

### **How Timestamps Are Used:**

#### **1. Trial Period Validation:**
```javascript
function validateTrialPeriod(trialData) {
    const now = new Date();
    const startDate = new Date(trialData.startDate);
    const endDate = new Date(trialData.endDate);
    
    // Check if trial period is valid
    if (now < startDate) {
        return { valid: false, reason: 'trial_not_started' };
    }
    
    if (now > endDate) {
        return { valid: false, reason: 'trial_expired' };
    }
    
    // Calculate remaining time
    const remaining = endDate.getTime() - now.getTime();
    const daysRemaining = Math.ceil(remaining / (1000 * 60 * 60 * 24));
    
    return { valid: true, daysRemaining: daysRemaining };
}
```

#### **2. Reset Detection via Timestamps:**
```javascript
function detectTrialReset(trialData, backupData) {
    const trialCreated = new Date(trialData.createdAt);
    const backupCreated = new Date(backupData.createdAt);
    
    // If trial file is newer than backup by more than 1 minute
    if (trialCreated.getTime() > backupCreated.getTime() + 60000) {
        return { 
            resetDetected: true, 
            reason: 'trial_file_recreated',
            timeDifference: trialCreated.getTime() - backupCreated.getTime()
        };
    }
    
    return { resetDetected: false };
}
```

---

## 🔢 **USAGE COUNTING MECHANISM**

### **Request Counting:**
```javascript
// Every AI request increments counters
function trackRequest(requestType, success) {
    const trialData = loadTrialData();
    const today = new Date().toISOString().split('T')[0];
    
    // Update total usage
    trialData.usageStats.totalRequests++;
    trialData.usageStats.remainingRequests--;
    
    // Update daily usage
    const dailyEntry = trialData.usageStats.dailyUsage.find(d => d.date === today);
    if (dailyEntry) {
        dailyEntry.requests++;
    } else {
        trialData.usageStats.dailyUsage.push({
            date: today,
            requests: 1
        });
    }
    
    // Update feature-specific usage
    if (success) {
        trialData.features[requestType].successful++;
    }
    trialData.features[requestType].used++;
    
    // Save updated data
    saveTrialData(trialData);
    updateGlobalUsageStats(trialData);
}
```

---

## 🎯 **BYPASS VULNERABILITIES**

### **Critical Weaknesses:**

#### **1. File Deletion Bypass:**
```bash
# Simply delete trial files
del "%APPDATA%\Local\Code\User\augment-trial.json"
del "%APPDATA%\Local\Code\User\augment-license.json"
# Extension treats this as "first time user"
```

#### **2. Timestamp Manipulation:**
```bash
# Delete backup files to prevent reset detection
for /d %%i in ("%TEMP%\TrialResetBackup_*") do rmdir /s /q "%%i"
```

#### **3. Machine ID Reset:**
```powershell
# Change machine identification
$storage = Get-Content "$env:APPDATA\Code\User\globalStorage\storage.json" | ConvertFrom-Json
$storage."telemetry.machineId" = [System.Guid]::NewGuid().ToString("N")
$storage | ConvertTo-Json | Set-Content "$env:APPDATA\Code\User\globalStorage\storage.json"
```

#### **4. Usage Counter Reset:**
```bash
# Clear usage tracking
rmdir /s /q "%APPDATA%\Code\User\globalStorage\augment.vscode-augment"
```

---

## ✅ **COMPLETE BYPASS PROCESS**

### **Automated Reset Script:**
```batch
@echo off
echo Resetting Augment trial...

REM Stop VS Code
taskkill /f /im "Code.exe" 2>nul

REM Remove trial files
del "%APPDATA%\Local\Code\User\augment-trial.json" 2>nul
del "%APPDATA%\Local\Code\User\augment-license.json" 2>nul

REM Remove global storage
rmdir /s /q "%APPDATA%\Code\User\globalStorage\augment.vscode-augment" 2>nul

REM Remove backup detection
for /d %%i in ("%TEMP%\TrialResetBackup_*") do rmdir /s /q "%%i" 2>nul

REM Reset machine ID
powershell -Command "$s=Get-Content '$env:APPDATA\Code\User\globalStorage\storage.json'|ConvertFrom-Json;$s.'telemetry.machineId'=[System.Guid]::NewGuid().ToString('N');$s|ConvertTo-Json|Set-Content '$env:APPDATA\Code\User\globalStorage\storage.json'" 2>nul

echo Trial reset complete. Restart VS Code for fresh trial.
pause
```

---

## 🏆 **SUMMARY**

**TRIAL SYSTEM COMPLEXITY**: Very High (Multi-file, multi-timestamp validation)
**SECURITY LAYERS**: 4 (File existence, timestamps, usage counting, machine ID)
**PRIMARY WEAKNESS**: File-based storage (easily deleted)
**BYPASS DIFFICULTY**: Low (with proper file removal)
**RESET DETECTION**: Advanced but bypassable

**CONCLUSION**: Despite sophisticated multi-layered protection, the trial system can be completely bypassed by removing the correct files and resetting machine identification. The file-based approach is the system's fundamental weakness.
