@echo off
echo === SEARCHING FOR AUGMENT EXTENSION ===
echo.

echo Searching in common VS Code extension locations...
echo.

set FOUND=0

echo 1. Checking %USERPROFILE%\.vscode\extensions\
if exist "%USERPROFILE%\.vscode\extensions\" (
    for /d %%i in ("%USERPROFILE%\.vscode\extensions\*augment*") do (
        echo   FOUND: %%i
        set FOUND=1
        
        echo   Checking for key files...
        if exist "%%i\package.json" echo     - package.json: YES
        if exist "%%i\out\extension.js" echo     - out\extension.js: YES
        if exist "%%i\extension.js" echo     - extension.js: YES
        if exist "%%i\dist\extension.js" echo     - dist\extension.js: YES
        echo.
    )
) else (
    echo   Directory not found
)

echo 2. Checking %APPDATA%\Code\User\extensions\
if exist "%APPDATA%\Code\User\extensions\" (
    for /d %%i in ("%APPDATA%\Code\User\extensions\*augment*") do (
        echo   FOUND: %%i
        set FOUND=1
    )
) else (
    echo   Directory not found
)

echo 3. Checking cached extensions...
if exist "%APPDATA%\Code\CachedExtensionVSIXs\" (
    for %%i in ("%APPDATA%\Code\CachedExtensionVSIXs\*augment*") do (
        echo   FOUND CACHE: %%i
        set FOUND=1
    )
) else (
    echo   Cache directory not found
)

echo 4. Searching entire user directory (this may take a moment)...
for /f "delims=" %%i in ('dir /s /b "%USERPROFILE%\*augment*" 2^>nul ^| findstr /i "vscode\|code"') do (
    echo   FOUND: %%i
    set FOUND=1
)

echo.
if %FOUND%==1 (
    echo SUCCESS: Augment extension files found!
    echo You can now manually copy the extension.js file for analysis.
) else (
    echo WARNING: No Augment extension files found.
    echo The extension may not be installed or may be in a different location.
)

echo.
echo === SEARCH COMPLETE ===
pause
